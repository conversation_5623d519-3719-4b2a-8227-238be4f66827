<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Cache - Zurich Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background: #3b82f6;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #2563eb;
        }
        .success {
            color: #059669;
            background: #d1fae5;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            color: #dc2626;
            background: #fee2e2;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Clear Browser Cache</h1>
        <p>If you're experiencing issues with the Zurich Claims Dashboard, try clearing your browser cache:</p>
        
        <h3>Method 1: Hard Refresh (Recommended)</h3>
        <p>Press <strong>Ctrl+F5</strong> (Windows/Linux) or <strong>Cmd+Shift+R</strong> (Mac) while on the dashboard page.</p>
        
        <h3>Method 2: Clear Cache and Reload</h3>
        <button class="button" onclick="clearCacheAndReload()">Clear Cache & Reload Dashboard</button>
        
        <h3>Method 3: Manual Steps</h3>
        <ol>
            <li>Open Developer Tools (F12)</li>
            <li>Right-click the refresh button</li>
            <li>Select "Empty Cache and Hard Reload"</li>
        </ol>
        
        <div id="status"></div>
        
        <hr style="margin: 30px 0;">
        
        <h3>Quick Links</h3>
        <button class="button" onclick="window.open('http://localhost:3000', '_blank')">Open Dashboard</button>
        <button class="button" onclick="window.open('http://localhost:8000/docs', '_blank')">API Documentation</button>
    </div>

    <script>
        function clearCacheAndReload() {
            const status = document.getElementById('status');
            status.innerHTML = '<div class="success">🔄 Clearing cache and reloading...</div>';
            
            // Clear localStorage and sessionStorage
            localStorage.clear();
            sessionStorage.clear();
            
            // Clear cache using service worker if available
            if ('caches' in window) {
                caches.keys().then(function(names) {
                    for (let name of names) {
                        caches.delete(name);
                    }
                });
            }
            
            // Force reload the dashboard
            setTimeout(() => {
                window.open('http://localhost:3000', '_blank');
                status.innerHTML = '<div class="success">✅ Cache cleared! Dashboard opened in new tab.</div>';
            }, 1000);
        }
        
        // Check if services are running
        async function checkServices() {
            const status = document.getElementById('status');
            try {
                const frontendResponse = await fetch('http://localhost:3000/health');
                const backendResponse = await fetch('http://localhost:8000/health');
                
                if (frontendResponse.ok && backendResponse.ok) {
                    status.innerHTML = '<div class="success">✅ All services are running properly!</div>';
                } else {
                    status.innerHTML = '<div class="error">⚠️ Some services may not be responding. Try restarting the containers.</div>';
                }
            } catch (error) {
                status.innerHTML = '<div class="error">❌ Cannot connect to services. Make sure Docker containers are running.</div>';
            }
        }
        
        // Check services on page load
        checkServices();
    </script>
</body>
</html> 