# 🚀 ZURICH EMAIL CLASSIFICATION API - SETUP & USAGE GUIDE

## 📋 **OVERVIEW**

This API provides AI-powered email classification for Zurich Insurance claims processing using BAML (Basically, A Made-Up Language) with OpenAI GPT-4o and GPT-4o-mini models.

**Key Features:**
- ✅ **Claims Detection**: Identifies insurance claim-related emails
- ✅ **Attachment Validation**: Detects missing attachments scenarios  
- ✅ **Canadian Legal Context**: Provincial jurisdiction awareness
- ✅ **Risk Assessment**: Multi-level risk analysis
- ✅ **N8N Integration**: Ready for workflow automation
- ✅ **Fallback System**: GPT-4o with GPT-4o-mini backup

---

## 🔧 **SETUP INSTRUCTIONS**

### **Step 1: Install Dependencies**
```bash
# Install Python dependencies
pip install -r requirements_email_classification.txt

# Or using poetry (recommended)
poetry install
```

### **Step 2: Setup BAML Client**
```bash
# Generate BAML client from schema
poetry run baml-cli generate

# Or if using pip
baml-cli generate
```

### **Step 3: Configure Environment Variables**
Create `.env` file:
```bash
# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Optional: Alternative API endpoints
OPENAI_BASE_URL=https://api.openai.com/v1

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=true
```

### **Step 4: Start the API Server**
```bash
# Development mode
python backend/src/api/zurich_email_classification_api.py

# Or using uvicorn directly
uvicorn zurich_email_classification_api:app --host 0.0.0.0 --port 8000 --reload

# Production mode
gunicorn -w 4 -k uvicorn.workers.UvicornWorker zurich_email_classification_api:app
```

---

## 🧪 **TESTING**

### **Quick Health Check**
```bash
# Test API availability
python backend/tests/test_zurich_email_classification_api.py health

# Test API info
python backend/tests/test_zurich_email_classification_api.py info
```

### **Individual Test Scenarios**
```bash
# Test auto claim classification
python backend/tests/test_zurich_email_classification_api.py auto

# Test missing attachments scenario
python backend/tests/test_zurich_email_classification_api.py attachments

# Test marketing email (should be ignored)
python backend/tests/test_zurich_email_classification_api.py marketing

# Test fraud detection
python backend/tests/test_zurich_email_classification_api.py fraud

# Test Quebec civil law considerations
python backend/tests/test_zurich_email_classification_api.py quebec
```

### **Comprehensive Test Suite**
```bash
# Run all tests
python backend/tests/test_zurich_email_classification_api.py

# Using pytest
pytest backend/tests/test_zurich_email_classification_api.py -v
```

---

## 🔌 **N8N INTEGRATION**

### **N8N HTTP Request Node Configuration**

#### **Basic Classification Request**
```json
{
  "method": "POST",
  "url": "{{$env.ZURICH_API_URL}}/api/classify-email/simple",
  "headers": {
    "Content-Type": "application/json"
  },
  "body": {
    "subject": "{{$json.subject}}",
    "body": "{{$json.body}}",
    "senderEmail": "{{$json.from}}",
    "senderName": "{{$json.fromName}}",
    "attachments": "{{$json.attachments}}"
  }
}
```

#### **N8N Workflow Routing Based on Response**
```javascript
// N8N Function Node for workflow routing
const response = $json;

// Route based on workflow action
switch(response.workflowAction) {
  case 'PROCEED_TO_ZENDESK':
    return [{ json: { ...response, route: 'create_ticket' } }];
    
  case 'IGNORE_EMAIL':
    return [{ json: { ...response, route: 'ignore' } }];
    
  case 'REQUEST_ATTACHMENTS':
    return [{ json: { ...response, route: 'request_docs' } }];
    
  case 'HUMAN_REVIEW_REQUIRED':
    return [{ json: { ...response, route: 'human_review' } }];
    
  default:
    return [{ json: { ...response, route: 'error' } }];
}
```

---

## 📊 **API ENDPOINTS**

### **1. Main Classification Endpoint**
```http
POST /api/classify-email
Content-Type: application/json

{
  "subject": "Car Accident - Need Help",
  "body": "I was in an accident yesterday...",
  "senderEmail": "<EMAIL>",
  "senderName": "John Doe",
  "attachments": [
    {
      "filename": "damage_photos.jpg",
      "contentType": "image/jpeg",
      "size": 1024000
    }
  ]
}
```

**Response:**
```json
{
  "workflowAction": "PROCEED_TO_ZENDESK",
  "isClaimRelated": true,
  "claimType": "AUTO",
  "urgencyLevel": "MEDIUM",
  "confidenceScore": 0.92,
  "canadianJurisdiction": "ONTARIO",
  "attachmentAnalysis": {
    "hasAttachments": true,
    "mentionsAttachments": true,
    "attachmentMismatch": "NO_MISMATCH"
  },
  "classificationReasoning": "Clear auto insurance claim with incident description...",
  "processingTime": 2.34,
  "modelUsed": "gpt-4o"
}
```

### **2. Simplified Endpoint for N8N**
```http
POST /api/classify-email/simple
```

**Response:**
```json
{
  "workflowAction": "PROCEED_TO_ZENDESK",
  "isClaimRelated": true,
  "claimType": "AUTO",
  "urgencyLevel": "MEDIUM",
  "confidenceScore": 0.92,
  "attachmentIssue": false,
  "requiresHumanReview": false,
  "reasoning": "Clear auto insurance claim..."
}
```

### **3. Health Check**
```http
GET /api/health
```

### **4. API Information**
```http
GET /api/info
```

---

## 🎯 **WORKFLOW ACTIONS EXPLAINED**

| Action | Description | N8N Next Step |
|--------|-------------|---------------|
| `PROCEED_TO_ZENDESK` | Valid claim, create ticket | Route to Zendesk creation |
| `IGNORE_EMAIL` | Not claim-related | Stop workflow or archive |
| `REQUEST_ATTACHMENTS` | Mentions docs but none attached | Send attachment request email |
| `HUMAN_REVIEW_REQUIRED` | High-risk or unclear | Route to human agent review |

---

## 🧪 **BAML PLAYGROUND TESTING**

### **Testing in BAML Playground**
1. Copy the BAML schema from `baml_models/zurich_email_classification.baml`
2. Uncomment the test cases at the bottom
3. Run in BAML playground to test classification logic

**Example Test Case:**
```baml
test AutoClaimTest {
  functions [ClassifyZurichEmail]
  args {
    emailData {
      subject: "Car Accident - Need to File Claim"
      body: "I was in a car accident yesterday..."
      senderEmail: "<EMAIL>"
      attachments: [
        {filename: "accident_photos.zip", contentType: "application/zip"}
      ]
    }
  }
}
```

---

## 🚨 **DEBUGGING & TROUBLESHOOTING**

### **Common Issues**

#### **1. BAML Client Import Error**
```bash
Error: ModuleNotFoundError: No module named 'baml_client'
```
**Solution:**
```bash
# Generate BAML client
poetry run baml-cli generate
# OR
baml-cli generate
```

#### **2. OpenAI API Key Error**
```bash
Error: OpenAI API key not configured
```
**Solution:**
```bash
# Set environment variable
export OPENAI_API_KEY=your_key_here
# OR add to .env file
echo "OPENAI_API_KEY=your_key_here" >> .env
```

#### **3. Model Fallback Trigger**
When GPT-4o fails, the API automatically falls back to GPT-4o-mini. Check logs:
```bash
# Look for fallback messages in logs
tail -f zurich_email_classification.log | grep "fallback"
```

### **Debug Specific Email**
```python
# Use the debug function in test file
from backend.tests.test_zurich_email_classification_api import debug_specific_email

debug_specific_email(
    subject="Test Email",
    body="Test email content",
    sender_email="<EMAIL>"
)
```

### **API Logs**
```bash
# View detailed logs
tail -f zurich_email_classification.log

# Filter for specific request
grep "req_1234567890" zurich_email_classification.log
```

---

## 📈 **PERFORMANCE OPTIMIZATION**

### **Production Deployment**
```bash
# Use multiple workers
gunicorn -w 4 -k uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8000 \
  --timeout 30 \
  --keep-alive 5 \
  zurich_email_classification_api:app
```

### **Monitoring**
```bash
# Health check endpoint for monitoring
curl http://localhost:8000/api/health

# API metrics
curl http://localhost:8000/api/info
```

---

## 🔐 **SECURITY CONSIDERATIONS**

### **Production Security**
```python
# Update CORS settings in production
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://your-n8n-domain.com"],  # Specific domains only
    allow_credentials=True,
    allow_methods=["POST", "GET"],
    allow_headers=["Content-Type", "Authorization"],
)
```

### **API Rate Limiting**
Consider adding rate limiting for production:
```bash
pip install slowapi
```

---

## 📚 **INTEGRATION EXAMPLES**

### **Curl Example**
```bash
curl -X POST "http://localhost:8000/api/classify-email/simple" \
  -H "Content-Type: application/json" \
  -d '{
    "subject": "Car Accident Claim",
    "body": "I need to file a claim for my car accident",
    "senderEmail": "<EMAIL>",
    "attachments": []
  }'
```

### **Python Client Example**
```python
import requests

response = requests.post(
    "http://localhost:8000/api/classify-email/simple",
    json={
        "subject": "Property Damage",
        "body": "My house was damaged in a storm",
        "senderEmail": "<EMAIL>",
        "attachments": []
    }
)

result = response.json()
print(f"Action: {result['workflowAction']}")
print(f"Is Claim: {result['isClaimRelated']}")
```

---

## 🎯 **SUCCESS METRICS**

### **Expected Performance**
- **Response Time**: < 3 seconds per classification
- **Accuracy**: > 95% for clear claims/non-claims
- **Fallback Success**: > 99% (GPT-4o-mini backup)
- **Attachment Detection**: > 98% accuracy

### **Monitoring Endpoints**
- Health: `GET /api/health`
- Info: `GET /api/info`
- Logs: `zurich_email_classification.log`

---

## 🆘 **SUPPORT**

### **Common Commands**
```bash
# Start API
python backend/src/api/zurich_email_classification_api.py

# Run tests
python backend/tests/test_zurich_email_classification_api.py

# Check BAML schema
cat baml_models/zurich_email_classification.baml

# View logs
tail -f zurich_email_classification.log
```

### **File Structure**
```
├── baml_models/
│   └── zurich_email_classification.baml     # BAML schema & functions
├── backend/
│   ├── src/api/
│   │   └── zurich_email_classification_api.py  # Main API
│   └── tests/
│       └── test_zurich_email_classification_api.py  # Test suite
├── requirements_email_classification.txt    # Dependencies
└── ZURICH_EMAIL_CLASSIFICATION_SETUP.md    # This guide
```

---

**🎉 Ready to classify emails for Zurich Insurance claims processing!** 