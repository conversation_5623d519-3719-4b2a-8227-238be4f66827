// Claims Review Application JavaScript
class ClaimsReviewApp {
    constructor() {
        this.zoomLevel = 100;
        this.currentHighlight = null;
        this.mobileViewMode = 'document'; // 'document' or 'analysis'
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupThemeToggle();
        this.setupMobileView();
        this.initializeHighlights();
        this.setupFieldInteractions();
        this.setupActionButtons();
    }

    setupEventListeners() {
        // Zoom controls
        document.getElementById('zoom-in').addEventListener('click', () => this.zoomIn());
        document.getElementById('zoom-out').addEventListener('click', () => this.zoomOut());
        
        // Theme toggle
        document.getElementById('theme-toggle').addEventListener('click', () => this.toggleTheme());
        
        // Mobile toggle
        document.getElementById('mobile-toggle').addEventListener('click', () => this.toggleMobileView());
        
        // Field info buttons
        document.querySelectorAll('.field-info').forEach(button => {
            button.addEventListener('click', (e) => this.handleFieldInfoClick(e));
        });
        
        // Document highlights
        document.querySelectorAll('.highlight').forEach(highlight => {
            highlight.addEventListener('click', (e) => this.handleHighlightClick(e));
        });
    }

    setupThemeToggle() {
        const themeToggle = document.getElementById('theme-toggle');
        const themeIcon = themeToggle.querySelector('.theme-icon');
        
        // Check for saved theme preference
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-color-scheme', savedTheme);
        themeIcon.textContent = savedTheme === 'dark' ? '☀️' : '🌙';
    }

    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-color-scheme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        
        document.documentElement.setAttribute('data-color-scheme', newTheme);
        localStorage.setItem('theme', newTheme);
        
        const themeIcon = document.querySelector('.theme-icon');
        themeIcon.textContent = newTheme === 'dark' ? '☀️' : '🌙';
    }

    setupMobileView() {
        const mobileToggle = document.getElementById('mobile-toggle');
        const toggleText = mobileToggle.querySelector('.toggle-text');
        
        // Update mobile view based on screen size
        this.updateMobileView();
        window.addEventListener('resize', () => this.updateMobileView());
    }

    updateMobileView() {
        const isMobile = window.innerWidth <= 1024;
        const documentViewer = document.querySelector('.document-viewer');
        const analysisPanel = document.querySelector('.analysis-panel');
        
        if (isMobile) {
            if (this.mobileViewMode === 'document') {
                documentViewer.style.display = 'block';
                analysisPanel.style.display = 'none';
                document.getElementById('mobile-toggle').querySelector('.toggle-text').textContent = 'Show Analysis';
            } else {
                documentViewer.style.display = 'none';
                analysisPanel.style.display = 'block';
                document.getElementById('mobile-toggle').querySelector('.toggle-text').textContent = 'Show Document';
            }
        } else {
            documentViewer.style.display = 'block';
            analysisPanel.style.display = 'block';
        }
    }

    toggleMobileView() {
        this.mobileViewMode = this.mobileViewMode === 'document' ? 'analysis' : 'document';
        this.updateMobileView();
    }

    zoomIn() {
        if (this.zoomLevel < 200) {
            this.zoomLevel += 25;
            this.updateZoom();
        }
    }

    zoomOut() {
        if (this.zoomLevel > 50) {
            this.zoomLevel -= 25;
            this.updateZoom();
        }
    }

    updateZoom() {
        const documentContent = document.getElementById('document-content');
        const zoomLevelElement = document.querySelector('.zoom-level');
        
        documentContent.style.transform = `scale(${this.zoomLevel / 100})`;
        documentContent.style.transformOrigin = 'top left';
        zoomLevelElement.textContent = `${this.zoomLevel}%`;
    }

    initializeHighlights() {
        // Initialize highlight colors based on field types
        const fieldColors = {
            'incident_date': '#4CAF50',
            'claimant_name': '#2196F3',
            'location': '#FF9800',
            'incident_type': '#9C27B0'
        };

        document.querySelectorAll('.highlight').forEach(highlight => {
            const field = highlight.getAttribute('data-field');
            if (fieldColors[field]) {
                highlight.style.backgroundColor = fieldColors[field] + '33'; // 20% opacity
                highlight.style.borderLeft = `3px solid ${fieldColors[field]}`;
            }
        });
    }

    setupFieldInteractions() {
        // Add click handlers for field items to highlight corresponding document text
        document.querySelectorAll('.field-item').forEach(fieldItem => {
            fieldItem.addEventListener('click', (e) => {
                if (e.target.classList.contains('field-info')) return;
                
                const fieldName = fieldItem.getAttribute('data-field');
                this.highlightDocumentField(fieldName);
            });
        });
    }

    handleFieldInfoClick(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const fieldItem = e.target.closest('.field-item');
        const fieldName = fieldItem.getAttribute('data-field');
        
        this.highlightDocumentField(fieldName);
        this.showFieldTooltip(e.target, fieldName);
    }

    handleHighlightClick(e) {
        const fieldName = e.target.getAttribute('data-field');
        this.highlightAnalysisField(fieldName);
    }

    highlightDocumentField(fieldName) {
        // Clear previous highlights
        document.querySelectorAll('.highlight.active').forEach(highlight => {
            highlight.classList.remove('active');
        });
        
        // Highlight the corresponding document text
        const documentHighlight = document.querySelector(`.highlight[data-field="${fieldName}"]`);
        if (documentHighlight) {
            documentHighlight.classList.add('active');
            documentHighlight.scrollIntoView({ behavior: 'smooth', block: 'center' });
            
            // Switch to document view on mobile
            if (window.innerWidth <= 1024 && this.mobileViewMode !== 'document') {
                this.mobileViewMode = 'document';
                this.updateMobileView();
            }
        }
    }

    highlightAnalysisField(fieldName) {
        // Clear previous highlights
        document.querySelectorAll('.field-item.active').forEach(item => {
            item.classList.remove('active');
        });
        
        // Highlight the corresponding analysis field
        const analysisField = document.querySelector(`.field-item[data-field="${fieldName}"]`);
        if (analysisField) {
            analysisField.classList.add('active');
            analysisField.scrollIntoView({ behavior: 'smooth', block: 'center' });
            
            // Switch to analysis view on mobile
            if (window.innerWidth <= 1024 && this.mobileViewMode !== 'analysis') {
                this.mobileViewMode = 'analysis';
                this.updateMobileView();
            }
        }
    }

    showFieldTooltip(element, fieldName) {
        // Remove existing tooltips
        document.querySelectorAll('.field-tooltip').forEach(tooltip => {
            tooltip.remove();
        });
        
        // Create tooltip with field source information
        const tooltip = document.createElement('div');
        tooltip.className = 'field-tooltip';
        tooltip.style.cssText = `
            position: absolute;
            background: var(--color-surface);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-base);
            padding: var(--space-12);
            font-size: var(--font-size-sm);
            color: var(--color-text);
            z-index: 1000;
            max-width: 300px;
            box-shadow: var(--shadow-lg);
            pointer-events: none;
        `;
        
        // Get field source information
        const fieldInfo = this.getFieldSourceInfo(fieldName);
        tooltip.innerHTML = `
            <strong>Source:</strong><br>
            ${fieldInfo.source}<br>
            <strong>Confidence:</strong> ${Math.round(fieldInfo.confidence * 100)}%
        `;
        
        document.body.appendChild(tooltip);
        
        // Position tooltip
        const rect = element.getBoundingClientRect();
        tooltip.style.top = `${rect.bottom + window.scrollY + 5}px`;
        tooltip.style.left = `${rect.left + window.scrollX}px`;
        
        // Remove tooltip after 3 seconds
        setTimeout(() => {
            tooltip.remove();
        }, 3000);
    }

    getFieldSourceInfo(fieldName) {
        // Mock data for field source information
        const fieldSources = {
            'incident_date': {
                source: 'Page 1, line 15: "On Thursday August 27, 2020, at approximately 3:30 PM"',
                confidence: 0.95
            },
            'claimant_name': {
                source: 'Page 1, header: "Claimant: Jacques Eadbyrt"',
                confidence: 0.98
            },
            'location': {
                source: 'Page 1, line 8: "Franco\'s No Frills grocery store located at 754 Riverside Road"',
                confidence: 0.92
            },
            'incident_type': {
                source: 'Page 1, line 20: "slipped on water while walking toward checkout"',
                confidence: 0.89
            }
        };
        
        return fieldSources[fieldName] || { source: 'Source not available', confidence: 0 };
    }

    setupActionButtons() {
        document.getElementById('approve-btn').addEventListener('click', () => this.handleApprove());
        document.getElementById('escalate-btn').addEventListener('click', () => this.handleEscalate());
        document.getElementById('export-btn').addEventListener('click', () => this.handleExport());
    }

    handleApprove() {
        // Show confirmation dialog
        const confirmed = confirm('Are you sure you want to approve this settlement for $12,395?');
        if (confirmed) {
            this.showNotification('Settlement approved successfully!', 'success');
            this.updateClaimStatus('Approved');
        }
    }

    handleEscalate() {
        // Show escalation dialog
        const reason = prompt('Please provide a reason for escalation:');
        if (reason) {
            this.showNotification('Claim escalated for senior review', 'info');
            this.updateClaimStatus('Escalated');
        }
    }

    handleExport() {
        // Generate and download report
        this.generateReport();
        this.showNotification('Report exported successfully!', 'success');
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification--${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-surface);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-base);
            padding: var(--space-16);
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            max-width: 300px;
            animation: slideIn 0.3s ease-out;
        `;
        
        if (type === 'success') {
            notification.style.borderLeftColor = 'var(--color-success)';
            notification.style.borderLeftWidth = '4px';
        } else if (type === 'error') {
            notification.style.borderLeftColor = 'var(--color-error)';
            notification.style.borderLeftWidth = '4px';
        } else if (type === 'info') {
            notification.style.borderLeftColor = 'var(--color-info)';
            notification.style.borderLeftWidth = '4px';
        }
        
        notification.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; font-size: 18px; cursor: pointer;">×</button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Remove notification after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    updateClaimStatus(status) {
        const statusElement = document.querySelector('.status');
        statusElement.textContent = status;
        statusElement.className = `status status--${status.toLowerCase() === 'approved' ? 'success' : 'warning'}`;
    }

    generateReport() {
        // Mock report generation
        const reportData = {
            claim_reference: 'CLM-48935489',
            timestamp: new Date().toISOString(),
            status: document.querySelector('.status').textContent,
            reviewer: 'AI System',
            net_recoverable: '$12,395',
            confidence: '85%'
        };
        
        const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `claim-report-${reportData.claim_reference}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .field-item.active {
        background: rgba(var(--color-primary-rgb, 33, 128, 141), 0.1);
        border-color: var(--color-primary);
        transform: scale(1.02);
    }
    
    .field-item {
        transition: all var(--duration-normal) var(--ease-standard);
    }
    
    .notification {
        animation: slideIn 0.3s ease-out;
    }
`;
document.head.appendChild(style);

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ClaimsReviewApp();
});

// Add keyboard shortcuts
document.addEventListener('keydown', (e) => {
    if (e.altKey) {
        switch (e.key) {
            case 't':
                e.preventDefault();
                document.getElementById('theme-toggle').click();
                break;
            case 'a':
                e.preventDefault();
                document.getElementById('approve-btn').click();
                break;
            case 'e':
                e.preventDefault();
                document.getElementById('escalate-btn').click();
                break;
            case 'r':
                e.preventDefault();
                document.getElementById('export-btn').click();
                break;
        }
    }
});

// Add print functionality
window.addEventListener('beforeprint', () => {
    document.body.classList.add('printing');
});

window.addEventListener('afterprint', () => {
    document.body.classList.remove('printing');
});