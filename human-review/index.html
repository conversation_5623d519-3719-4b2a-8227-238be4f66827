<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Claims Document Review | AI-Powered Analysis</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container flex justify-between items-center">
            <div class="flex items-center gap-16">
                <h1 class="header__title">Claims Review</h1>
                <div class="claim-info">
                    <span class="claim-reference">CLM-48935489</span>
                    <span class="status status--info">Under Review</span>
                </div>
            </div>
            <div class="flex items-center gap-8">
                <button class="btn btn--outline btn--sm" id="theme-toggle">
                    <span class="theme-icon">🌙</span>
                </button>
                <div class="confidence-indicator">
                    <span class="confidence-label">Overall Confidence</span>
                    <div class="confidence-bar">
                        <div class="confidence-fill" style="width: 85%"></div>
                        <span class="confidence-value">85%</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <div class="review-layout">
                <!-- Document Viewer Section -->
                <section class="document-viewer">
                    <div class="viewer-controls">
                        <button class="btn btn--outline btn--sm" id="zoom-out">-</button>
                        <span class="zoom-level">100%</span>
                        <button class="btn btn--outline btn--sm" id="zoom-in">+</button>
                        <div class="page-nav">
                            <span>Page 1 of 1</span>
                        </div>
                    </div>
                    
                    <div class="document-content" id="document-content">
                        <div class="document-page">
                            <div class="document-header">
                                <h2>Motor Vehicle Accident Claim Report</h2>
                                <p><strong>Claimant:</strong> <span class="highlight" data-field="claimant_name">Jacques Eadbyrt</span></p>
                                <p><strong>Claim Reference:</strong> CLM-48935489</p>
                            </div>
                            
                            <div class="document-body">
                                <p>This report documents an incident that occurred at <span class="highlight" data-field="location">Franco's No Frills grocery store located at 754 Riverside Road, Broadway, YT</span>.</p>
                                
                                <p>On <span class="highlight" data-field="incident_date">Thursday August 27, 2020</span>, at approximately 3:30 PM, the claimant was shopping for groceries when they <span class="highlight" data-field="incident_type">slipped on water while walking toward checkout</span>.</p>
                                
                                <p>The claimant reported immediate pain and was unable to continue shopping. Emergency services were not called at the scene, but the claimant sought medical attention later that evening.</p>
                                
                                <p>Store management was notified of the incident and completed an internal incident report. No witnesses were present at the time of the incident.</p>
                                
                                <p>The claimant sustained injuries including bruising and minor soft tissue damage. Medical documentation has been provided supporting the claim for damages.</p>
                                
                                <p>This incident falls under the store's Commercial General Liability coverage with policy limits of $5,000,000 per occurrence.</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- AI Analysis Panel -->
                <section class="analysis-panel">
                    <div class="panel-header">
                        <h2>AI Analysis Results</h2>
                        <span class="analysis-timestamp">Processed: July 3, 2025</span>
                    </div>

                    <div class="analysis-levels">
                        <!-- Level 1: Extraction -->
                        <div class="analysis-level" data-level="level1">
                            <div class="level-header">
                                <h3>L1: Document Extraction & Classification</h3>
                                <div class="level-confidence">
                                    <div class="confidence-bar">
                                        <div class="confidence-fill" style="width: 88%"></div>
                                        <span class="confidence-value">88%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="level-content">
                                <div class="extracted-fields">
                                    <div class="field-item" data-field="incident_date">
                                        <span class="field-label">Incident Date</span>
                                        <span class="field-value">2020-08-27</span>
                                        <button class="field-info" title="Click to highlight source">ℹ️</button>
                                    </div>
                                    <div class="field-item" data-field="claimant_name">
                                        <span class="field-label">Claimant</span>
                                        <span class="field-value">Jacques Eadbyrt</span>
                                        <button class="field-info" title="Click to highlight source">ℹ️</button>
                                    </div>
                                    <div class="field-item" data-field="location">
                                        <span class="field-label">Location</span>
                                        <span class="field-value">Franco's No Frills, 754 Riverside Road, Broadway, YT</span>
                                        <button class="field-info" title="Click to highlight source">ℹ️</button>
                                    </div>
                                    <div class="field-item" data-field="incident_type">
                                        <span class="field-label">Incident Type</span>
                                        <span class="field-value">Slip and Fall</span>
                                        <button class="field-info" title="Click to highlight source">ℹ️</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Level 2: Coverage -->
                        <div class="analysis-level" data-level="level2">
                            <div class="level-header">
                                <h3>L2: Coverage Determination</h3>
                                <div class="level-confidence">
                                    <div class="confidence-bar">
                                        <div class="confidence-fill" style="width: 82%"></div>
                                        <span class="confidence-value">82%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="level-content">
                                <div class="coverage-decision">
                                    <div class="field-item">
                                        <span class="field-label">Coverage Decision</span>
                                        <span class="field-value status--success">COVERED</span>
                                    </div>
                                    <div class="field-item">
                                        <span class="field-label">Policy Limits</span>
                                        <span class="field-value">$5,000,000 per occurrence</span>
                                    </div>
                                    <div class="field-item">
                                        <span class="field-label">Policy Period</span>
                                        <span class="field-value">Active (2020-07-01 to 2021-07-01)</span>
                                    </div>
                                </div>
                                <div class="shap-explanation">
                                    <h4>Coverage Decision Factors</h4>
                                    <div class="feature-importance">
                                        <div class="feature-bar">
                                            <span class="feature-name">Policy Type</span>
                                            <div class="importance-bar positive">
                                                <div class="importance-fill" style="width: 45%"></div>
                                                <span class="importance-value">+0.45</span>
                                            </div>
                                        </div>
                                        <div class="feature-bar">
                                            <span class="feature-name">Incident Date</span>
                                            <div class="importance-bar positive">
                                                <div class="importance-fill" style="width: 25%"></div>
                                                <span class="importance-value">+0.25</span>
                                            </div>
                                        </div>
                                        <div class="feature-bar">
                                            <span class="feature-name">Location</span>
                                            <div class="importance-bar positive">
                                                <div class="importance-fill" style="width: 20%"></div>
                                                <span class="importance-value">+0.20</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Level 3: Fault -->
                        <div class="analysis-level" data-level="level3">
                            <div class="level-header">
                                <h3>L3: Fault Allocation</h3>
                                <div class="level-confidence">
                                    <div class="confidence-bar">
                                        <div class="confidence-fill" style="width: 78%"></div>
                                        <span class="confidence-value">78%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="level-content">
                                <div class="fault-allocation">
                                    <div class="fault-split">
                                        <div class="fault-party">
                                            <span class="party-name">Store</span>
                                            <div class="fault-percentage">50%</div>
                                            <span class="fault-reason">Failure to maintain safe premises</span>
                                        </div>
                                        <div class="fault-party">
                                            <span class="party-name">Customer</span>
                                            <div class="fault-percentage">50%</div>
                                            <span class="fault-reason">Duty of reasonable care while walking</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Level 4: Quantum -->
                        <div class="analysis-level" data-level="level4">
                            <div class="level-header">
                                <h3>L4: Loss Quantum Assessment</h3>
                                <div class="level-confidence">
                                    <div class="confidence-bar">
                                        <div class="confidence-fill" style="width: 85%"></div>
                                        <span class="confidence-value">85%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="level-content">
                                <div class="quantum-breakdown">
                                    <div class="quantum-item">
                                        <span class="quantum-label">Total Damages</span>
                                        <span class="quantum-value">$24,790</span>
                                    </div>
                                    <div class="quantum-item highlight-item">
                                        <span class="quantum-label">Net Recoverable (50% fault)</span>
                                        <span class="quantum-value">$12,395</span>
                                    </div>
                                    <div class="quantum-breakdown-details">
                                        <div class="breakdown-item">
                                            <span class="breakdown-label">Pain & Suffering</span>
                                            <span class="breakdown-value">$12,000</span>
                                        </div>
                                        <div class="breakdown-item">
                                            <span class="breakdown-label">Medical Costs</span>
                                            <span class="breakdown-value">$1,520</span>
                                        </div>
                                        <div class="breakdown-item">
                                            <span class="breakdown-label">Income Loss</span>
                                            <span class="breakdown-value">$11,270</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recommendations -->
                    <div class="recommendations">
                        <h3>AI Recommendations</h3>
                        <div class="recommendation-item primary">
                            <span class="recommendation-label">Primary:</span>
                            <span class="recommendation-text">Approve settlement up to $12,395</span>
                        </div>
                        <div class="recommendation-item alternative">
                            <span class="recommendation-label">Alternative:</span>
                            <span class="recommendation-text">Request additional evidence before settlement</span>
                        </div>
                        <div class="recommendation-item escalation">
                            <span class="recommendation-label">Escalation:</span>
                            <span class="recommendation-text">Complex fault determination requires senior review</span>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <button class="btn btn--primary" id="approve-btn">Approve Settlement</button>
                        <button class="btn btn--outline" id="escalate-btn">Escalate for Review</button>
                        <button class="btn btn--secondary" id="export-btn">Export Report</button>
                    </div>
                </section>
            </div>
        </div>
    </main>

    <!-- Mobile Toggle Button -->
    <button class="mobile-toggle" id="mobile-toggle">
        <span class="toggle-text">Show Analysis</span>
    </button>

    <script src="app.js"></script>
</body>
</html>