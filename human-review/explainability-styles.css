/* Explainable AI Styles for Claims Processing Dashboard */

/* ========================================
   Explainability Panel Base Styles
   ======================================== */

.explainability-panel {
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin: 1rem 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.explanation-header {
    background: var(--header-background);
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.explanation-header h3 {
    margin: 0 0 1rem 0;
    color: var(--text-color);
    font-size: 1.2rem;
    font-weight: 600;
}

/* ========================================
   Tab Navigation
   ======================================== */

.explanation-tabs {
    display: flex;
    gap: 0.5rem;
}

.tab-button {
    background: transparent;
    border: 1px solid var(--border-color);
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-color);
    transition: all 0.2s ease;
}

.tab-button:hover {
    background: var(--hover-background);
}

.tab-button.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* ========================================
   Tab Content Areas
   ======================================== */

.explanation-content {
    padding: 1.5rem;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* ========================================
   SHAP Visualization Styles
   ======================================== */

.shap-explanation {
    width: 100%;
}

.prediction-summary {
    background: var(--info-background);
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1.5rem;
    border-left: 4px solid var(--primary-color);
}

.prediction-summary h4 {
    margin: 0 0 0.5rem 0;
    color: var(--text-color);
    font-size: 1.1rem;
}

.prediction-summary p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.waterfall-chart {
    height: 300px;
    margin: 1.5rem 0;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 1rem;
    background: var(--chart-background);
    overflow-x: auto;
}

.waterfall-svg {
    width: 100%;
    height: 100%;
}

/* ========================================
   Feature Details Styles
   ======================================== */

.feature-details {
    display: grid;
    gap: 0.75rem;
}

.feature-item {
    display: grid;
    grid-template-columns: 2fr 1fr 2fr 80px;
    gap: 1rem;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--card-background);
    transition: all 0.2s ease;
    cursor: pointer;
}

.feature-item:hover {
    background: var(--hover-background);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.feature-item.positive-impact {
    border-left: 4px solid #4CAF50;
}

.feature-item.negative-impact {
    border-left: 4px solid #F44336;
}

.feature-name {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.9rem;
}

.feature-value {
    color: var(--text-secondary);
    font-size: 0.85rem;
    font-family: 'Courier New', monospace;
    background: var(--code-background);
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
}

.feature-impact {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.impact-value {
    font-weight: bold;
    font-size: 0.9rem;
    min-width: 60px;
    text-align: right;
}

.impact-bar {
    flex: 1;
    height: 8px;
    background: var(--bar-background);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.impact-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: 4px;
    transition: width 0.3s ease;
}

.positive-impact .impact-fill {
    background: linear-gradient(90deg, #4CAF50, #81C784);
}

.negative-impact .impact-fill {
    background: linear-gradient(90deg, #F44336, #EF5350);
}

.confidence-badge {
    background: var(--confidence-background);
    color: var(--confidence-text);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
    min-width: 50px;
}

/* ========================================
   LIME Explanation Styles
   ======================================== */

.lime-explanation {
    width: 100%;
}

.lime-summary {
    background: var(--info-background);
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1.5rem;
    border-left: 4px solid #FF9800;
}

.lime-summary h4 {
    margin: 0 0 0.5rem 0;
    color: var(--text-color);
    font-size: 1.1rem;
}

.lime-summary p {
    margin: 0.25rem 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.local-features {
    display: grid;
    gap: 1rem;
}

.local-feature-item {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 1rem;
    background: var(--card-background);
}

.local-feature-item h5 {
    margin: 0 0 0.75rem 0;
    color: var(--text-color);
    font-size: 1rem;
}

.feature-details p {
    margin: 0.25rem 0;
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.perturbation-effects {
    margin-top: 0.75rem;
}

.perturbation-effects small {
    color: var(--text-secondary);
    font-size: 0.75rem;
}

.perturbation-bars {
    display: flex;
    height: 6px;
    border-radius: 3px;
    overflow: hidden;
    margin-top: 0.25rem;
    background: var(--bar-background);
}

.pos-effect {
    background: #4CAF50;
    transition: width 0.3s ease;
}

.neg-effect {
    background: #F44336;
    transition: width 0.3s ease;
}

/* ========================================
   Document Annotations Styles
   ======================================== */

.annotations-panel {
    width: 100%;
}

.annotations-panel h4 {
    margin: 0 0 1rem 0;
    color: var(--text-color);
    font-size: 1.1rem;
}

.annotations-list {
    display: grid;
    gap: 0.75rem;
}

.annotation-item {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 1rem;
    background: var(--card-background);
    transition: all 0.2s ease;
}

.annotation-item:hover {
    background: var(--hover-background);
    border-color: var(--primary-color);
}

.annotation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.field-name {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.9rem;
}

.extracted-text {
    background: var(--code-background);
    padding: 0.5rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    color: var(--code-text);
    margin: 0.5rem 0;
    border-left: 3px solid var(--primary-color);
}

.annotation-explanation {
    color: var(--text-secondary);
    font-size: 0.85rem;
    margin: 0.5rem 0;
    font-style: italic;
}

.highlight-button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: background 0.2s ease;
}

.highlight-button:hover {
    background: var(--primary-dark);
}

/* ========================================
   Annotation Overlay Styles
   ======================================== */

.annotation-overlay {
    pointer-events: auto;
    cursor: pointer;
}

.annotation-tooltip {
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    max-width: 200px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    z-index: 1000;
}

.annotation-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal-content {
    background: var(--background-color);
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: var(--header-background);
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: var(--text-color);
}

.close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-button:hover {
    color: var(--text-color);
}

.modal-body {
    padding: 1.5rem;
}

.modal-body p {
    margin: 0.75rem 0;
    color: var(--text-color);
}

/* ========================================
   Confidence Indicators
   ======================================== */

.confidence-high {
    background: #4CAF50;
    color: white;
}

.confidence-medium {
    background: #FF9800;
    color: white;
}

.confidence-low {
    background: #F44336;
    color: white;
}

/* ========================================
   Responsive Design
   ======================================== */

@media (max-width: 1024px) {
    .feature-item {
        grid-template-columns: 1fr;
        gap: 0.5rem;
        text-align: center;
    }
    
    .feature-impact {
        justify-content: center;
    }
    
    .explanation-tabs {
        flex-direction: column;
    }
    
    .tab-button {
        width: 100%;
        margin: 0.25rem 0;
    }
}

@media (max-width: 768px) {
    .explainability-panel {
        margin: 0.5rem 0;
    }
    
    .explanation-content {
        padding: 1rem;
    }
    
    .waterfall-chart {
        height: 200px;
    }
    
    .annotation-item,
    .local-feature-item {
        padding: 0.75rem;
    }
    
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
}

/* ========================================
   Dark Theme Overrides
   ======================================== */

[data-color-scheme="dark"] .explainability-panel {
    --background-color: #1e1e1e;
    --text-color: #ffffff;
    --text-secondary: #b0b0b0;
    --border-color: #333333;
    --header-background: #2d2d2d;
    --card-background: #252525;
    --hover-background: #2a2a2a;
    --info-background: #1a1a2e;
    --chart-background: #1a1a1a;
    --code-background: #2d2d2d;
    --code-text: #ffffff;
    --bar-background: #333333;
    --confidence-background: #333333;
    --confidence-text: #ffffff;
    --primary-color: #2196F3;
    --primary-light: #64B5F6;
    --primary-dark: #1976D2;
}

/* ========================================
   Animation and Transitions
   ======================================== */

.explainability-panel * {
    transition: all 0.2s ease;
}

.feature-item:hover .impact-fill {
    transform: scaleY(1.2);
}

.annotation-item:hover {
    transform: translateY(-2px);
}

.tab-button:active {
    transform: scale(0.98);
}

/* ========================================
   Print Styles
   ======================================== */

@media print {
    .explainability-panel {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .tab-content {
        display: block !important;
    }
    
    .explanation-tabs {
        display: none;
    }
    
    .annotation-tooltip,
    .annotation-modal {
        display: none !important;
    }
}

/* ========================================
   Real-time Features
   ======================================== */

.realtime-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--primary-color);
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateX(100%);
    transition: transform 0.3s ease, opacity 0.3s ease;
    z-index: 1000;
    font-size: 14px;
    max-width: 300px;
    opacity: 0;
}

.realtime-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.realtime-notification::before {
    content: '🔄';
    margin-right: 8px;
}

#last-updated {
    font-size: 12px;
    color: var(--text-secondary);
    font-style: italic;
    margin-top: 8px;
    padding: 4px 8px;
    background: var(--card-background);
    border-radius: 4px;
    display: inline-block;
    border: 1px solid var(--border-color);
}

.realtime-status {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    background: var(--card-background);
    padding: 8px 12px;
    border-radius: 20px;
    border: 1px solid var(--border-color);
    font-size: 12px;
    color: var(--text-secondary);
    z-index: 100;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.realtime-status .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #10b981;
    animation: pulse 2s infinite;
}

.realtime-status.disconnected .status-dot {
    background: #ef4444;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.explainability-panel.updating {
    position: relative;
}

.explainability-panel.updating::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    animation: progress 2s infinite;
    border-radius: 8px 8px 0 0;
}

@keyframes progress {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Enhanced loading states for real-time updates */
.claim-section.loading {
    opacity: 0.7;
    pointer-events: none;
    position: relative;
}

.claim-section.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* Real-time update indicators */
.updated-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    background: #10b981;
    border-radius: 50%;
    margin-left: 8px;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: scale(0); }
    to { opacity: 1; transform: scale(1); }
}

/* Enhanced claim reference input for URL integration */
#claim-reference {
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

#claim-reference:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.url-detected {
    color: var(--primary-color);
    font-size: 12px;
    margin-top: 4px;
    font-style: italic;
}

/* Mobile responsiveness for real-time features */
@media (max-width: 768px) {
    .realtime-notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
        font-size: 12px;
    }
    
    .realtime-status {
        bottom: 10px;
        right: 10px;
        font-size: 11px;
        padding: 6px 10px;
    }
    
    #last-updated {
        font-size: 11px;
        padding: 3px 6px;
    }
} 