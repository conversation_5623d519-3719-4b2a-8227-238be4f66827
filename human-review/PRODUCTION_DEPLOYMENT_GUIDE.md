# Zurich Claims Explainable AI Dashboard - Production Deployment Guide

## 🚀 Production-Ready Features

### ✅ Completed Features
- **SHAP Explanations**: Full feature importance visualization with waterfall charts
- **LIME Local Explanations**: Perturbation-based local explanations  
- **Document Annotations**: Interactive PDF/document highlighting
- **Real-time Updates**: Supabase real-time subscriptions for live data
- **URL-based Access**: Direct claim access via URL parameters
- **Mobile Responsive**: Full mobile and tablet compatibility
- **Dark/Light Themes**: Professional UI with theme switching
- **Production Optimizations**: Error handling, cleanup, performance

## 📋 System Requirements

### Dependencies
- **Supabase**: Real-time database with realtime enabled
- **Modern Browser**: Chrome 88+, Firefox 85+, Safari 14+, Edge 88+
- **Network**: HTTPS required for production (WebSocket security)

### File Structure
```
human-review/
├── review-app.html              # Main dashboard
├── review-app.js               # Application logic + real-time
├── explainability-engine.js    # SHAP/LIME explanations
├── explainability-styles.css   # Complete styling
├── style.css                   # Base dashboard styles
└── PRODUCTION_DEPLOYMENT_GUIDE.md
```

## 🔧 Configuration

### 1. Supabase Setup
Configure your Supabase project with the required tables and real-time enabled:

```sql
-- Enable real-time for required tables
ALTER PUBLICATION supabase_realtime ADD TABLE claims;
ALTER PUBLICATION supabase_realtime ADD TABLE attachments;
```

### 2. Environment Configuration
Update the Supabase configuration in `review-app.js`:

```javascript
// Replace these with your actual Supabase credentials
this.supabaseUrl = 'https://your-project-url.supabase.co';
this.supabaseAnonKey = 'your-anon-key';
```

### 3. API Configuration
Ensure your backend API endpoints are accessible:
- Level 1 Analysis: `/api/level01-analysis/analyze`
- Level 2 Analysis: `/api/level02-coverage/analyze` 
- Supabase Config: `/api/config/supabase`

## 🌐 Deployment Options

### Option 1: Static File Hosting
Deploy directly to any static hosting service:

```bash
# Simple deployment to any web server
cp -r human-review/* /var/www/html/claims-dashboard/
```

**Supported Platforms:**
- Netlify
- Vercel
- GitHub Pages
- AWS S3 + CloudFront
- Azure Static Web Apps

### Option 2: Docker Deployment
```dockerfile
FROM nginx:alpine
COPY human-review/ /usr/share/nginx/html/
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### Option 3: Integrated Backend
Include in your existing backend serving:

```python
# FastAPI example
from fastapi.staticfiles import StaticFiles

app.mount("/dashboard", StaticFiles(directory="human-review"), name="dashboard")
```

## 🔗 Access Methods

### 1. Direct Claim Access
Access specific claims directly via URL:
```
https://your-domain.com/dashboard/?claim=CLM-48935489
https://your-domain.com/dashboard/#claim=ABC123456
```

### 2. Manual Entry
Use the search form to enter any claim reference and load data in real-time.

### 3. Embed in Existing Systems
Include as iframe or integrate directly:
```html
<iframe src="https://your-dashboard.com/?claim=CLM-123456" 
        width="100%" height="800px"></iframe>
```

## ⚡ Real-time Features

### Automatic Updates
The dashboard automatically:
- ✅ Subscribes to Supabase real-time changes
- ✅ Updates UI when claim data changes
- ✅ Shows live processing status
- ✅ Displays real-time notifications
- ✅ Auto-refreshes every 30 seconds as fallback

### Real-time Indicators
- **🟢 Live Updates**: Connected to real-time
- **🔴 Offline**: Disconnected, using polling
- **🔄 Notification**: Data updated in real-time
- **⏰ Last Updated**: Timestamp of last data change

## 🔐 Security Considerations

### 1. HTTPS Requirements
```nginx
# Nginx configuration for HTTPS
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location /dashboard {
        root /var/www/html;
        try_files $uri $uri/ /dashboard/review-app.html;
    }
}
```

### 2. Supabase Security
- Enable Row Level Security (RLS)
- Use environment variables for keys
- Configure proper API permissions

### 3. Content Security Policy
```html
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self' https://*.supabase.co; 
               script-src 'self' 'unsafe-inline' https://unpkg.com; 
               style-src 'self' 'unsafe-inline';">
```

## 🎯 Performance Optimizations

### 1. Caching Strategy
```nginx
# Nginx caching for static assets
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

location /dashboard {
    add_header Cache-Control "no-cache, must-revalidate";
}
```

### 2. CDN Configuration
- Use CDN for static assets
- Configure proper cache headers
- Enable gzip compression

### 3. Monitoring
```javascript
// Performance monitoring (add to review-app.js)
if ('performance' in window) {
    window.addEventListener('load', () => {
        const perfData = performance.timing;
        console.log('Page load time:', perfData.loadEventEnd - perfData.navigationStart);
    });
}
```

## 📱 Mobile Optimization

### Features
- ✅ Responsive layout with mobile-specific CSS
- ✅ Touch-friendly interface
- ✅ Optimized real-time notifications
- ✅ Mobile-specific document viewer
- ✅ Swipe navigation between analysis levels

### Testing
Test on various devices:
```bash
# Chrome DevTools mobile simulation
chrome --user-data-dir=/tmp/chrome --disable-web-security --user-agent="Mobile"
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Real-time Not Working
```javascript
// Debug real-time connection
console.log('Supabase client:', this.supabaseClient);
console.log('Channel status:', this.realtimeChannel?.state);
```

**Solutions:**
- Check Supabase URL and API key
- Verify real-time is enabled on tables
- Ensure HTTPS in production

#### 2. CORS Issues
```javascript
// API configuration
const response = await fetch(endpoint, {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
    },
    body: JSON.stringify(data)
});
```

#### 3. Performance Issues
- Check browser network tab for slow requests
- Monitor real-time subscription count
- Verify auto-refresh interval (30 seconds)

### Debug Mode
Enable debugging by adding to URL:
```
?claim=CLM-123456&debug=true
```

## 🧪 Testing

### Manual Testing Checklist
- [ ] Load claim via URL parameter
- [ ] Load claim via search form
- [ ] Real-time subscription connects
- [ ] Data updates reflect in real-time
- [ ] Explainability features work
- [ ] Mobile responsive layout
- [ ] Theme switching works
- [ ] Error handling for invalid claims

### Browser Testing
Test across browsers:
- Chrome/Chromium 88+
- Firefox 85+
- Safari 14+
- Edge 88+
- Mobile browsers

## 📊 Monitoring & Analytics

### Key Metrics
- Dashboard load time
- Real-time connection success rate
- Claim lookup success rate
- User interaction patterns
- Error rates

### Logging
```javascript
// Add to review-app.js for production logging
const logEvent = (event, data) => {
    console.log(`[${new Date().toISOString()}] ${event}:`, data);
    // Send to analytics service
};
```

## 🔄 Updates & Maintenance

### Update Procedure
1. Test changes in development
2. Update version in comments
3. Deploy new files
4. Clear CDN cache if applicable
5. Monitor for errors

### Version Control
Tag releases with semantic versioning:
```bash
git tag v1.0.0-production
git push origin v1.0.0-production
```

## 📈 Scaling Considerations

### Load Balancing
For high traffic:
```nginx
upstream dashboard_servers {
    server web1.example.com;
    server web2.example.com;
    server web3.example.com;
}

server {
    location /dashboard {
        proxy_pass http://dashboard_servers;
    }
}
```

### Database Considerations
- Monitor Supabase real-time connection limits
- Implement connection pooling if needed
- Consider read replicas for high load

## 🎉 Production Checklist

### Pre-deployment
- [ ] Supabase credentials configured
- [ ] Real-time tables enabled
- [ ] HTTPS certificate installed
- [ ] Security headers configured
- [ ] Performance optimizations applied
- [ ] Mobile testing completed
- [ ] Cross-browser testing done
- [ ] Error handling tested

### Post-deployment
- [ ] Real-time connections working
- [ ] URL-based access functional
- [ ] Performance metrics acceptable
- [ ] Error monitoring active
- [ ] User feedback collection enabled

---

## 🚀 Quick Start

1. **Configure Supabase**: Update credentials in `review-app.js`
2. **Deploy Files**: Copy to web server or hosting platform  
3. **Test Access**: Visit `https://your-domain.com/dashboard/?claim=CLM-48935489`
4. **Monitor**: Check real-time status indicator and performance

The dashboard is now production-ready with comprehensive explainable AI, real-time updates, and enterprise-grade features! 🎯 