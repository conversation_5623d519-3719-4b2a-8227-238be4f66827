<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zurich Claims Human Review Dashboard</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="explainability-styles.css">
    
    <!-- Supabase CDN for real-time functionality -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        /* Additional styles specific to this implementation */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-content {
            background: var(--color-surface);
            padding: var(--space-32);
            border-radius: var(--radius-lg);
            text-align: center;
            max-width: 400px;
        }

        .loading-spinner {
            border: 3px solid var(--color-border);
            border-top: 3px solid var(--color-primary);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto var(--space-16);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .claim-search {
            background: var(--color-surface);
            padding: var(--space-24);
            border-radius: var(--radius-lg);
            margin-bottom: var(--space-24);
            box-shadow: var(--shadow-base);
        }

        .search-form {
            display: flex;
            gap: var(--space-16);
            align-items: flex-end;
        }

        .search-form .form-group {
            flex: 1;
            margin: 0;
        }

        .error-message {
            background: var(--color-error-bg);
            color: var(--color-error);
            padding: var(--space-16);
            border-radius: var(--radius-base);
            margin-top: var(--space-16);
            display: none;
        }

        .no-data {
            padding: var(--space-32);
            text-align: center;
            color: var(--color-text-muted);
            font-style: italic;
        }

        .document-iframe {
            width: 100%;
            height: 600px;
            border: 1px solid var(--color-border);
            border-radius: var(--radius-base);
            background: var(--color-bg);
        }

        .attachment-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .attachment-item {
            padding: var(--space-12);
            border-bottom: 1px solid var(--color-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .attachment-item:last-child {
            border-bottom: none;
        }

        .attachment-name {
            font-weight: 500;
            color: var(--color-primary);
            text-decoration: none;
        }

        .attachment-name:hover {
            text-decoration: underline;
        }

        .attachment-type {
            font-size: var(--font-size-sm);
            color: var(--color-text-muted);
        }

        .json-viewer {
            background: var(--color-code-bg);
            padding: var(--space-16);
            border-radius: var(--radius-base);
            font-family: var(--font-mono);
            font-size: var(--font-size-sm);
            overflow-x: auto;
            max-height: 400px;
            overflow-y: auto;
        }

        .meta-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-16);
            margin-bottom: var(--space-16);
        }

        .meta-item {
            background: var(--color-bg);
            padding: var(--space-12);
            border-radius: var(--radius-base);
        }

        .meta-label {
            font-size: var(--font-size-sm);
            color: var(--color-text-muted);
            margin-bottom: var(--space-4);
        }

        .meta-value {
            font-weight: 600;
            color: var(--color-text);
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h3>Loading claim data...</h3>
            <p>Fetching comprehensive analysis from all levels</p>
        </div>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header__content">
                <h1 class="header__title">Zurich Claims Human Review</h1>
                <div class="header__actions">
                    <button id="theme-toggle" class="btn btn--outline" aria-label="Toggle theme">
                        <span class="theme-icon">🌙</span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Claim Search -->
            <div class="claim-search">
                <h2>Load Claim for Review</h2>
                <form id="claim-search-form" class="search-form">
                    <div class="form-group">
                        <label for="claim-reference" class="form-label">Claim Reference</label>
                        <input 
                            type="text" 
                            id="claim-reference" 
                            class="form-control" 
                            placeholder="e.g., CLM-85228383"
                            value="CLM-85228383"
                            required
                        >
                    </div>
                    <button type="submit" class="btn btn--primary">Load Claim</button>
                </form>
                <div id="error-message" class="error-message"></div>
            </div>

            <!-- Claim Information -->
            <div id="claim-info-section" class="claim-info" style="display: none;">
                <div class="claim-header">
                    <h2>Claim Analysis: <span id="claim-reference-display">-</span></h2>
                    <div class="claim-status">
                        <span id="claim-status" class="status">Loading...</span>
                    </div>
                </div>

                <!-- Meta Information -->
                <div class="meta-info">
                    <div class="meta-item">
                        <div class="meta-label">Claim Type</div>
                        <div id="claim-type-value" class="meta-value">-</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Incident Date</div>
                        <div id="incident-date-value" class="meta-value">-</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Policy Number</div>
                        <div id="policy-number-value" class="meta-value">-</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Claimant</div>
                        <div id="claimant-value" class="meta-value">-</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Estimated Loss</div>
                        <div id="estimated-loss-value" class="meta-value">-</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Data Source</div>
                        <div id="data-source-value" class="meta-value">-</div>
                    </div>
                </div>

                <!-- Real-time Status -->
                <div class="realtime-status" style="margin-top: var(--space-16); display: flex; justify-content: space-between; align-items: center;">
                    <div id="last-updated" class="last-updated" style="font-size: var(--font-size-sm); color: var(--color-text-muted);">
                        Last updated: <span id="last-updated-time">-</span>
                    </div>
                    <div id="realtime-indicator" class="realtime-indicator" style="display: flex; align-items: center; gap: var(--space-8);">
                        <div class="status-dot" style="width: 8px; height: 8px; border-radius: 50%; background: var(--color-border);"></div>
                        <span style="font-size: var(--font-size-sm); color: var(--color-text-muted);">Connecting...</span>
                    </div>
                </div>
            </div>

            <!-- Review Layout -->
            <div id="review-content" class="review-layout" style="display: none;">
                <!-- Document Viewer -->
                <section class="document-viewer">
                    <div class="viewer-controls">
                        <h2>Claim Documents</h2>
                        <div class="controls">
                            <select id="document-selector" class="form-control">
                                <option value="">Select a document...</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="document-content" id="document-content">
                        <div id="document-display" class="document-display">
                            <div class="no-data">Select a document to view</div>
                        </div>
                        
                        <!-- Attachments List -->
                        <div class="card" style="margin-top: var(--space-24);">
                            <div class="card__header">
                                <h3>Attachments</h3>
                            </div>
                            <div class="card__body">
                                <ul id="attachments-list" class="attachment-list">
                                    <li class="no-data">No attachments found</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Analysis Panel -->
                <section class="analysis-panel">
                    <div class="panel-header">
                        <h2>AI Analysis Results</h2>
                        <p class="analysis-timestamp" id="analysis-timestamp">--</p>
                    </div>

                    <div class="analysis-levels">
                        <!-- Level 1: Extraction -->
                        <div class="analysis-level" data-level="1">
                            <div class="level-header">
                                <h3>Level 1: Data Extraction</h3>
                                <span class="level-confidence" id="l1-confidence">--</span>
                            </div>
                            <div class="level-content">
                                <div id="l1-content" class="extracted-fields">
                                    <div class="no-data">Loading Level 1 analysis...</div>
                                </div>
                            </div>
                        </div>

                        <!-- Level 2: Coverage -->
                        <div class="analysis-level" data-level="2">
                            <div class="level-header">
                                <h3>Level 2: Coverage Analysis</h3>
                                <span class="level-confidence" id="l2-confidence">--</span>
                            </div>
                            <div class="level-content">
                                <div id="l2-content" class="coverage-decision">
                                    <div class="no-data">Loading Level 2 analysis...</div>
                                </div>
                            </div>
                        </div>

                        <!-- Level 3: Fault Determination -->
                        <div class="analysis-level" data-level="3">
                            <div class="level-header">
                                <h3>Level 3: Fault Determination</h3>
                                <span class="level-confidence" id="l3-confidence">--</span>
                            </div>
                            <div class="level-content">
                                <div id="l3-content" class="fault-allocation">
                                    <div class="no-data">Loading Level 3 analysis...</div>
                                </div>
                            </div>
                        </div>

                        <!-- Level 4: Quantum Calculation -->
                        <div class="analysis-level" data-level="4">
                            <div class="level-header">
                                <h3>Level 4: Quantum Calculation</h3>
                                <span class="level-confidence" id="l4-confidence">--</span>
                            </div>
                            <div class="level-content">
                                <div id="l4-content" class="quantum-breakdown">
                                    <div class="no-data">Loading Level 4 analysis...</div>
                                </div>
                            </div>
                        </div>

                        <!-- Explainable AI Panel -->
                        <div class="analysis-level explainable-ai-section" data-level="explain">
                            <div class="level-header">
                                <h3>AI Decision Explanation</h3>
                                <span class="level-confidence" id="explain-confidence">--</span>
                            </div>
                            <div class="level-content">
                                <div id="explainability-container">
                                    <div class="no-data">Click "Generate Explanation" to see AI decision reasoning</div>
                                    <button id="generate-explanation-btn" class="btn btn--primary" style="margin-top: 1rem;">
                                        Generate AI Explanation
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Recommendations -->
                        <div class="recommendations">
                            <h3>Settlement Recommendations</h3>
                            <div id="recommendations-content">
                                <div class="no-data">Loading recommendations...</div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <button id="approve-btn" class="btn btn--primary" disabled>
                            Approve Settlement
                        </button>
                        <button id="escalate-btn" class="btn btn--secondary" disabled>
                            Escalate for Review
                        </button>
                        <button id="export-btn" class="btn btn--outline">
                            Export Report
                        </button>
                    </div>
                </section>
            </div>
        </div>
    </main>

    <!-- Mobile Toggle (for responsive view) -->
    <button id="mobile-toggle" class="mobile-toggle">
        <span class="toggle-text">Show Analysis</span>
    </button>

    <!-- Real-time Status Indicator -->
    <div id="realtime-status" class="realtime-status" style="display: none;">
        <div class="status-dot"></div>
        <span>Live Updates</span>
    </div>

    <script src="explainability-engine.js"></script>
    <script src="review-app.js"></script>
</body>
</html> 