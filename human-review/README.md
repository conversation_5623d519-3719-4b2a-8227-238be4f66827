# Zurich Claims Human Review Dashboard

A comprehensive web application for human agents to review insurance claims with all AI analysis results from Levels 1-4, including documents from Supabase storage.

## Features

### 🎯 Core Functionality
- **Single Claim Reference Input**: Load all claim data with just the claim reference
- **Document Viewer**: Display PDFs, images, and OCR text from Supabase storage
- **4-Level AI Analysis Display**: 
  - Level 1: Data Extraction
  - Level 2: Coverage Analysis
  - Level 3: Fault Determination
  - Level 4: Quantum Calculation
- **Interactive Confidence Indicators**: Visual confidence scores for each analysis level
- **Smart Recommendations**: AI-driven settlement recommendations
- **Action Buttons**: Approve settlements or escalate for review

### 🎨 User Experience
- **Dark/Light Theme Toggle**: Comfortable viewing in any environment
- **Mobile Responsive**: Optimized for desktop, tablet, and mobile devices
- **Keyboard Shortcuts**: 
  - `Alt+T`: Toggle theme
  - `Alt+L`: Focus on claim reference input
- **Loading States**: Clear feedback during data fetching
- **Error Handling**: Graceful error messages with recovery options

## Quick Start

### 1. Prerequisites
- Running Zurich backend API (default: `http://localhost:8000`)
- Valid claim reference with data in Supabase
- Modern web browser (Chrome, Firefox, Safari, Edge)

### 2. Setup
1. Open `review-app.html` in your web browser
2. Enter the API endpoint (default is `http://localhost:8000`)
3. Enter a claim reference (e.g., `CLM-48935489`)
4. Click "Load Claim"

### 3. Using the Dashboard

#### Document Viewer (Left Panel)
- **Document Selector**: Choose from available documents
- **Document Display**: 
  - PDFs display in embedded viewer
  - Images show inline
  - Other files show OCR text or download link
- **Attachments List**: Quick access to all claim documents

#### Analysis Panel (Right Panel)
- **Level 1 - Data Extraction**: View extracted fields like claimant, dates, locations
- **Level 2 - Coverage Analysis**: See coverage decision and justification
- **Level 3 - Fault Determination**: Review fault percentages and legal basis
- **Level 4 - Quantum Calculation**: Examine damage calculations and settlement ranges

#### Action Buttons
- **Approve Settlement**: Enabled when claim is ready for settlement
- **Escalate for Review**: Available when any analysis requires human review
- **Export Report**: Download complete claim analysis as JSON

## Data Flow

```mermaid
graph TD
    A[Enter Claim Reference] --> B[Fetch from Backend API]
    B --> C[Level 1 Analysis]
    B --> D[Level 2 Coverage]
    B --> E[Level 3 Fault]
    B --> F[Level 4 Quantum]
    B --> G[Supabase Storage Documents]
    
    C --> H[Display Extracted Data]
    D --> I[Show Coverage Decision]
    E --> J[Present Fault Split]
    F --> K[Calculate Settlement]
    G --> L[Load Documents]
    
    H --> M[Human Review]
    I --> M
    J --> M
    K --> M
    L --> M
    
    M --> N[Approve/Escalate]
```

## API Integration

The dashboard integrates with the following backend endpoints:

### Analysis Endpoints
- `POST /api/level01-analysis/analyze` - Level 1 extraction
- `POST /api/level02-coverage/` - Level 2 coverage analysis
- `POST /api/level03-fault/analyze` - Level 3 fault determination
- `POST /api/level04-quantum/calculate-ai` - Level 4 quantum calculation

### Document Endpoints
- `POST /api/supabase-ocr/list-files` - List documents from Supabase storage
- Direct Supabase storage URLs for document viewing

## Customization

### Styling
The application uses CSS custom properties for easy theming:

```css
:root {
    --color-primary: #21808d;
    --color-success: #4CAF50;
    --color-error: #F44336;
    --color-warning: #FF9800;
    /* ... more in style.css */
}
```

### Configuration
Modify default settings in `review-app.js`:

```javascript
constructor() {
    this.apiEndpoint = 'http://localhost:8000'; // Default API endpoint
    this.claimReference = '';
    this.mobileViewMode = 'document'; // Default mobile view
}
```

## Troubleshooting

### Common Issues

1. **"Failed to load claim" error**
   - Verify the API endpoint is correct
   - Check if the backend server is running
   - Ensure the claim reference exists in the database

2. **Documents not loading**
   - Verify Supabase storage configuration
   - Check if documents exist under `claim/{claim_reference}/` path
   - Ensure proper CORS settings on Supabase

3. **Analysis levels showing "not available"**
   - The claim may not have been processed through all levels
   - Run the claim through the n8n workflow first
   - Check API logs for processing errors

### Debug Mode
Open browser developer console and check for:
- Network requests to API endpoints
- JavaScript errors in console
- Response data structure

## Security Considerations

1. **Authentication**: Currently uses direct API access - implement JWT tokens for production
2. **CORS**: Configure appropriate CORS headers on backend
3. **Data Sanitization**: All user inputs are escaped before display
4. **Secure Storage**: Sensitive claim data should use HTTPS in production

## Future Enhancements

- [ ] Real-time updates via WebSocket
- [ ] Collaborative review with multiple agents
- [ ] Audit trail for all actions
- [ ] Integration with case management systems
- [ ] Advanced search and filtering
- [ ] Bulk claim processing
- [ ] Voice notes and annotations
- [ ] AI explanation tooltips

## Development

### File Structure
```
human-review/
├── review-app.html     # Main application HTML
├── review-app.js       # Application logic
├── style.css          # Shared styles
└── README.md          # This file
```

### Building for Production
1. Minify JavaScript and CSS files
2. Configure production API endpoints
3. Add authentication layer
4. Enable HTTPS
5. Set up monitoring and logging

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review API documentation
3. Examine browser console logs
4. Contact the development team

---

Built with ❤️ for Zurich Insurance Claims Processing 