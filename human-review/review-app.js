// Zurich Claims Human Review Application
class ZurichClaimsReviewApp {
    constructor() {
        this.claimReference = '';
        this.claimData = null;
        this.mobileViewMode = 'document';
        
        // Supabase Configuration - Direct connection like backend
        this.supabaseUrl = 'https://tlduggpohclrgxbvuzhd.supabase.co';
        this.supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZHVnZ3BvaGNscmd4YnZ1emhkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3MDEwMzgsImV4cCI6MjA2NjI3NzAzOH0.Eud_SDfGulErh3yhqjaIMqM37eghuz-PVeRxknzxRfk';
        this.supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZHVnZ3BvaGNscmd4YnZ1emhkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDcwMTAzOCwiZXhwIjoyMDY2Mjc3MDM4fQ.wDi5bG1O81a3HaXOujWshJZoGV6AhFgEuaoVcjam0SE';
        this.supabaseBucketName = 'claims-attachments';
        this.supabaseClient = null;
        this.realtimeChannel = null;
        this.autoRefreshInterval = null;
        this.lastUpdateTimestamp = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupThemeToggle();
        this.setupMobileView();
        this.initializeSupabaseDirectly();
        this.initializeRealtimeFeatures();
    }

    setupEventListeners() {
        // Form submission
        document.getElementById('claim-search-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.loadClaim();
        });

        // Theme toggle
        document.getElementById('theme-toggle').addEventListener('click', () => this.toggleTheme());
        
        // Mobile toggle
        document.getElementById('mobile-toggle').addEventListener('click', () => this.toggleMobileView());
        
        // Document selector
        document.getElementById('document-selector').addEventListener('change', (e) => {
            this.displayDocument(e.target.value);
        });

        // Action buttons
        document.getElementById('approve-btn').addEventListener('click', () => this.handleApprove());
        document.getElementById('escalate-btn').addEventListener('click', () => this.handleEscalate());
        document.getElementById('export-btn').addEventListener('click', () => this.handleExport());

        // Explainability button
        document.getElementById('generate-explanation-btn').addEventListener('click', () => this.generateExplanation());

        // Analysis level click handlers
        document.querySelectorAll('.analysis-level').forEach(level => {
            level.addEventListener('click', (e) => {
                if (!e.target.closest('.level-header')) return;
                level.classList.toggle('expanded');
            });
        });
    }

    async initializeSupabaseDirectly() {
        try {
            // Initialize Supabase client directly with known credentials
            if (typeof supabase !== 'undefined') {
                // Use service role key for full database access (like backend)
                this.supabaseClient = supabase.createClient(this.supabaseUrl, this.supabaseServiceKey);
                console.log('✅ Supabase client initialized successfully');
                console.log('URL:', this.supabaseUrl);
                console.log('Using service role key for full database access');
                
                // Test connection
                await this.testSupabaseConnection();
            } else {
                console.warn('❌ Supabase library not loaded from CDN');
                this.showError('Supabase library not available. Real-time features disabled.');
            }
        } catch (error) {
            console.error('❌ Failed to initialize Supabase:', error);
            this.showError(`Supabase initialization failed: ${error.message}`);
        }
    }

    async testSupabaseConnection() {
        try {
            // Test basic connection by trying to access claims table
            const { data, error } = await this.supabaseClient
                .from('claims')
                .select('count')
                .limit(1);
            
            if (error) {
                console.warn('⚠️ Supabase connection test failed:', error.message);
                this.showRealtimeStatus(false);
            } else {
                console.log('✅ Supabase connection test successful');
                this.showRealtimeStatus(true);
            }
        } catch (error) {
            console.warn('⚠️ Supabase connection test error:', error);
            this.showRealtimeStatus(false);
        }
    }

    initializeRealtimeFeatures() {
        // Check for claim reference in URL
        this.checkForUrlClaimReference();
        
        // Set up auto-refresh for real-time updates
        this.setupAutoRefresh();
        
        // Set up URL change listener for SPA behavior
        window.addEventListener('popstate', () => {
            this.checkForUrlClaimReference();
        });
    }

    checkForUrlClaimReference() {
        const urlParams = new URLSearchParams(window.location.search);
        const claimRef = urlParams.get('claim') || this.extractClaimFromUrl();
        
        if (claimRef && claimRef !== this.claimReference) {
            document.getElementById('claim-reference').value = claimRef;
            this.loadClaim();
        }
    }

    extractClaimFromUrl() {
        // Extract claim reference from various URL patterns
        const path = window.location.pathname;
        const hash = window.location.hash;
        
        // Pattern: /claim/ABC123456 or #claim=ABC123456
        const pathMatch = path.match(/\/claim\/([A-Z0-9-]+)/i);
        const hashMatch = hash.match(/#claim=([A-Z0-9-]+)/i);
        
        return pathMatch?.[1] || hashMatch?.[1] || null;
    }

    setupAutoRefresh() {
        // Clear existing interval
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
        }
        
        // Set up auto-refresh every 30 seconds
        this.autoRefreshInterval = setInterval(() => {
            if (this.claimReference) {
                this.refreshClaimData();
            }
        }, 30000);
    }

    async refreshClaimData() {
        if (!this.claimReference) return;
        
        try {
            console.log(`🔄 Auto-refreshing data for claim ${this.claimReference}`);
            const claimData = await this.fetchComprehensiveClaimDataFromSupabase(this.claimReference);
            const hasChanges = this.detectDataChanges(claimData);
            
            if (hasChanges) {
                this.claimData = claimData;
                this.updateDisplayWithNewData();
                this.showRealtimeNotification('Claim data updated in real-time');
                console.log('✅ Real-time update applied');
            }
        } catch (error) {
            console.error('❌ Auto-refresh failed:', error);
        }
    }

    detectDataChanges(newData) {
        if (!this.claimData) return true;
        
        // Compare timestamps or data hashes
        const oldHash = this.generateDataHash(this.claimData);
        const newHash = this.generateDataHash(newData);
        
        return oldHash !== newHash;
    }

    generateDataHash(data) {
        // Simple hash function for change detection
        const str = JSON.stringify(data);
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash;
    }

    setupRealtimeSubscription(claimReference) {
        // Clean up existing subscription
        this.cleanupRealtimeSubscription();
        
        if (!this.supabaseClient) {
            console.warn('⚠️ Supabase client not available for real-time subscriptions');
            return;
        }
        
        try {
            console.log(`🔄 Setting up real-time subscription for claim ${claimReference}`);
            
            // Subscribe to claims table changes for this specific claim
            this.realtimeChannel = this.supabaseClient
                .channel('claims-realtime')
                .on(
                    'postgres_changes',
                    {
                        event: '*', // Listen to INSERT, UPDATE, DELETE
                        schema: 'public',
                        table: 'claims',
                        filter: `claim_reference=eq.${claimReference}`
                    },
                    (payload) => this.handleRealtimeUpdate(payload)
                )
                .on(
                    'postgres_changes',
                    {
                        event: '*',
                        schema: 'public',
                        table: 'attachments',
                        filter: `claim_reference=eq.${claimReference}`
                    },
                    (payload) => this.handleAttachmentUpdate(payload)
                )
                .subscribe((status) => {
                    console.log('📡 Real-time subscription status:', status);
                    this.showRealtimeStatus(status === 'SUBSCRIBED');
                });
            
            console.log('✅ Real-time subscription established');
        } catch (error) {
            console.error('❌ Failed to set up real-time subscription:', error);
            this.showRealtimeStatus(false);
        }
    }

    cleanupRealtimeSubscription() {
        if (this.realtimeChannel) {
            this.supabaseClient.removeChannel(this.realtimeChannel);
            this.realtimeChannel = null;
        }
        this.showRealtimeStatus(false);
    }

    showRealtimeStatus(connected) {
        const indicator = document.getElementById('realtime-indicator');
        if (!indicator) return;
        
        const dot = indicator.querySelector('.status-dot');
        const text = indicator.querySelector('span');
        
        if (connected) {
            dot.style.background = 'var(--color-success)';
            text.textContent = 'Live';
            text.style.color = 'var(--color-success)';
        } else {
            dot.style.background = 'var(--color-border)';
            text.textContent = 'Offline';
            text.style.color = 'var(--color-text-muted)';
        }
    }

    async handleRealtimeUpdate(payload) {
        console.log('Real-time claim update:', payload);
        
        const { eventType, new: newRecord, old: oldRecord } = payload;
        
        switch (eventType) {
            case 'UPDATE':
                // Merge new data into existing claim data
                this.mergeClaimUpdate(newRecord);
                this.updateDisplayWithNewData();
                this.showRealtimeNotification('Claim analysis updated');
                break;
            case 'INSERT':
                this.showRealtimeNotification('New claim data available');
                await this.refreshClaimData();
                break;
        }
    }

    async handleAttachmentUpdate(payload) {
        console.log('Real-time attachment update:', payload);
        
        const { eventType, new: newRecord } = payload;
        
        if (eventType === 'UPDATE' && newRecord.ocr_text) {
            this.showRealtimeNotification('Document processing completed');
            await this.loadDocuments();
        }
    }

    mergeClaimUpdate(newRecord) {
        if (!this.claimData) return;
        
        // Merge the new record data into existing claim data
        Object.keys(newRecord).forEach(key => {
            if (newRecord[key] !== null) {
                this.claimData[key] = newRecord[key];
            }
        });
        
        this.lastUpdateTimestamp = new Date().toISOString();
    }

    updateDisplayWithNewData() {
        // Update all display components with new data
        this.displayClaimInfo();
        this.displayAnalysisLevels();
        this.updateActionButtons();
        
        // Update last updated timestamp
        this.displayLastUpdated();
    }

    displayLastUpdated() {
        const timeElement = document.getElementById('last-updated-time');
        if (!timeElement || !this.lastUpdateTimestamp) return;
        
        const timeString = this.lastUpdateTimestamp.toLocaleString('en-US', { 
            dateStyle: 'medium', 
            timeStyle: 'short' 
        });
        
        timeElement.textContent = timeString;
    }

    showRealtimeNotification(message) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'realtime-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: var(--space-16);
            right: var(--space-16);
            background: var(--color-success);
            color: white;
            padding: var(--space-12) var(--space-16);
            border-radius: var(--radius-base);
            box-shadow: var(--shadow-lg);
            z-index: 10000;
            font-size: var(--font-size-sm);
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // Show notification
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateY(0)';
        }, 100);
        
        // Hide notification after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    updateUrl(claimReference) {
        // Update URL without refreshing the page
        const newUrl = `${window.location.pathname}?claim=${claimReference}`;
        window.history.pushState({ claimReference }, '', newUrl);
    }

    setupThemeToggle() {
        const themeToggle = document.getElementById('theme-toggle');
        const themeIcon = themeToggle.querySelector('.theme-icon');
        
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-color-scheme', savedTheme);
        themeIcon.textContent = savedTheme === 'dark' ? '☀️' : '🌙';
    }

    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-color-scheme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        
        document.documentElement.setAttribute('data-color-scheme', newTheme);
        localStorage.setItem('theme', newTheme);
        
        const themeIcon = document.querySelector('.theme-icon');
        themeIcon.textContent = newTheme === 'dark' ? '☀️' : '🌙';
    }

    setupMobileView() {
        this.updateMobileView();
        window.addEventListener('resize', () => this.updateMobileView());
    }

    updateMobileView() {
        const isMobile = window.innerWidth <= 1024;
        const documentViewer = document.querySelector('.document-viewer');
        const analysisPanel = document.querySelector('.analysis-panel');
        const mobileToggle = document.getElementById('mobile-toggle');
        
        if (isMobile) {
            mobileToggle.style.display = 'block';
            if (this.mobileViewMode === 'document') {
                documentViewer.style.display = 'block';
                analysisPanel.style.display = 'none';
                mobileToggle.querySelector('.toggle-text').textContent = 'Show Analysis';
            } else {
                documentViewer.style.display = 'none';
                analysisPanel.style.display = 'block';
                mobileToggle.querySelector('.toggle-text').textContent = 'Show Document';
            }
        } else {
            mobileToggle.style.display = 'none';
            documentViewer.style.display = 'block';
            analysisPanel.style.display = 'block';
        }
    }

    toggleMobileView() {
        this.mobileViewMode = this.mobileViewMode === 'document' ? 'analysis' : 'document';
        this.updateMobileView();
    }

    showLoading(show = true) {
        document.getElementById('loading-overlay').style.display = show ? 'flex' : 'none';
    }

    showError(message) {
        const errorElement = document.getElementById('error-message');
        errorElement.textContent = message;
        errorElement.style.display = 'block';
        setTimeout(() => {
            errorElement.style.display = 'none';
        }, 5000);
    }

    async loadClaim() {
        const claimRef = document.getElementById('claim-reference').value.trim();
        
        if (!claimRef) {
            this.showError('Please enter a claim reference');
            return;
        }

        this.showLoading(true);
        this.claimReference = claimRef;
        
        // Update URL for sharing/bookmarking
        this.updateUrl(claimRef);
        
        try {
            console.log(`🔍 Loading comprehensive data for claim ${claimRef}`);
            
            // Fetch comprehensive data directly from Supabase only
            if (!this.supabaseClient) {
                throw new Error('Supabase client not available. Please check your configuration.');
            }
            
            console.log('📊 Using direct Supabase access for comprehensive data');
            const claimData = await this.fetchComprehensiveClaimDataFromSupabase(claimRef);
            
            this.claimData = claimData;
            this.displayClaimInfo();
            this.displayAnalysisLevels();
            this.displayRecommendations();
            this.loadDocuments();
            this.updateActionButtons();
            
            // Set up real-time subscription for this claim
            if (this.supabaseClient) {
                this.setupRealtimeSubscription(claimRef);
            }
            
            // Update last loaded timestamp
            this.lastUpdateTimestamp = new Date();
            this.displayLastUpdated();
            
            console.log('✅ Claim data loaded successfully');
            
        } catch (error) {
            console.error('❌ Error loading claim:', error);
            this.showError(`Failed to load claim ${claimRef}: ${error.message}`);
        } finally {
            this.showLoading(false);
        }
    }

    async fetchComprehensiveClaimDataFromSupabase(claimReference) {
        // Direct Supabase data fetching - the preferred method
        console.log(`🔍 Fetching comprehensive data from Supabase for claim ${claimReference}`);
        
        const data = {
            claim_details: {},
            level01_analysis: null,
            level02_analysis: null,
            level03_analysis: null,
            level04_analysis: null,
            attachments: [],
            documents: [],
            ocr_texts: [],
            fetched_from_supabase: true,
            data_source: 'supabase_direct'
        };

        try {
            // Fetch main claim record with all analysis levels
            console.log('📊 Fetching claim data from claims table...');
            const { data: claimRecord, error: claimError } = await this.supabaseClient
                .from('claims')
                .select(`
                    *,
                    claim_reference,
                    email_subject,
                    email_body,
                    01_level_analysis,
                    02_level_analysis,
                    03_level_analysis,
                    04_level_analysis
                `)
                .eq('claim_reference', claimReference)
                .single();

            if (claimError) {
                console.error('❌ Error fetching claim:', claimError);
                throw claimError;
            }

            if (claimRecord) {
                console.log('✅ Claim record found:', claimRecord);
                
                // Extract data from the claim record
                data.claim_details = {
                    claim_reference: claimRecord.claim_reference,
                    email_subject: claimRecord.email_subject,
                    email_body: claimRecord.email_body,
                    claim_id: claimRecord.claim_id,
                    // Add any other basic claim fields that exist
                    ...claimRecord
                };

                // Parse JSON analysis data if available
                try {
                    if (claimRecord['01_level_analysis']) {
                        data.level01_analysis = typeof claimRecord['01_level_analysis'] === 'string' 
                            ? JSON.parse(claimRecord['01_level_analysis']) 
                            : claimRecord['01_level_analysis'];
                        console.log('✅ Level 1 analysis loaded');
                    }
                    
                    if (claimRecord['02_level_analysis']) {
                        data.level02_analysis = typeof claimRecord['02_level_analysis'] === 'string' 
                            ? JSON.parse(claimRecord['02_level_analysis']) 
                            : claimRecord['02_level_analysis'];
                        console.log('✅ Level 2 analysis loaded');
                    }
                    
                    if (claimRecord['03_level_analysis']) {
                        data.level03_analysis = typeof claimRecord['03_level_analysis'] === 'string' 
                            ? JSON.parse(claimRecord['03_level_analysis']) 
                            : claimRecord['03_level_analysis'];
                        console.log('✅ Level 3 analysis loaded');
                    }
                    
                    if (claimRecord['04_level_analysis']) {
                        data.level04_analysis = typeof claimRecord['04_level_analysis'] === 'string' 
                            ? JSON.parse(claimRecord['04_level_analysis']) 
                            : claimRecord['04_level_analysis'];
                        console.log('✅ Level 4 analysis loaded');
                    }
                } catch (parseError) {
                    console.warn('⚠️ Error parsing analysis JSON:', parseError);
                }

                // Fetch attachments for this claim
                console.log('📎 Fetching attachments...');
                const { data: attachments, error: attachmentsError } = await this.supabaseClient
                    .from('attachments')
                    .select('*')
                    .eq('claim_reference', claimReference);

                if (attachmentsError) {
                    console.warn('⚠️ Error fetching attachments:', attachmentsError);
                } else {
                    data.attachments = attachments || [];
                    console.log(`✅ Found ${data.attachments.length} attachments`);
                }

                console.log('🎉 Successfully loaded comprehensive claim data from Supabase');
                return data;
            } else {
                console.log('⚠️ No claim found with reference:', claimReference);
                return data;
            }

        } catch (error) {
            console.error('❌ Error fetching data from Supabase:', error);
            throw error;
        }
    }

    displayClaimInfo() {
        if (!this.claimData) return;

        const claimDetails = this.claimData.claim_details || {};
        const level01 = this.claimData.level01_analysis || {};
        
        // Update claim reference display
        document.getElementById('claim-reference-display').textContent = 
            this.claimReference || 'Unknown';

        // Update claim status
        const status = this.determineClaimStatus();
        const statusElement = document.getElementById('claim-status');
        if (statusElement) {
            statusElement.textContent = status;
            statusElement.className = `status ${this.getStatusClass(status)}`;
        }

        // Update meta information
        this.updateMetaInfo();

        // Show the claim info section
        const claimInfoSection = document.getElementById('claim-info-section');
        const reviewContent = document.getElementById('review-content');
        
        if (claimInfoSection) claimInfoSection.style.display = 'block';
        if (reviewContent) reviewContent.style.display = 'grid';
    }

    updateMetaInfo() {
        const claimDetails = this.claimData?.claim_details || {};
        const level01 = this.claimData?.level01_analysis || {};
        
        // Update meta items with graceful fallbacks
        const metaItems = [
            {
                id: 'claim-type',
                label: 'Claim Type',
                value: level01.claimType || 
                       claimDetails.claim_type || 
                       claimDetails.type || 
                       'Not specified'
            },
            {
                id: 'incident-date',
                label: 'Incident Date',
                value: level01.incidentDate || 
                       claimDetails.incident_date || 
                       claimDetails.date_of_loss || 
                       'Not available'
            },
            {
                id: 'policy-number',
                label: 'Policy Number',
                value: level01.policyNumber || 
                       claimDetails.policy_number || 
                       claimDetails.policy_ref || 
                       'Not available'
            },
            {
                id: 'claimant',
                label: 'Claimant',
                value: level01.claimant || 
                       claimDetails.claimant_name || 
                       claimDetails.insured_name || 
                       'Not available'
            },
            {
                id: 'estimated-loss',
                label: 'Estimated Loss',
                value: level01.estimatedLoss || 
                       claimDetails.estimated_loss || 
                       claimDetails.claim_amount || 
                       'Not estimated'
            },
            {
                id: 'data-source',
                label: 'Data Source',
                value: this.claimData.fetched_from_supabase ? 'Supabase (Real-time)' : 'API Fallback'
            }
        ];

        metaItems.forEach(item => {
            const valueElement = document.getElementById(`${item.id}-value`);
            if (valueElement) {
                valueElement.textContent = item.value;
            }
        });

        // Update email information if available
        const emailSubject = claimDetails.email_subject;
        const emailBody = claimDetails.email_body;
        
        if (emailSubject) {
            const emailSection = document.createElement('div');
            emailSection.className = 'meta-item';
            emailSection.innerHTML = `
                <div class="meta-label">Email Subject</div>
                <div class="meta-value">${this.escapeHtml(emailSubject)}</div>
            `;
            
            const metaInfo = document.querySelector('.meta-info');
            if (metaInfo) {
                metaInfo.appendChild(emailSection);
            }
        }
    }

    determineClaimStatus() {
        if (!this.claimData) return 'Loading';
        
        const level01 = this.claimData.level01_analysis;
        const level02 = this.claimData.level02_analysis;
        const level03 = this.claimData.level03_analysis;
        const level04 = this.claimData.level04_analysis;
        
        // Determine status based on available analysis levels
        if (level04) {
            return level04.exit_path || 'Level 4 Complete';
        } else if (level03) {
            return level03.exit_path || 'Level 3 Complete';
        } else if (level02) {
            return level02.coverage_decision || 'Level 2 Complete';
        } else if (level01) {
            return level01.exitPath || level01.level01ExitPath || 'Level 1 Complete';
        } else {
            return 'Data Available';
        }
    }

    getStatusClass(status) {
        const statusLower = status.toLowerCase();
        
        if (statusLower.includes('covered') || statusLower.includes('complete')) {
            return 'success';
        } else if (statusLower.includes('not_covered') || statusLower.includes('investigation')) {
            return 'warning';
        } else if (statusLower.includes('error') || statusLower.includes('dispute')) {
            return 'error';
        } else {
            return 'info';
        }
    }

    async loadDocuments() {
        if (!this.claimData) return;

        const attachments = this.claimData.attachments || [];
        const selector = document.getElementById('document-selector');
        const container = document.getElementById('document-container');
        
        if (!selector || !container) return;

        // Clear existing options
        selector.innerHTML = '<option value="">Select a document...</option>';
        
        if (attachments.length === 0) {
            container.innerHTML = '<div class="no-data">No documents available for this claim.</div>';
            return;
        }

        // Build document list with storage URLs
        const documents = attachments.map((attachment, index) => {
            const fileName = attachment.file_name || attachment.name || `Document ${index + 1}`;
            const contentType = attachment.content_type || attachment.mimetype || 'application/octet-stream';
            
            // Build Supabase storage URL for claims-attachments bucket
            const storageUrl = this.buildStorageUrl(this.claimReference, fileName);
            
            return {
                index,
                name: fileName,
                type: contentType,
                url: storageUrl,
                ocrText: attachment.ocr_text || null,
                size: attachment.size || null
            };
        });

        // Populate selector
        documents.forEach((doc, index) => {
            const option = document.createElement('option');
            option.value = index;
            option.textContent = `${doc.name} (${this.getFileTypeLabel(doc.type)})`;
            selector.appendChild(option);
        });

        // Display document list
        this.displayDocumentList(documents);
        
        console.log(`📄 Loaded ${documents.length} documents`);
    }

    buildStorageUrl(claimReference, fileName) {
        // Build the storage URL following the pattern: claims-attachments/claim/CLM-85228383/filename
        const bucketName = 'claims-attachments';
        const path = `claim/${claimReference}/${fileName}`;
        
        if (this.supabaseClient) {
            // Use Supabase client to get public URL
            const { data } = this.supabaseClient.storage
                .from(bucketName)
                .getPublicUrl(path);
            return data.publicUrl;
        } else {
            // Construct URL manually
            return `${this.supabaseUrl}/storage/v1/object/public/${bucketName}/${path}`;
        }
    }

    displayDocumentList(documents) {
        const container = document.getElementById('document-container');
        if (!container) return;

        if (documents.length === 0) {
            container.innerHTML = '<div class="no-data">No documents available for this claim.</div>';
            return;
        }

        const listHtml = `
            <div class="document-list">
                <h4>Available Documents (${documents.length})</h4>
                <ul class="attachment-list">
                    ${documents.map((doc, index) => `
                        <li class="attachment-item">
                            <div>
                                <a href="${doc.url}" target="_blank" class="attachment-name">
                                    ${this.escapeHtml(doc.name)}
                                </a>
                                <div class="attachment-type">
                                    ${this.getFileTypeLabel(doc.type)}
                                    ${doc.size ? ` • ${this.formatFileSize(doc.size)}` : ''}
                                </div>
                                ${doc.ocrText ? '<div class="attachment-type">✓ OCR Text Available</div>' : ''}
                            </div>
                            <button onclick="app.displayDocument(${index})" class="btn btn--sm">
                                View
                            </button>
                        </li>
                    `).join('')}
                </ul>
            </div>
        `;

        container.innerHTML = listHtml;
    }

    getFileTypeLabel(contentType) {
        if (!contentType) return 'File';
        if (contentType.includes('pdf')) return 'PDF';
        if (contentType.includes('image')) return 'Image';
        if (contentType.includes('text')) return 'Text';
        if (contentType.includes('word')) return 'Word Document';
        if (contentType.includes('excel')) return 'Spreadsheet';
        return 'Document';
    }

    formatFileSize(bytes) {
        if (!bytes) return '--';
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        if (bytes === 0) return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    displayAnalysisLevels() {
        // Display Level 1 Analysis
        this.displayLevel1Analysis();
        
        // Display Level 2 Coverage
        this.displayLevel2Analysis();
        
        // Display Level 3 Fault
        this.displayLevel3Analysis();
        
        // Display Level 4 Quantum
        this.displayLevel4Analysis();
        
        // Display Recommendations
        this.displayRecommendations();
    }

    displayLevel1Analysis() {
        const content = document.getElementById('l1-content');
        const confidence = document.getElementById('l1-confidence');
        const data = this.claimData.level01_analysis;
        
        if (!data) {
            content.innerHTML = '<div class="no-data">Level 1 analysis not available</div>';
            confidence.textContent = '--';
            return;
        }
        
        // Update confidence
        const dataQuality = data.dataQualityScore || 0;
        confidence.textContent = `${Math.round(dataQuality * 100)}%`;
        confidence.className = `level-confidence ${this.getConfidenceClass(dataQuality)}`;
        
        // Build extracted fields display
        const fields = [];
        
        if (data.claimDetails) {
            const details = data.claimDetails;
            if (details.claimId) fields.push(this.createFieldItem('Claim ID', details.claimId));
            if (details.claimType) fields.push(this.createFieldItem('Claim Type', details.claimType));
            if (details.claimant) fields.push(this.createFieldItem('Claimant', details.claimant));
            if (details.incidentDate) fields.push(this.createFieldItem('Incident Date', details.incidentDate));
            if (details.reportDate) fields.push(this.createFieldItem('Report Date', details.reportDate));
            if (details.location) fields.push(this.createFieldItem('Location', details.location));
            if (details.primaryCause) fields.push(this.createFieldItem('Primary Cause', details.primaryCause));
        }
        
        if (data.policyDetails) {
            const policy = data.policyDetails;
            if (policy.policyNumber) fields.push(this.createFieldItem('Policy Number', policy.policyNumber));
            if (policy.policyType) fields.push(this.createFieldItem('Policy Type', policy.policyType));
            if (policy.coverageStatus) fields.push(this.createFieldItem('Coverage Status', policy.coverageStatus));
        }
        
        if (data.estimatedLoss) {
            fields.push(this.createFieldItem('Estimated Loss', `$${this.formatNumber(data.estimatedLoss)}`));
        }
        
        content.innerHTML = fields.join('') || '<div class="no-data">No extracted fields available</div>';
    }

    displayLevel2Analysis() {
        const content = document.getElementById('l2-content');
        const confidence = document.getElementById('l2-confidence');
        const data = this.claimData.level02_analysis;
        
        if (!data) {
            content.innerHTML = '<div class="no-data">Level 2 analysis not available</div>';
            confidence.textContent = '--';
            return;
        }
        
        // Update confidence
        const coverageConfidence = data.coverage_confidence || 0;
        confidence.textContent = `${Math.round(coverageConfidence * 100)}%`;
        confidence.className = `level-confidence ${this.getConfidenceClass(coverageConfidence)}`;
        
        // Build coverage decision display
        let html = '<div class="coverage-summary">';
        
        // Coverage Decision
        const decision = data.coverage_decision || 'PENDING';
        const decisionClass = this.getCoverageDecisionClass(decision);
        html += `
            <div class="coverage-decision-box ${decisionClass}">
                <h4>Coverage Decision</h4>
                <div class="decision-value">${decision.replace(/_/g, ' ')}</div>
            </div>
        `;
        
        // Justification
        if (data.justification) {
            html += `
                <div class="coverage-section">
                    <h4>Justification</h4>
                    <p>${data.justification}</p>
                </div>
            `;
        }
        
        // Policy Violations
        if (data.policy_violations && data.policy_violations.length > 0) {
            html += `
                <div class="coverage-section">
                    <h4>Policy Violations</h4>
                    <ul class="violation-list">
                        ${data.policy_violations.map(v => `<li>${v}</li>`).join('')}
                    </ul>
                </div>
            `;
        }
        
        // Coverage Details
        if (data.coverage_details) {
            html += `
                <div class="coverage-section">
                    <h4>Coverage Analysis</h4>
                    ${this.formatCoverageDetails(data.coverage_details)}
                </div>
            `;
        }
        
        html += '</div>';
        content.innerHTML = html;
    }

    displayLevel3Analysis() {
        const content = document.getElementById('l3-content');
        const confidence = document.getElementById('l3-confidence');
        const data = this.claimData.level03_analysis;
        
        if (!data) {
            content.innerHTML = '<div class="no-data">Level 3 analysis not available</div>';
            confidence.textContent = '--';
            return;
        }
        
        // Update confidence
        const faultConfidence = data.determination_confidence || 0;
        confidence.textContent = `${Math.round(faultConfidence * 100)}%`;
        confidence.className = `level-confidence ${this.getConfidenceClass(faultConfidence)}`;
        
        // Build fault determination display
        let html = '<div class="fault-summary">';
        
        // Fault Split
        if (data.fault_percentages) {
            html += '<div class="fault-split">';
            Object.entries(data.fault_percentages).forEach(([party, percentage]) => {
                html += `
                    <div class="fault-party">
                        <div class="party-info">
                            <span class="party-name">${party}</span>
                            <span class="fault-percentage">${percentage}%</span>
                        </div>
                        <div class="fault-bar">
                            <div class="fault-fill" style="width: ${percentage}%"></div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
        }
        
        // Legal Basis
        if (data.legal_basis) {
            html += `
                <div class="fault-section">
                    <h4>Legal Basis</h4>
                    <p>${data.legal_basis}</p>
                </div>
            `;
        }
        
        // Applied Rules
        if (data.applied_rules && data.applied_rules.length > 0) {
            html += `
                <div class="fault-section">
                    <h4>Applied Rules</h4>
                    <ul class="rule-list">
                        ${data.applied_rules.map(rule => `<li>${rule}</li>`).join('')}
                    </ul>
                </div>
            `;
        }
        
        html += '</div>';
        content.innerHTML = html;
    }

    displayLevel4Analysis() {
        const content = document.getElementById('l4-content');
        const confidence = document.getElementById('l4-confidence');
        const data = this.claimData.level04_analysis;
        
        if (!data) {
            content.innerHTML = '<div class="no-data">Level 4 analysis not available</div>';
            confidence.textContent = '--';
            return;
        }
        
        // Update confidence
        const quantumConfidence = data.calculation_confidence || 0;
        confidence.textContent = `${Math.round(quantumConfidence * 100)}%`;
        confidence.className = `level-confidence ${this.getConfidenceClass(quantumConfidence)}`;
        
        // Build quantum breakdown display
        let html = '<div class="quantum-summary">';
        
        // Main quantum amounts
        if (data.quantum_breakdown) {
            const breakdown = data.quantum_breakdown;
            
            // Total Damages Summary
            html += `
                <div class="quantum-item highlight-item">
                    <span class="quantum-label">Net Recoverable</span>
                    <span class="quantum-value">$${this.formatNumber(breakdown.net_recoverable)}</span>
                </div>
            `;
            
            // Special Damages
            html += `
                <div class="quantum-section">
                    <h4>Special Damages (Pecuniary)</h4>
                    <div class="quantum-breakdown-details">
                        <div class="breakdown-item">
                            <span class="breakdown-label">Medical & Rehabilitation</span>
                            <span class="breakdown-value">$${this.formatNumber(breakdown.cost_breakdown?.total_medical || 0)}</span>
                        </div>
                        <div class="breakdown-item">
                            <span class="breakdown-label">Income Loss</span>
                            <span class="breakdown-value">$${this.formatNumber(breakdown.income_breakdown?.total_income_loss || 0)}</span>
                        </div>
                        <div class="breakdown-item">
                            <span class="breakdown-label">Out of Pocket</span>
                            <span class="breakdown-value">$${this.formatNumber(breakdown.out_of_pocket || 0)}</span>
                        </div>
                        <div class="breakdown-item">
                            <span class="breakdown-label">Property Damage</span>
                            <span class="breakdown-value">$${this.formatNumber(breakdown.property_damage || 0)}</span>
                        </div>
                    </div>
                </div>
            `;
            
            // General Damages
            html += `
                <div class="quantum-section">
                    <h4>General Damages (Non-Pecuniary)</h4>
                    <div class="quantum-breakdown-details">
                        <div class="breakdown-item">
                            <span class="breakdown-label">Pain & Suffering</span>
                            <span class="breakdown-value">$${this.formatNumber(breakdown.pain_and_suffering || 0)}</span>
                        </div>
                        <div class="breakdown-item">
                            <span class="breakdown-label">Loss of Amenities</span>
                            <span class="breakdown-value">$${this.formatNumber(breakdown.loss_of_amenities || 0)}</span>
                        </div>
                    </div>
                </div>
            `;
            
            // Reductions
            if (breakdown.fault_reduction_amount > 0) {
                html += `
                    <div class="quantum-section">
                        <h4>Reductions</h4>
                        <div class="quantum-breakdown-details">
                            <div class="breakdown-item">
                                <span class="breakdown-label">Fault Reduction (${breakdown.fault_reduction_percentage}%)</span>
                                <span class="breakdown-value">-$${this.formatNumber(breakdown.fault_reduction_amount)}</span>
                            </div>
                        </div>
                    </div>
                `;
            }
        }
        
        // Settlement Range
        if (data.settlement_range_low && data.settlement_range_high) {
            html += `
                <div class="settlement-range">
                    <h4>Settlement Range</h4>
                    <div class="range-display">
                        <span class="range-low">$${this.formatNumber(data.settlement_range_low)}</span>
                        <span class="range-separator">to</span>
                        <span class="range-high">$${this.formatNumber(data.settlement_range_high)}</span>
                    </div>
                    <div class="recommended-settlement">
                        Recommended: <strong>$${this.formatNumber(data.recommended_settlement)}</strong>
                    </div>
                </div>
            `;
        }
        
        html += '</div>';
        content.innerHTML = html;
    }

    displayRecommendations() {
        const content = document.getElementById('recommendations-content');
        const l4Data = this.claimData.level04_analysis;
        const l3Data = this.claimData.level03_analysis;
        const l2Data = this.claimData.level02_analysis;
        
        let html = '<div class="recommendations-list">';
        
        // Primary recommendation based on exit path
        if (l4Data?.exit_path) {
            const exitPath = l4Data.exit_path;
            let recommendation = '';
            let className = '';
            
            switch (exitPath) {
                case 'SETTLE':
                    recommendation = `Approve settlement at $${this.formatNumber(l4Data.recommended_settlement)}`;
                    className = 'primary';
                    break;
                case 'NEGOTIATE':
                    recommendation = 'Enter settlement negotiations';
                    className = 'alternative';
                    break;
                case 'MEDIATE':
                    recommendation = 'Proceed to mediation';
                    className = 'alternative';
                    break;
                case 'LITIGATE':
                    recommendation = 'Prepare for litigation';
                    className = 'escalation';
                    break;
                default:
                    recommendation = 'Review required';
                    className = 'alternative';
            }
            
            html += `
                <div class="recommendation-item ${className}">
                    <span class="recommendation-label">Primary Action:</span>
                    <span class="recommendation-text">${recommendation}</span>
                </div>
            `;
        }
        
        // Additional recommendations
        if (l4Data?.requires_medical_review) {
            html += `
                <div class="recommendation-item escalation">
                    <span class="recommendation-label">Medical Review:</span>
                    <span class="recommendation-text">Independent medical examination recommended</span>
                </div>
            `;
        }
        
        if (l3Data?.requires_investigation) {
            html += `
                <div class="recommendation-item escalation">
                    <span class="recommendation-label">Investigation:</span>
                    <span class="recommendation-text">Further investigation required for fault determination</span>
                </div>
            `;
        }
        
        if (l2Data?.requires_legal_review) {
            html += `
                <div class="recommendation-item escalation">
                    <span class="recommendation-label">Legal Review:</span>
                    <span class="recommendation-text">Complex coverage issues require legal counsel</span>
                </div>
            `;
        }
        
        html += '</div>';
        content.innerHTML = html;
    }

    createFieldItem(label, value) {
        return `
            <div class="field-item" data-field="${label.toLowerCase().replace(/\s+/g, '_')}">
                <span class="field-label">${label}:</span>
                <span class="field-value">${value}</span>
            </div>
        `;
    }

    getConfidenceClass(confidence) {
        if (confidence >= 0.8) return 'confidence-high';
        if (confidence >= 0.6) return 'confidence-medium';
        return 'confidence-low';
    }

    getCoverageDecisionClass(decision) {
        switch (decision) {
            case 'COVERED': return 'decision-covered';
            case 'NOT_COVERED': return 'decision-not-covered';
            case 'INFORMATION_REQUIRED': return 'decision-pending';
            default: return 'decision-unknown';
        }
    }

    formatNumber(num) {
        if (!num && num !== 0) return '0';
        return new Intl.NumberFormat('en-US').format(num);
    }

    formatCoverageDetails(details) {
        if (typeof details === 'string') return `<p>${details}</p>`;
        if (typeof details === 'object') {
            return `<pre class="json-viewer">${JSON.stringify(details, null, 2)}</pre>`;
        }
        return '<p>No details available</p>';
    }

    updateActionButtons() {
        const approveBtn = document.getElementById('approve-btn');
        const escalateBtn = document.getElementById('escalate-btn');
        
        // Enable buttons based on analysis completeness
        const hasL4 = !!this.claimData.level04_analysis;
        const hasL3 = !!this.claimData.level03_analysis;
        const hasL2 = !!this.claimData.level02_analysis;
        
        // Enable approve button if we have quantum calculation and it's ready to settle
        if (hasL4 && this.claimData.level04_analysis.exit_path === 'SETTLE') {
            approveBtn.disabled = false;
            approveBtn.textContent = `Approve Settlement ($${this.formatNumber(this.claimData.level04_analysis.recommended_settlement)})`;
        } else {
            approveBtn.disabled = true;
        }
        
        // Enable escalate button if any analysis requires review
        const requiresReview = 
            this.claimData.level02_analysis?.requires_human_review ||
            this.claimData.level03_analysis?.requires_investigation ||
            this.claimData.level04_analysis?.requires_medical_review ||
            this.claimData.level04_analysis?.requires_actuarial_review;
            
        escalateBtn.disabled = !requiresReview;
    }

    async handleApprove() {
        const settlement = this.claimData.level04_analysis?.recommended_settlement;
        const confirmed = confirm(`Are you sure you want to approve this settlement for $${this.formatNumber(settlement)}?`);
        
        if (confirmed) {
            try {
                // TODO: Send approval to backend API
                this.showNotification('Settlement approved successfully!', 'success');
                this.updateClaimStatus('Approved');
            } catch (error) {
                this.showNotification('Failed to approve settlement', 'error');
            }
        }
    }

    async handleEscalate() {
        const reason = prompt('Please provide a reason for escalation:');
        if (reason) {
            try {
                // TODO: Send escalation to backend API
                this.showNotification('Claim escalated for senior review', 'info');
                this.updateClaimStatus('Escalated');
            } catch (error) {
                this.showNotification('Failed to escalate claim', 'error');
            }
        }
    }

    handleExport() {
        const reportData = {
            claim_reference: this.claimReference,
            export_timestamp: new Date().toISOString(),
            claim_details: this.claimData.claim_details,
            level01_analysis: this.claimData.level01_analysis,
            level02_analysis: this.claimData.level02_analysis,
            level03_analysis: this.claimData.level03_analysis,
            level04_analysis: this.claimData.level04_analysis,
            documents: this.claimData.documents?.map(d => ({
                name: d.name,
                type: d.content_type,
                size: d.size
            }))
        };
        
        const blob = new Blob([JSON.stringify(reportData, null, 2)], { 
            type: 'application/json' 
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `claim-review-${this.claimReference}-${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showNotification('Report exported successfully!', 'success');
    }

    updateClaimStatus(status) {
        const statusElement = document.getElementById('claim-status');
        statusElement.textContent = status;
        statusElement.className = `status status--${status.toLowerCase() === 'approved' ? 'success' : 'warning'}`;
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification--${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-surface);
            border: 1px solid var(--color-border);
            border-radius: var(--radius-base);
            padding: var(--space-16);
            box-shadow: var(--shadow-lg);
            z-index: 10000;
            max-width: 300px;
            animation: slideIn 0.3s ease-out;
        `;
        
        const borderColor = {
            success: 'var(--color-success)',
            error: 'var(--color-error)',
            info: 'var(--color-info)',
            warning: 'var(--color-warning)'
        }[type];
        
        notification.style.borderLeftColor = borderColor;
        notification.style.borderLeftWidth = '4px';
        
        notification.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" 
                        style="background: none; border: none; font-size: 18px; cursor: pointer; margin-left: 12px;">
                    ×
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Remove notification after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    // Explainable AI Integration
    async generateExplanation() {
        if (!this.claimData) {
            this.showError('No claim data available for explanation');
            return;
        }

        const generateBtn = document.getElementById('generate-explanation-btn');
        const originalText = generateBtn.textContent;
        generateBtn.textContent = 'Generating...';
        generateBtn.disabled = true;

        try {
            // Extract features from all analysis levels
            const features = this.extractFeaturesForExplanation();
            
            // Extract document annotations from OCR and analysis
            const documentAnnotations = this.extractDocumentAnnotations();
            
            // Determine overall prediction/decision
            const prediction = this.determinePrediction();
            
            // Generate comprehensive explanation
            const explanationData = explainabilityEngine.explainDecision(
                features, 
                prediction, 
                documentAnnotations
            );

            // Display the explanation
            this.displayExplanation(explanationData);

            // Update confidence indicator
            document.getElementById('explain-confidence').textContent = 
                `${Math.round(explanationData.combinedConfidence * 100)}%`;
            document.getElementById('explain-confidence').className = 
                `level-confidence ${this.getConfidenceClass(explanationData.combinedConfidence)}`;

        } catch (error) {
            console.error('Error generating explanation:', error);
            this.showError('Failed to generate AI explanation');
        } finally {
            generateBtn.textContent = originalText;
            generateBtn.disabled = false;
        }
    }

    extractFeaturesForExplanation() {
        const features = [];

        // Extract features directly from Supabase-stored analysis results
        // Level 1 analysis (from claims.01_level_analysis column)
        if (this.claimData.level01_analysis) {
            const l1 = this.claimData.level01_analysis;
            
            if (l1.claimDetails) {
                features.push({
                    name: 'claim_amount',
                    value: l1.claimDetails.estimatedAmount || 'Not specified',
                    confidence: l1.confidenceScore || 0.8,
                    importance: 0.9,
                    source: 'Supabase: Level 1 extraction',
                    supabaseColumn: '01_level_analysis'
                });

                features.push({
                    name: 'incident_date',
                    value: l1.claimDetails.incidentDate || 'Not specified',
                    confidence: l1.confidenceScore || 0.8,
                    importance: 0.7,
                    source: 'Level 1 extraction'
                });

                features.push({
                    name: 'claimant_name',
                    value: l1.claimDetails.claimantName || 'Not specified',
                    confidence: l1.confidenceScore || 0.8,
                    importance: 0.6,
                    source: 'Level 1 extraction'
                });
            }

            if (l1.policyDetails) {
                features.push({
                    name: 'policy_number',
                    value: l1.policyDetails.policyNumber || 'Not found',
                    confidence: l1.confidenceScore || 0.8,
                    importance: 0.95,
                    source: 'Level 1 extraction'
                });
            }
        }

        // Level 2 analysis (from claims.02_level_analysis column)
        if (this.claimData.level02_analysis) {
            const l2 = this.claimData.level02_analysis;
            
            features.push({
                name: 'coverage_decision',
                value: l2.coverageDecision || 'Unknown',
                confidence: l2.confidenceScore || 0.8,
                importance: 1.0,
                source: 'Supabase: Level 2 coverage analysis',
                supabaseColumn: '02_level_analysis'
            });

            if (l2.policyAnalysis) {
                features.push({
                    name: 'policy_in_force',
                    value: l2.policyAnalysis.policyInForce ? 'Yes' : 'No',
                    confidence: l2.confidenceScore || 0.8,
                    importance: 0.9,
                    source: 'Level 2 policy analysis'
                });
            }
        }

        // From Level 3 analysis
        if (this.claimData.level03_analysis) {
            const l3 = this.claimData.level03_analysis;
            
            if (l3.fault_determination) {
                features.push({
                    name: 'fault_percentage',
                    value: `${l3.fault_determination.fault_percentage || 0}%`,
                    confidence: l3.fault_determination.confidence || 0.8,
                    importance: 0.85,
                    source: 'Level 3 fault determination'
                });
            }
        }

        // From Level 4 analysis
        if (this.claimData.level04_analysis) {
            const l4 = this.claimData.level04_analysis;
            
            if (l4.total_settlement_value) {
                features.push({
                    name: 'settlement_amount',
                    value: `$${this.formatNumber(l4.total_settlement_value)}`,
                    confidence: l4.calculation_confidence || 0.8,
                    importance: 1.0,
                    source: 'Level 4 quantum calculation'
                });
            }
        }

        return features;
    }

    extractDocumentAnnotations() {
        const annotations = [];

        /*
         * Extract document annotations directly from Supabase attachments table
         * Data source: attachments.ocr_text column
         * No additional API calls needed - all data already fetched
         * 
         * Supabase Schema:
         * - attachments.claim_reference (links to claims)
         * - attachments.ocr_text (contains extracted text)
         * - attachments.file_name (document name)
         * - attachments.original_filename (original file)
         */
        if (this.claimData.attachments) {
            this.claimData.attachments.forEach((attachment, index) => {
                if (attachment.ocr_text) {
                    // Create mock annotations for key extracted fields
                    const text = attachment.ocr_text;
                    
                    // Look for policy numbers
                    const policyMatch = text.match(/policy\s*(?:number|#)?\s*:?\s*([A-Z0-9-]+)/i);
                    if (policyMatch) {
                        annotations.push({
                            fieldName: 'policy_number',
                            extractedText: policyMatch[1],
                            confidence: 0.9,
                            coordinates: { x: 100 + (index * 50), y: 150 + (index * 30), width: 100, height: 20 },
                            explanation: 'Policy number extracted from document text',
                            shapContribution: 0.3
                        });
                    }

                    // Look for dates
                    const dateMatch = text.match(/(\d{1,2}[-\/]\d{1,2}[-\/]\d{2,4})/);
                    if (dateMatch) {
                        annotations.push({
                            fieldName: 'incident_date',
                            extractedText: dateMatch[1],
                            confidence: 0.8,
                            coordinates: { x: 120 + (index * 50), y: 200 + (index * 30), width: 80, height: 20 },
                            explanation: 'Incident date identified in document',
                            shapContribution: 0.2
                        });
                    }

                    // Look for dollar amounts
                    const amountMatch = text.match(/\$([0-9,]+(?:\.\d{2})?)/);
                    if (amountMatch) {
                        annotations.push({
                            fieldName: 'claim_amount',
                            extractedText: amountMatch[0],
                            confidence: 0.85,
                            coordinates: { x: 140 + (index * 50), y: 250 + (index * 30), width: 90, height: 20 },
                            explanation: 'Monetary amount found in claim documents',
                            shapContribution: 0.4
                        });
                    }
                }
            });
        }

        return annotations;
    }

    determinePrediction() {
        // Determine overall prediction based on all analysis levels
        if (this.claimData.level02_analysis && this.claimData.level02_analysis.coverageDecision) {
            return `Coverage: ${this.claimData.level02_analysis.coverageDecision}`;
        }
        
        if (this.claimData.level04_analysis && this.claimData.level04_analysis.total_settlement_value) {
            return `Settlement: $${this.formatNumber(this.claimData.level04_analysis.total_settlement_value)}`;
        }

        return 'Analysis in progress';
    }

    displayExplanation(explanationData) {
        const container = document.getElementById('explainability-container');
        
        // Clear existing content
        container.innerHTML = '';

        // Create and display the explanation panel
        const explanationPanel = explainabilityEngine.createExplanationPanel(
            explanationData, 
            'explainability-container'
        );

        // Add document annotations to the document viewer if available
        if (explanationData.annotations && explanationData.annotations.length > 0) {
            this.addDocumentAnnotations(explanationData.annotations);
        }

        // Show explanation quality metrics
        this.displayExplanationMetrics(explanationData);
    }

    addDocumentAnnotations(annotations) {
        // Find the document display area
        const documentDisplay = document.getElementById('document-display');
        if (!documentDisplay) return;

        // Attach explainability engine to document viewer
        explainabilityEngine.attachToDocument(documentDisplay);

        // Add all annotations
        annotations.forEach(annotation => {
            explainabilityEngine.addDocumentAnnotation(annotation);
        });

        // Add a note about the annotations
        const annotationNote = document.createElement('div');
        annotationNote.className = 'annotation-note';
        annotationNote.innerHTML = `
            <p><strong>📍 Document Annotations:</strong> ${annotations.length} AI-highlighted regions showing 
            evidence used in the decision. Click on highlighted areas for details.</p>
        `;
        annotationNote.style.cssText = `
            background: #e3f2fd; 
            padding: 0.75rem; 
            border-radius: 4px; 
            margin-bottom: 1rem; 
            border-left: 4px solid #2196f3;
            font-size: 0.9rem;
        `;
        
        documentDisplay.insertBefore(annotationNote, documentDisplay.firstChild);
    }

    displayExplanationMetrics(explanationData) {
        const metricsHtml = `
            <div class="explanation-metrics" style="margin-top: 1.5rem; padding: 1rem; background: var(--info-background, #f5f5f5); border-radius: 6px;">
                <h4>Explanation Quality Metrics</h4>
                <div class="metrics-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem; margin-top: 0.75rem;">
                    <div class="metric-item">
                        <div class="metric-label" style="font-size: 0.8rem; color: #666;">Combined Confidence</div>
                        <div class="metric-value" style="font-size: 1.2rem; font-weight: bold;">${Math.round(explanationData.combinedConfidence * 100)}%</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label" style="font-size: 0.8rem; color: #666;">Explanation Quality</div>
                        <div class="metric-value" style="font-size: 1.2rem; font-weight: bold;">${Math.round(explanationData.explanationQuality * 100)}%</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label" style="font-size: 0.8rem; color: #666;">Features Analyzed</div>
                        <div class="metric-value" style="font-size: 1.2rem; font-weight: bold;">${explanationData.shapExplanation ? explanationData.shapExplanation.features.length : 0}</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label" style="font-size: 0.8rem; color: #666;">Document Evidence</div>
                        <div class="metric-value" style="font-size: 1.2rem; font-weight: bold;">${explanationData.annotations ? explanationData.annotations.length : 0} regions</div>
                    </div>
                </div>
            </div>
        `;

        const container = document.getElementById('explainability-container');
        container.insertAdjacentHTML('beforeend', metricsHtml);
    }

    // Cleanup methods for production deployment
    destroy() {
        // Clean up all subscriptions and intervals
        this.cleanupRealtimeSubscription();
        
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
        }
        
        // Remove event listeners
        window.removeEventListener('popstate', this.checkForUrlClaimReference);
        window.removeEventListener('resize', this.updateMobileView);
        
        // Clean up explainability engine
        if (window.explainabilityEngine) {
            window.explainabilityEngine = null;
        }
        
        console.log('ZurichClaimsReviewApp cleaned up');
    }



    displayDocument(index) {
        const attachments = this.claimData?.attachments || [];
        const displayElement = document.getElementById('document-display');
        
        if (!displayElement) return;
        
        if (!attachments[index]) {
            displayElement.innerHTML = '<div class="no-data">Document not found</div>';
            return;
        }

        const attachment = attachments[index];
        const fileName = attachment.file_name || attachment.name || 'Unknown Document';
        const contentType = attachment.content_type || attachment.mimetype || 'application/octet-stream';
        const storageUrl = this.buildStorageUrl(this.claimReference, fileName);
        
        console.log(`📄 Displaying document: ${fileName} (${contentType})`);

        // Check if it's a PDF
        if (contentType.includes('pdf')) {
            displayElement.innerHTML = `
                <div class="document-viewer">
                    <div class="document-header">
                        <h4>${this.escapeHtml(fileName)}</h4>
                        <a href="${storageUrl}" target="_blank" class="btn btn--sm">Open in New Tab</a>
                    </div>
                    <iframe src="${storageUrl}" class="document-iframe" 
                            title="${this.escapeHtml(fileName)}"></iframe>
                </div>
            `;
        } 
        // Check if it's an image
        else if (contentType.includes('image')) {
            displayElement.innerHTML = `
                <div class="document-viewer">
                    <div class="document-header">
                        <h4>${this.escapeHtml(fileName)}</h4>
                        <a href="${storageUrl}" target="_blank" class="btn btn--sm">Open in New Tab</a>
                    </div>
                    <div style="text-align: center; padding: var(--space-24);">
                        <img src="${storageUrl}" alt="${this.escapeHtml(fileName)}" 
                             style="max-width: 100%; height: auto; border-radius: var(--radius-base); box-shadow: var(--shadow-base);">
                    </div>
                </div>
            `;
        }
        // For other file types or if OCR text is available
        else {
            if (attachment.ocr_text) {
                displayElement.innerHTML = `
                    <div class="document-viewer">
                        <div class="document-header">
                            <h4>${this.escapeHtml(fileName)} - OCR Text</h4>
                            <a href="${storageUrl}" target="_blank" class="btn btn--sm">Download Original</a>
                        </div>
                        <div class="document-body">
                            <div class="json-viewer">
                                ${this.escapeHtml(attachment.ocr_text)}
                            </div>
                        </div>
                    </div>
                `;
            } else {
                displayElement.innerHTML = `
                    <div class="document-viewer">
                        <div class="document-header">
                            <h4>${this.escapeHtml(fileName)}</h4>
                        </div>
                        <div class="no-data">
                            <p>Preview not available for this file type.</p>
                            <a href="${storageUrl}" target="_blank" class="btn btn--primary">
                                Download ${this.escapeHtml(fileName)}
                            </a>
                        </div>
                    </div>
                `;
            }
        }
    }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .analysis-level {
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .analysis-level:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
    
    .analysis-level.expanded {
        border-color: var(--color-primary);
    }
    
    .level-confidence {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.875rem;
        font-weight: 600;
    }
    
    .confidence-high { 
        background: rgba(76, 175, 80, 0.1); 
        color: #4CAF50; 
    }
    
    .confidence-medium { 
        background: rgba(255, 152, 0, 0.1); 
        color: #FF9800; 
    }
    
    .confidence-low { 
        background: rgba(244, 67, 54, 0.1); 
        color: #F44336; 
    }
    
    .decision-covered { background: rgba(76, 175, 80, 0.1); }
    .decision-not-covered { background: rgba(244, 67, 54, 0.1); }
    .decision-pending { background: rgba(255, 152, 0, 0.1); }
    
    .coverage-decision-box {
        padding: var(--space-16);
        border-radius: var(--radius-base);
        margin-bottom: var(--space-16);
        text-align: center;
    }
    
    .decision-value {
        font-size: 1.25rem;
        font-weight: 700;
        margin-top: var(--space-8);
    }
    
    .fault-bar {
        height: 8px;
        background: var(--color-bg);
        border-radius: 4px;
        overflow: hidden;
        margin-top: 4px;
    }
    
    .fault-fill {
        height: 100%;
        background: var(--color-primary);
        transition: width 0.3s ease;
    }
    
    .settlement-range {
        background: var(--color-bg);
        padding: var(--space-16);
        border-radius: var(--radius-base);
        text-align: center;
        margin-top: var(--space-16);
    }
    
    .range-display {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--space-12);
        margin: var(--space-12) 0;
        font-size: 1.125rem;
    }
    
    .range-low, .range-high {
        font-weight: 600;
        color: var(--color-primary);
    }
    
    .recommended-settlement {
        font-size: 1.25rem;
        color: var(--color-success);
    }
`;
document.head.appendChild(style);

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.claimsReviewApp = new ZurichClaimsReviewApp();
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.claimsReviewApp) {
        window.claimsReviewApp.destroy();
    }
});

// Add keyboard shortcuts
document.addEventListener('keydown', (e) => {
    if (e.altKey) {
        switch (e.key) {
            case 't':
                e.preventDefault();
                document.getElementById('theme-toggle').click();
                break;
            case 'l':
                e.preventDefault();
                document.getElementById('claim-reference').focus();
                break;
        }
    }
}); 