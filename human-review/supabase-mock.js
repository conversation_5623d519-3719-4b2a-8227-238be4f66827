/**
 * Local Supabase Mock Client
 * Fallback when CDN fails to load
 */

// Mock Supabase client for testing
window.supabaseMock = {
    createClient: function(url, key) {
        console.log('🔧 Using local Supabase mock client');
        
        return {
            from: function(table) {
                return {
                    select: function(columns) {
                        return {
                            eq: function(field, value) {
                                return {
                                    single: function() {
                                        return Promise.resolve({
                                            data: {
                                                claim_reference: value,
                                                status: 'active',
                                                priority: 'high',
                                                estimated_value: 50000,
                                                assigned_agent: 'AI Assistant',
                                                created_at: new Date().toISOString(),
                                                updated_at: new Date().toISOString()
                                            },
                                            error: null
                                        });
                                    }
                                };
                            }
                        };
                    }
                };
            },
            table: function(table) {
                return {
                    select: function(columns) {
                        return {
                            eq: function(field, value) {
                                return Promise.resolve({
                                    data: [
                                        {
                                            id: 1,
                                            claim_reference: value,
                                            file_name: 'claim_document.pdf',
                                            file_type: 'pdf',
                                            file_size: 1024000,
                                            created_at: new Date().toISOString()
                                        }
                                    ],
                                    error: null
                                });
                            }
                        };
                    }
                };
            }
        };
    }
};

// Auto-initialize if global supabase is not available
if (typeof window.supabase === 'undefined') {
    console.log('🔄 CDN Supabase not available, using local mock');
    window.supabase = window.supabaseMock;
} 