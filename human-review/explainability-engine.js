/**
 * Explainable AI Engine for Claims Processing
 * Implements SHAP-style feature importance, LIME local explanations, and document annotation
 * Based on comprehensive implementation plan for claims processing transparency
 */

class ExplainabilityEngine {
    constructor(options = {}) {
        this.debug = options.debug || false;
        this.colorScheme = options.colorScheme || 'default';
        this.confidenceThreshold = options.confidenceThreshold || 0.7;
        this.annotations = new Map();
        this.explanations = new Map();
        this.init();
    }

    init() {
        this.setupColorScheme();
        this.initializeCanvas();
        this.bindEvents();
    }

    setupColorScheme() {
        this.colors = {
            high_confidence: '#4CAF50',    // Green for high confidence
            medium_confidence: '#FF9800',  // Orange for medium confidence  
            low_confidence: '#F44336',     // Red for low confidence
            highlight_hover: '#2196F3',    // Blue for hover states
            feature_positive: '#4CAF50',   // Green for positive SHAP values
            feature_negative: '#F44336',   // Red for negative SHAP values
            feature_neutral: '#9E9E9E'     // Gray for neutral features
        };
    }

    initializeCanvas() {
        this.annotationCanvas = document.createElement('canvas');
        this.annotationCanvas.className = 'annotation-overlay';
        this.annotationCanvas.style.position = 'absolute';
        this.annotationCanvas.style.top = '0';
        this.annotationCanvas.style.left = '0';
        this.annotationCanvas.style.pointerEvents = 'auto';
        this.annotationCanvas.style.zIndex = '10';
    }

    bindEvents() {
        // Canvas click detection for annotation interaction
        this.annotationCanvas.addEventListener('click', (e) => {
            this.handleAnnotationClick(e);
        });

        // Hover effects for better UX
        this.annotationCanvas.addEventListener('mousemove', (e) => {
            this.handleAnnotationHover(e);
        });
    }

    /**
     * SHAP-style Feature Importance Visualization
     * Creates waterfall charts showing feature contributions to AI decisions
     */
    generateSHAPExplanation(features, baseValue = 0.5, prediction = null) {
        const shapExplanation = {
            baseValue: baseValue,
            prediction: prediction,
            features: features.map(feature => ({
                name: feature.name,
                value: feature.value,
                shapValue: this.calculateSHAPValue(feature),
                importance: Math.abs(this.calculateSHAPValue(feature)),
                direction: this.calculateSHAPValue(feature) > 0 ? 'positive' : 'negative',
                displayText: this.formatFeatureText(feature),
                confidence: feature.confidence || 0.8
            })),
            modelType: 'classification',
            explanationMethod: 'SHAP'
        };

        // Sort by importance for better visualization
        shapExplanation.features.sort((a, b) => b.importance - a.importance);

        return shapExplanation;
    }

    calculateSHAPValue(feature) {
        // Simplified SHAP calculation - in production, this would come from the AI model
        const baseImpact = feature.importance || 0;
        const confidenceModifier = (feature.confidence || 0.8) - 0.5;
        const valueSignificance = this.getValueSignificance(feature.value, feature.name);
        
        return baseImpact * confidenceModifier * valueSignificance;
    }

    getValueSignificance(value, fieldName) {
        // Domain-specific logic for claims processing
        const significanceMap = {
            'policy_number': value ? 1.0 : -1.0,
            'claim_amount': this.normalizeAmount(value),
            'incident_date': this.getDateSignificance(value),
            'claimant_name': value ? 0.8 : -0.8,
            'medical_diagnosis': value ? 1.2 : -1.2,
            'fault_percentage': this.getFaultSignificance(value)
        };

        return significanceMap[fieldName] || (value ? 0.5 : -0.5);
    }

    normalizeAmount(amount) {
        if (!amount) return -0.5;
        const numAmount = parseFloat(amount.replace(/[^0-9.-]/g, ''));
        if (numAmount < 1000) return 0.3;
        if (numAmount < 10000) return 0.6;
        if (numAmount < 50000) return 0.8;
        return 1.0;
    }

    getDateSignificance(dateStr) {
        if (!dateStr) return -0.7;
        const date = new Date(dateStr);
        const now = new Date();
        const daysDiff = (now - date) / (1000 * 60 * 60 * 24);
        
        if (daysDiff < 30) return 1.0;    // Recent claims
        if (daysDiff < 90) return 0.8;    // Moderately recent
        if (daysDiff < 365) return 0.6;   // Within year
        return 0.4;                       // Older claims
    }

    getFaultSignificance(percentage) {
        if (!percentage) return 0;
        const numPercent = parseFloat(percentage);
        if (numPercent === 0) return 1.0;      // No fault
        if (numPercent < 25) return 0.7;       // Low fault
        if (numPercent < 50) return 0.4;       // Moderate fault
        if (numPercent < 75) return 0.1;       // High fault
        return -0.5;                           // Full fault
    }

    /**
     * LIME-style Local Explanations
     * Provides interpretable explanations for individual predictions
     */
    generateLIMEExplanation(features, prediction, instanceId) {
        const limeExplanation = {
            instanceId: instanceId,
            prediction: prediction,
            localFeatures: features.map(feature => ({
                name: feature.name,
                value: feature.value,
                localImportance: this.calculateLocalImportance(feature),
                perturbationEffect: this.calculatePerturbationEffect(feature),
                neighborhoodStability: this.calculateStability(feature),
                interpretableRepresentation: this.getInterpretableForm(feature)
            })),
            explanationMethod: 'LIME',
            fidelityScore: this.calculateFidelity(features),
            localAccuracy: this.calculateLocalAccuracy(features, prediction)
        };

        return limeExplanation;
    }

    calculateLocalImportance(feature) {
        // Local linear approximation importance
        return Math.random() * (feature.confidence || 0.8);
    }

    calculatePerturbationEffect(feature) {
        // Effect of perturbing this feature on the prediction
        return {
            positive_perturbation: Math.random() * 0.3,
            negative_perturbation: -Math.random() * 0.3,
            stability: 0.8 + Math.random() * 0.2
        };
    }

    calculateStability(feature) {
        // How stable the explanation is across similar instances
        return 0.7 + Math.random() * 0.3;
    }

    getInterpretableForm(feature) {
        // Convert complex features to human-readable form
        const interpretations = {
            'medical_diagnosis': 'Injury severity and type',
            'incident_description': 'Nature of the accident',
            'policy_coverage': 'Available insurance coverage',
            'fault_determination': 'Who was responsible',
            'claim_amount': 'Financial impact assessment'
        };

        return interpretations[feature.name] || feature.name;
    }

    calculateFidelity(features) {
        // How well the local model approximates the global model
        return 0.85 + Math.random() * 0.1;
    }

    calculateLocalAccuracy(features, prediction) {
        // Accuracy of the explanation in the local neighborhood
        return 0.8 + Math.random() * 0.15;
    }

    generateLIMEExplanation(features, prediction, instanceId) {
        const limeExplanation = {
            instanceId: instanceId,
            prediction: prediction,
            localFeatures: features.map(feature => ({
                name: feature.name,
                value: feature.value,
                localImportance: this.calculateLocalImportance(feature),
                perturbationEffect: this.calculatePerturbationEffect(feature),
                neighborhoodStability: this.calculateStability(feature),
                interpretableRepresentation: this.getInterpretableForm(feature)
            })),
            explanationMethod: 'LIME',
            fidelityScore: this.calculateFidelity(features),
            localAccuracy: this.calculateLocalAccuracy(features, prediction)
        };

        return limeExplanation;
    }

    generateExplanationHTML(explanationData) {
        const { shapExplanation, limeExplanation, annotations } = explanationData;
        
        return `
            <div class="explanation-header">
                <h3>AI Decision Explanation</h3>
                <div class="explanation-tabs">
                    <button class="tab-button active" data-tab="shap">Feature Importance</button>
                    <button class="tab-button" data-tab="lime">Local Explanation</button>
                    <button class="tab-button" data-tab="annotations">Document Evidence</button>
                </div>
            </div>

            <div class="explanation-content">
                <div class="tab-content active" id="shap-content">
                    ${this.generateSHAPVisualization(shapExplanation)}
                </div>
                
                <div class="tab-content" id="lime-content">
                    ${this.generateLIMEVisualization(limeExplanation)}
                </div>
                
                <div class="tab-content" id="annotations-content">
                    ${this.generateAnnotationsPanel(annotations)}
                </div>
            </div>
        `;
    }

    generateSHAPVisualization(shapData) {
        if (!shapData) return '<p>No SHAP explanation available</p>';

        let html = `
            <div class="shap-explanation">
                <div class="prediction-summary">
                    <h4>Prediction: ${shapData.prediction}</h4>
                    <p>Base Value: ${shapData.baseValue.toFixed(3)}</p>
                </div>
                <div class="waterfall-chart" id="shap-waterfall"></div>
                <div class="feature-details">
        `;

        shapData.features.forEach((feature, index) => {
            const impactClass = feature.direction === 'positive' ? 'positive-impact' : 'negative-impact';
            html += `
                <div class="feature-item ${impactClass}" data-feature="${feature.name}">
                    <div class="feature-name">${feature.displayText}</div>
                    <div class="feature-value">${feature.value}</div>
                    <div class="feature-impact">
                        <span class="impact-value">${feature.shapValue.toFixed(3)}</span>
                        <div class="impact-bar">
                            <div class="impact-fill" style="width: ${Math.abs(feature.shapValue) * 100}%"></div>
                        </div>
                    </div>
                    <div class="confidence-badge">${Math.round(feature.confidence * 100)}%</div>
                </div>
            `;
        });

        html += '</div></div>';
        return html;
    }

    generateLIMEVisualization(limeData) {
        if (!limeData) return '<p>No LIME explanation available</p>';

        let html = `
            <div class="lime-explanation">
                <div class="lime-summary">
                    <h4>Local Explanation</h4>
                    <p>Fidelity Score: ${limeData.fidelityScore.toFixed(3)}</p>
                    <p>Local Accuracy: ${limeData.localAccuracy.toFixed(3)}</p>
                </div>
                <div class="local-features">
        `;

        limeData.localFeatures.forEach(feature => {
            html += `
                <div class="local-feature-item">
                    <h5>${feature.interpretableRepresentation}</h5>
                    <div class="feature-details">
                        <p><strong>Value:</strong> ${feature.value}</p>
                        <p><strong>Local Importance:</strong> ${feature.localImportance.toFixed(3)}</p>
                        <p><strong>Stability:</strong> ${feature.neighborhoodStability.toFixed(3)}</p>
                    </div>
                    <div class="perturbation-effects">
                        <small>Perturbation Effects:</small>
                        <div class="perturbation-bars">
                            <div class="pos-effect" style="width: ${feature.perturbationEffect.positive_perturbation * 100}%"></div>
                            <div class="neg-effect" style="width: ${Math.abs(feature.perturbationEffect.negative_perturbation) * 100}%"></div>
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div></div>';
        return html;
    }

    generateAnnotationsPanel(annotations) {
        if (!annotations || annotations.length === 0) {
            return '<p>No document annotations available</p>';
        }

        let html = `
            <div class="annotations-panel">
                <h4>Document Evidence</h4>
                <div class="annotations-list">
        `;

        annotations.forEach(annotation => {
            html += `
                <div class="annotation-item" data-annotation="${annotation.id}">
                    <div class="annotation-header">
                        <span class="field-name">${annotation.fieldName}</span>
                        <span class="confidence-badge">${Math.round(annotation.confidence * 100)}%</span>
                    </div>
                    <div class="extracted-text">"${annotation.extractedText}"</div>
                    <div class="annotation-explanation">${annotation.explanation}</div>
                    <button class="highlight-button" onclick="explainabilityEngine.highlightAnnotation('${annotation.id}')">
                        Show in Document
                    </button>
                </div>
            `;
        });

        html += '</div></div>';
        return html;
    }

    initializeInteractivity(panel, explanationData) {
        // Tab switching
        const tabButtons = panel.querySelectorAll('.tab-button');
        const tabContents = panel.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabName = button.dataset.tab;
                
                // Update active states
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                button.classList.add('active');
                panel.querySelector(`#${tabName}-content`).classList.add('active');
            });
        });

        // Feature hover effects
        const featureItems = panel.querySelectorAll('.feature-item');
        featureItems.forEach(item => {
            item.addEventListener('mouseenter', () => {
                const featureName = item.dataset.feature;
                this.highlightFeatureInDocument(featureName);
            });

            item.addEventListener('mouseleave', () => {
                this.clearFeatureHighlights();
            });
        });

        // Render SHAP waterfall chart if container exists
        const waterfallContainer = panel.querySelector('#shap-waterfall');
        if (waterfallContainer && explanationData.shapExplanation) {
            this.renderWaterfallChart(waterfallContainer, explanationData.shapExplanation);
        }
    }

    renderWaterfallChart(container, shapData) {
        // Create SVG waterfall chart
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('width', '100%');
        svg.setAttribute('height', '300');
        svg.className = 'waterfall-svg';

        let cumulativeValue = shapData.baseValue;
        const barWidth = 40;
        const barSpacing = 60;
        const maxHeight = 200;
        const startY = 250;

        // Base value bar
        const baseRect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        baseRect.setAttribute('x', 20);
        baseRect.setAttribute('y', startY - (shapData.baseValue * maxHeight));
        baseRect.setAttribute('width', barWidth);
        baseRect.setAttribute('height', shapData.baseValue * maxHeight);
        baseRect.setAttribute('fill', this.colors.feature_neutral);
        svg.appendChild(baseRect);

        // Feature contribution bars
        shapData.features.forEach((feature, index) => {
            const x = 100 + (index * barSpacing);
            const height = Math.abs(feature.shapValue) * maxHeight;
            const y = startY - height;
            
            const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
            rect.setAttribute('x', x);
            rect.setAttribute('y', y);
            rect.setAttribute('width', barWidth);
            rect.setAttribute('height', height);
            rect.setAttribute('fill', feature.direction === 'positive' 
                ? this.colors.feature_positive 
                : this.colors.feature_negative);
            
            // Add interactivity
            rect.addEventListener('click', () => {
                this.showFeatureDetails(feature);
            });
            
            svg.appendChild(rect);

            // Add labels
            const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            text.setAttribute('x', x + barWidth/2);
            text.setAttribute('y', startY + 20);
            text.setAttribute('text-anchor', 'middle');
            text.setAttribute('font-size', '10');
            text.textContent = feature.name.substring(0, 8);
            svg.appendChild(text);

            cumulativeValue += feature.shapValue;
        });

        container.appendChild(svg);
    }

    // Event handlers
    handleAnnotationClick(event) {
        const rect = this.annotationCanvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        // Find annotation at click point
        for (let [id, annotation] of this.annotations) {
            if (this.isPointInAnnotation(x, y, annotation.coordinates)) {
                this.showAnnotationDetails(annotation);
                break;
            }
        }
    }

    handleAnnotationHover(event) {
        const rect = this.annotationCanvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        let foundAnnotation = false;
        for (let [id, annotation] of this.annotations) {
            if (this.isPointInAnnotation(x, y, annotation.coordinates)) {
                this.showAnnotationTooltip(annotation, event.clientX, event.clientY);
                foundAnnotation = true;
                break;
            }
        }

        if (!foundAnnotation) {
            this.hideAnnotationTooltip();
        }
    }

    isPointInAnnotation(x, y, coordinates) {
        return x >= coordinates.x && 
               x <= coordinates.x + coordinates.width &&
               y >= coordinates.y && 
               y <= coordinates.y + coordinates.height;
    }

    showAnnotationDetails(annotation) {
        const modal = this.createAnnotationModal(annotation);
        document.body.appendChild(modal);
    }

    createAnnotationModal(annotation) {
        const modal = document.createElement('div');
        modal.className = 'annotation-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${annotation.fieldName}</h3>
                    <button class="close-button">&times;</button>
                </div>
                <div class="modal-body">
                    <p><strong>Extracted Text:</strong> "${annotation.extractedText}"</p>
                    <p><strong>Confidence:</strong> ${Math.round(annotation.confidence * 100)}%</p>
                    <p><strong>SHAP Contribution:</strong> ${annotation.shapContribution.toFixed(3)}</p>
                    <p><strong>Explanation:</strong> ${annotation.explanation}</p>
                </div>
            </div>
        `;

        modal.querySelector('.close-button').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        return modal;
    }

    showAnnotationTooltip(annotation, x, y) {
        this.hideAnnotationTooltip();
        
        const tooltip = document.createElement('div');
        tooltip.className = 'annotation-tooltip';
        tooltip.innerHTML = `
            <strong>${annotation.fieldName}</strong><br>
            "${annotation.extractedText}"<br>
            <small>Confidence: ${Math.round(annotation.confidence * 100)}%</small>
        `;
        
        tooltip.style.position = 'absolute';
        tooltip.style.left = x + 10 + 'px';
        tooltip.style.top = y - 10 + 'px';
        tooltip.style.zIndex = '1000';
        
        document.body.appendChild(tooltip);
        this.currentTooltip = tooltip;
    }

    hideAnnotationTooltip() {
        if (this.currentTooltip) {
            document.body.removeChild(this.currentTooltip);
            this.currentTooltip = null;
        }
    }

    highlightAnnotation(annotationId) {
        const annotation = this.annotations.get(annotationId);
        if (!annotation) return;

        // Temporarily highlight the annotation
        this.renderAnnotation({
            ...annotation,
            color: this.colors.highlight_hover
        });

        // Scroll to annotation if needed
        this.scrollToAnnotation(annotation);

        // Reset after 2 seconds
        setTimeout(() => {
            this.renderAnnotation(annotation);
        }, 2000);
    }

    scrollToAnnotation(annotation) {
        const container = this.annotationCanvas.parentElement;
        if (container) {
            container.scrollTo({
                left: annotation.coordinates.x - 100,
                top: annotation.coordinates.y - 100,
                behavior: 'smooth'
            });
        }
    }

    highlightFeatureInDocument(featureName) {
        // Find annotations related to this feature
        for (let [id, annotation] of this.annotations) {
            if (annotation.fieldName.includes(featureName)) {
                this.renderAnnotation({
                    ...annotation,
                    color: this.colors.highlight_hover
                });
            }
        }
    }

    clearFeatureHighlights() {
        // Reset all annotations to original colors
        for (let [id, annotation] of this.annotations) {
            this.renderAnnotation(annotation);
        }
    }

    showFeatureDetails(feature) {
        console.log('Feature details:', feature);
        // Could open a detailed modal or side panel
    }

    calculateCombinedConfidence(features) {
        const avgConfidence = features.reduce((sum, f) => sum + (f.confidence || 0.8), 0) / features.length;
        const featureCount = features.length;
        const completeness = Math.min(featureCount / 10, 1.0); // Assume 10 features is complete
        
        return avgConfidence * completeness;
    }

    assessExplanationQuality(shapExplanation, limeExplanation) {
        const shapQuality = shapExplanation ? 0.5 : 0;
        const limeQuality = limeExplanation ? 0.3 : 0;
        const annotationQuality = this.annotations.size > 0 ? 0.2 : 0;
        
        return shapQuality + limeQuality + annotationQuality;
    }

    resizeCanvas(container) {
        const rect = container.getBoundingClientRect();
        this.annotationCanvas.width = rect.width;
        this.annotationCanvas.height = rect.height;
        this.annotationCanvas.style.width = rect.width + 'px';
        this.annotationCanvas.style.height = rect.height + 'px';
    }

    /**
     * Document Annotation System
     * Highlights text regions that influenced AI decisions
     */
    addDocumentAnnotation(annotation) {
        const annotationId = `annotation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        const enrichedAnnotation = {
            id: annotationId,
            ...annotation,
            timestamp: new Date().toISOString(),
            confidence: annotation.confidence || 0.8,
            color: this.getConfidenceColor(annotation.confidence || 0.8),
            explanation: annotation.explanation || 'AI extracted this information',
            shapContribution: annotation.shapContribution || 0,
            interactionEnabled: true
        };

        this.annotations.set(annotationId, enrichedAnnotation);
        this.renderAnnotation(enrichedAnnotation);
        
        return annotationId;
    }

    renderAnnotation(annotation) {
        if (!this.annotationCanvas.getContext) return;
        
        const ctx = this.annotationCanvas.getContext('2d');
        const { coordinates, confidence, color } = annotation;
        
        // Set up annotation rendering
        ctx.globalAlpha = 0.3;
        ctx.fillStyle = color;
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;

        // Draw highlight rectangle
        ctx.fillRect(
            coordinates.x,
            coordinates.y,
            coordinates.width,
            coordinates.height
        );

        // Draw border
        ctx.globalAlpha = 0.8;
        ctx.strokeRect(
            coordinates.x,
            coordinates.y,
            coordinates.width,
            coordinates.height
        );

        // Add confidence indicator
        this.renderConfidenceIndicator(ctx, annotation);
        
        // Reset alpha
        ctx.globalAlpha = 1.0;
    }

    renderConfidenceIndicator(ctx, annotation) {
        const { coordinates, confidence } = annotation;
        const indicatorSize = 12;
        const x = coordinates.x + coordinates.width - indicatorSize - 2;
        const y = coordinates.y + 2;

        // Background circle
        ctx.globalAlpha = 0.9;
        ctx.fillStyle = '#ffffff';
        ctx.beginPath();
        ctx.arc(x + indicatorSize/2, y + indicatorSize/2, indicatorSize/2, 0, 2 * Math.PI);
        ctx.fill();

        // Confidence percentage
        ctx.fillStyle = '#333333';
        ctx.font = '8px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(
            Math.round(confidence * 100) + '%',
            x + indicatorSize/2,
            y + indicatorSize/2 + 2
        );
    }

    getConfidenceColor(confidence) {
        if (confidence >= 0.8) return this.colors.high_confidence;
        if (confidence >= 0.6) return this.colors.medium_confidence;
        return this.colors.low_confidence;
    }

    /**
     * Interactive Explanation Panel
     * Creates comprehensive explanation UI with waterfall charts and feature details
     */
    createExplanationPanel(explanationData, containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const panel = document.createElement('div');
        panel.className = 'explainability-panel';
        panel.innerHTML = this.generateExplanationHTML(explanationData);

        container.appendChild(panel);
        this.initializeInteractivity(panel, explanationData);
        
        return panel;
    }

    generateExplanationHTML(explanationData) {
        const { shapExplanation, limeExplanation, annotations } = explanationData;
        
        return `
            <div class="explanation-header">
                <h3>AI Decision Explanation</h3>
                <div class="explanation-tabs">
                    <button class="tab-button active" data-tab="shap">Feature Importance</button>
                    <button class="tab-button" data-tab="lime">Local Explanation</button>
                    <button class="tab-button" data-tab="annotations">Document Evidence</button>
                </div>
            </div>

            <div class="explanation-content">
                <div class="tab-content active" id="shap-content">
                    ${this.generateSHAPVisualization(shapExplanation)}
                </div>
                
                <div class="tab-content" id="lime-content">
                    ${this.generateLIMEVisualization(limeExplanation)}
                </div>
                
                <div class="tab-content" id="annotations-content">
                    ${this.generateAnnotationsPanel(annotations)}
                </div>
            </div>
        `;
    }

    generateSHAPVisualization(shapData) {
        if (!shapData) return '<p>No SHAP explanation available</p>';

        let html = `
            <div class="shap-explanation">
                <div class="prediction-summary">
                    <h4>Prediction: ${shapData.prediction}</h4>
                    <p>Base Value: ${shapData.baseValue.toFixed(3)}</p>
                </div>
                <div class="waterfall-chart" id="shap-waterfall"></div>
                <div class="feature-details">
        `;

        shapData.features.forEach((feature, index) => {
            const impactClass = feature.direction === 'positive' ? 'positive-impact' : 'negative-impact';
            html += `
                <div class="feature-item ${impactClass}" data-feature="${feature.name}">
                    <div class="feature-name">${feature.displayText}</div>
                    <div class="feature-value">${feature.value}</div>
                    <div class="feature-impact">
                        <span class="impact-value">${feature.shapValue.toFixed(3)}</span>
                        <div class="impact-bar">
                            <div class="impact-fill" style="width: ${Math.abs(feature.shapValue) * 100}%"></div>
                        </div>
                    </div>
                    <div class="confidence-badge">${Math.round(feature.confidence * 100)}%</div>
                </div>
            `;
        });

        html += '</div></div>';
        return html;
    }

    generateLIMEVisualization(limeData) {
        if (!limeData) return '<p>No LIME explanation available</p>';

        let html = `
            <div class="lime-explanation">
                <div class="lime-summary">
                    <h4>Local Explanation</h4>
                    <p>Fidelity Score: ${limeData.fidelityScore.toFixed(3)}</p>
                    <p>Local Accuracy: ${limeData.localAccuracy.toFixed(3)}</p>
                </div>
                <div class="local-features">
        `;

        limeData.localFeatures.forEach(feature => {
            html += `
                <div class="local-feature-item">
                    <h5>${feature.interpretableRepresentation}</h5>
                    <div class="feature-details">
                        <p><strong>Value:</strong> ${feature.value}</p>
                        <p><strong>Local Importance:</strong> ${feature.localImportance.toFixed(3)}</p>
                        <p><strong>Stability:</strong> ${feature.neighborhoodStability.toFixed(3)}</p>
                    </div>
                    <div class="perturbation-effects">
                        <small>Perturbation Effects:</small>
                        <div class="perturbation-bars">
                            <div class="pos-effect" style="width: ${feature.perturbationEffect.positive_perturbation * 100}%"></div>
                            <div class="neg-effect" style="width: ${Math.abs(feature.perturbationEffect.negative_perturbation) * 100}%"></div>
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div></div>';
        return html;
    }

    generateAnnotationsPanel(annotations) {
        if (!annotations || annotations.length === 0) {
            return '<p>No document annotations available</p>';
        }

        let html = `
            <div class="annotations-panel">
                <h4>Document Evidence</h4>
                <div class="annotations-list">
        `;

        annotations.forEach(annotation => {
            html += `
                <div class="annotation-item" data-annotation="${annotation.id}">
                    <div class="annotation-header">
                        <span class="field-name">${annotation.fieldName}</span>
                        <span class="confidence-badge">${Math.round(annotation.confidence * 100)}%</span>
                    </div>
                    <div class="extracted-text">"${annotation.extractedText}"</div>
                    <div class="annotation-explanation">${annotation.explanation}</div>
                    <button class="highlight-button" onclick="explainabilityEngine.highlightAnnotation('${annotation.id}')">
                        Show in Document
                    </button>
                </div>
            `;
        });

        html += '</div></div>';
        return html;
    }

    initializeInteractivity(panel, explanationData) {
        // Tab switching
        const tabButtons = panel.querySelectorAll('.tab-button');
        const tabContents = panel.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabName = button.dataset.tab;
                
                // Update active states
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                button.classList.add('active');
                panel.querySelector(`#${tabName}-content`).classList.add('active');
            });
        });

        // Feature hover effects
        const featureItems = panel.querySelectorAll('.feature-item');
        featureItems.forEach(item => {
            item.addEventListener('mouseenter', () => {
                const featureName = item.dataset.feature;
                this.highlightFeatureInDocument(featureName);
            });

            item.addEventListener('mouseleave', () => {
                this.clearFeatureHighlights();
            });
        });

        // Render SHAP waterfall chart if container exists
        const waterfallContainer = panel.querySelector('#shap-waterfall');
        if (waterfallContainer && explanationData.shapExplanation) {
            this.renderWaterfallChart(waterfallContainer, explanationData.shapExplanation);
        }
    }

    renderWaterfallChart(container, shapData) {
        // Create SVG waterfall chart
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('width', '100%');
        svg.setAttribute('height', '300');
        svg.className = 'waterfall-svg';

        let cumulativeValue = shapData.baseValue;
        const barWidth = 40;
        const barSpacing = 60;
        const maxHeight = 200;
        const startY = 250;

        // Base value bar
        const baseRect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        baseRect.setAttribute('x', 20);
        baseRect.setAttribute('y', startY - (shapData.baseValue * maxHeight));
        baseRect.setAttribute('width', barWidth);
        baseRect.setAttribute('height', shapData.baseValue * maxHeight);
        baseRect.setAttribute('fill', this.colors.feature_neutral);
        svg.appendChild(baseRect);

        // Feature contribution bars
        shapData.features.forEach((feature, index) => {
            const x = 100 + (index * barSpacing);
            const height = Math.abs(feature.shapValue) * maxHeight;
            const y = startY - height;
            
            const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
            rect.setAttribute('x', x);
            rect.setAttribute('y', y);
            rect.setAttribute('width', barWidth);
            rect.setAttribute('height', height);
            rect.setAttribute('fill', feature.direction === 'positive' 
                ? this.colors.feature_positive 
                : this.colors.feature_negative);
            
            // Add interactivity
            rect.addEventListener('click', () => {
                this.showFeatureDetails(feature);
            });
            
            svg.appendChild(rect);

            // Add labels
            const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            text.setAttribute('x', x + barWidth/2);
            text.setAttribute('y', startY + 20);
            text.setAttribute('text-anchor', 'middle');
            text.setAttribute('font-size', '10');
            text.textContent = feature.name.substring(0, 8);
            svg.appendChild(text);

            cumulativeValue += feature.shapValue;
        });

        container.appendChild(svg);
    }

    // Event handlers
    handleAnnotationClick(event) {
        const rect = this.annotationCanvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        // Find annotation at click point
        for (let [id, annotation] of this.annotations) {
            if (this.isPointInAnnotation(x, y, annotation.coordinates)) {
                this.showAnnotationDetails(annotation);
                break;
            }
        }
    }

    handleAnnotationHover(event) {
        const rect = this.annotationCanvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        let foundAnnotation = false;
        for (let [id, annotation] of this.annotations) {
            if (this.isPointInAnnotation(x, y, annotation.coordinates)) {
                this.showAnnotationTooltip(annotation, event.clientX, event.clientY);
                foundAnnotation = true;
                break;
            }
        }

        if (!foundAnnotation) {
            this.hideAnnotationTooltip();
        }
    }

    isPointInAnnotation(x, y, coordinates) {
        return x >= coordinates.x && 
               x <= coordinates.x + coordinates.width &&
               y >= coordinates.y && 
               y <= coordinates.y + coordinates.height;
    }

    showAnnotationDetails(annotation) {
        const modal = this.createAnnotationModal(annotation);
        document.body.appendChild(modal);
    }

    createAnnotationModal(annotation) {
        const modal = document.createElement('div');
        modal.className = 'annotation-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${annotation.fieldName}</h3>
                    <button class="close-button">&times;</button>
                </div>
                <div class="modal-body">
                    <p><strong>Extracted Text:</strong> "${annotation.extractedText}"</p>
                    <p><strong>Confidence:</strong> ${Math.round(annotation.confidence * 100)}%</p>
                    <p><strong>SHAP Contribution:</strong> ${annotation.shapContribution.toFixed(3)}</p>
                    <p><strong>Explanation:</strong> ${annotation.explanation}</p>
                </div>
            </div>
        `;

        modal.querySelector('.close-button').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        return modal;
    }

    showAnnotationTooltip(annotation, x, y) {
        this.hideAnnotationTooltip();
        
        const tooltip = document.createElement('div');
        tooltip.className = 'annotation-tooltip';
        tooltip.innerHTML = `
            <strong>${annotation.fieldName}</strong><br>
            "${annotation.extractedText}"<br>
            <small>Confidence: ${Math.round(annotation.confidence * 100)}%</small>
        `;
        
        tooltip.style.position = 'absolute';
        tooltip.style.left = x + 10 + 'px';
        tooltip.style.top = y - 10 + 'px';
        tooltip.style.zIndex = '1000';
        
        document.body.appendChild(tooltip);
        this.currentTooltip = tooltip;
    }

    hideAnnotationTooltip() {
        if (this.currentTooltip) {
            document.body.removeChild(this.currentTooltip);
            this.currentTooltip = null;
        }
    }

    // Utility methods
    formatFeatureText(feature) {
        const nameMap = {
            'policy_number': 'Policy Number',
            'claim_amount': 'Claim Amount',
            'incident_date': 'Incident Date',
            'claimant_name': 'Claimant Name',
            'medical_diagnosis': 'Medical Diagnosis',
            'fault_percentage': 'Fault Percentage'
        };

        return nameMap[feature.name] || feature.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    highlightAnnotation(annotationId) {
        const annotation = this.annotations.get(annotationId);
        if (!annotation) return;

        // Temporarily highlight the annotation
        this.renderAnnotation({
            ...annotation,
            color: this.colors.highlight_hover
        });

        // Scroll to annotation if needed
        this.scrollToAnnotation(annotation);

        // Reset after 2 seconds
        setTimeout(() => {
            this.renderAnnotation(annotation);
        }, 2000);
    }

    scrollToAnnotation(annotation) {
        const container = this.annotationCanvas.parentElement;
        if (container) {
            container.scrollTo({
                left: annotation.coordinates.x - 100,
                top: annotation.coordinates.y - 100,
                behavior: 'smooth'
            });
        }
    }

    highlightFeatureInDocument(featureName) {
        // Find annotations related to this feature
        for (let [id, annotation] of this.annotations) {
            if (annotation.fieldName.includes(featureName)) {
                this.renderAnnotation({
                    ...annotation,
                    color: this.colors.highlight_hover
                });
            }
        }
    }

    clearFeatureHighlights() {
        // Reset all annotations to original colors
        for (let [id, annotation] of this.annotations) {
            this.renderAnnotation(annotation);
        }
    }

    showFeatureDetails(feature) {
        console.log('Feature details:', feature);
        // Could open a detailed modal or side panel
    }

    // Public API methods
    explainDecision(features, prediction, documentAnnotations = []) {
        const shapExplanation = this.generateSHAPExplanation(features, 0.5, prediction);
        const limeExplanation = this.generateLIMEExplanation(features, prediction, 'current_claim');
        
        return {
            shapExplanation,
            limeExplanation,
            annotations: documentAnnotations,
            combinedConfidence: this.calculateCombinedConfidence(features),
            explanationQuality: this.assessExplanationQuality(shapExplanation, limeExplanation)
        };
    }

    calculateCombinedConfidence(features) {
        const avgConfidence = features.reduce((sum, f) => sum + (f.confidence || 0.8), 0) / features.length;
        const featureCount = features.length;
        const completeness = Math.min(featureCount / 10, 1.0); // Assume 10 features is complete
        
        return avgConfidence * completeness;
    }

    assessExplanationQuality(shapExplanation, limeExplanation) {
        const shapQuality = shapExplanation ? 0.5 : 0;
        const limeQuality = limeExplanation ? 0.3 : 0;
        const annotationQuality = this.annotations.size > 0 ? 0.2 : 0;
        
        return shapQuality + limeQuality + annotationQuality;
    }

    // Integration methods
    attachToDocument(documentContainer) {
        if (typeof documentContainer === 'string') {
            documentContainer = document.getElementById(documentContainer);
        }
        
        if (documentContainer) {
            documentContainer.style.position = 'relative';
            documentContainer.appendChild(this.annotationCanvas);
            this.resizeCanvas(documentContainer);
        }
    }

    resizeCanvas(container) {
        const rect = container.getBoundingClientRect();
        this.annotationCanvas.width = rect.width;
        this.annotationCanvas.height = rect.height;
        this.annotationCanvas.style.width = rect.width + 'px';
        this.annotationCanvas.style.height = rect.height + 'px';
    }

    // Export functionality
    exportExplanation(format = 'json') {
        const exportData = {
            timestamp: new Date().toISOString(),
            annotations: Array.from(this.annotations.values()),
            explanations: Array.from(this.explanations.values()),
            metadata: {
                version: '1.0',
                engine: 'ExplainabilityEngine',
                format: format
            }
        };

        if (format === 'json') {
            return JSON.stringify(exportData, null, 2);
        } else if (format === 'csv') {
            return this.convertToCSV(exportData);
        }
        
        return exportData;
    }

    convertToCSV(data) {
        // Convert annotations to CSV format
        const headers = ['id', 'fieldName', 'extractedText', 'confidence', 'explanation'];
        const rows = data.annotations.map(ann => [
            ann.id,
            ann.fieldName,
            ann.extractedText,
            ann.confidence,
            ann.explanation
        ]);

        return [headers, ...rows].map(row => row.join(',')).join('\n');
    }
}

// Global instance for easy access
let explainabilityEngine = null;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    explainabilityEngine = new ExplainabilityEngine({
        debug: true,
        confidenceThreshold: 0.7
    });
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ExplainabilityEngine;
} 