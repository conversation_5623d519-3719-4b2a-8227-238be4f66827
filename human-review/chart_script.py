import plotly.graph_objects as go
import json

# Data from the provided JSON
data = {
    "tools": [
        {
            "name": "PDF.js Annotation Extension",
            "features": 9.5,
            "ease_of_use": 8.5,
            "active_development": 9.8,
            "ai_integration": 9.0,
            "mobile_support": 8.8
        },
        {
            "name": "React PDF Highlighter",
            "features": 8.2,
            "ease_of_use": 9.0,
            "active_development": 7.5,
            "ai_integration": 8.5,
            "mobile_support": 8.0
        },
        {
            "name": "Annotorious",
            "features": 8.8,
            "ease_of_use": 8.0,
            "active_development": 8.5,
            "ai_integration": 7.5,
            "mobile_support": 7.8
        },
        {
            "name": "PDF Annotate.js",
            "features": 7.5,
            "ease_of_use": 7.0,
            "active_development": 6.0,
            "ai_integration": 6.5,
            "mobile_support": 6.5
        },
        {
            "name": "RecogitoJS",
            "features": 7.8,
            "ease_of_use": 8.5,
            "active_development": 7.0,
            "ai_integration": 7.8,
            "mobile_support": 7.0
        },
        {
            "name": "Web Highlights",
            "features": 8.5,
            "ease_of_use": 9.2,
            "active_development": 8.8,
            "ai_integration": 6.8,
            "mobile_support": 9.0
        }
    ]
}

# Colors for each metric (using the 5 primary brand colors)
colors = ['#1FB8CD', '#FFC185', '#ECEBD5', '#5D878F', '#D2BA4C']

# Abbreviate tool names to fit 15 character limit
tool_names = [
    "PDF.js Ext",
    "React PDF", 
    "Annotorious",
    "PDF Annotate",
    "RecogitoJS",
    "Web Highlights"
]

# Create the figure
fig = go.Figure()

# Add bars for each metric
metrics = ['features', 'ease_of_use', 'active_development', 'ai_integration', 'mobile_support']
metric_labels = ['Features', 'Ease of Use', 'Active Dev', 'AI Integration', 'Mobile']

for i, (metric, label) in enumerate(zip(metrics, metric_labels)):
    values = [tool[metric] for tool in data['tools']]
    
    fig.add_trace(go.Bar(
        y=tool_names,
        x=values,
        name=label,
        orientation='h',
        marker_color=colors[i],
        cliponaxis=False
    ))

# Update layout
fig.update_layout(
    title='PDF Annotation Tools Comparison',
    xaxis_title='Score (0-10)',
    yaxis_title='Tools',
    barmode='group',
    legend=dict(
        orientation='h',
        yanchor='bottom',
        y=1.05,
        xanchor='center',
        x=0.5
    )
)

# Update axes
fig.update_xaxes(range=[0, 10])

# Save the chart
fig.write_image('pdf_annotation_comparison.png')