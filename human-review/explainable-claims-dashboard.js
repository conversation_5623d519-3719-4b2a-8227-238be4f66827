/**
 * ZURICH EXPLAINABLE CLAIMS DASHBOARD
 * Production-ready JavaScript application for AI-driven claims review
 * Features: Real-time AI highlights, bidirectional mapping, explainability
 */

class ExplainableClaimsDashboard {
    constructor() {
        // Configuration
        this.config = {
            supabaseUrl: 'https://tlduggpohclrgxbvuzhd.supabase.co',
            supabaseServiceKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZHVnZ3BvaGNscmd4YnZ1emhkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDcwMTAzOCwiZXhwIjoyMDY2Mjc3MDM4fQ.wDi5bG1O81a3HaXOujWshJZoGV6AhFgEuaoVcjam0SE',
            backendUrl: window.location.hostname === "localhost"
                ? "http://localhost:8000"
                : "https://your-deployed-backend-domain.com",
            defaultClaimReference: 'CLM-85228383'
        };

        // State management
        this.state = {
            currentClaim: null,
            claimData: null,
            attachments: [],
            explainabilityData: [],
            activeDocument: null,
            activeHighlights: [],
            selectedField: null,
            isLoading: false,
            documentZoom: 1.0
        };

        // Initialize application
        this.init();
    }

    /**
     * Initialize the application
     */
    async init() {
        console.log('🚀 Initializing Zurich Explainable Claims Dashboard');
        
        try {
            // Wait for Supabase to be available
            await this.waitForSupabase();
            
            // Initialize Supabase client
            this.initSupabase();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Load default claim
            await this.loadClaim(this.config.defaultClaimReference);
            
            console.log('✅ Dashboard initialized successfully');
        } catch (error) {
            console.error('❌ Failed to initialize dashboard:', error);
            this.showError('Failed to initialize dashboard. Please refresh the page.');
        }
    }

    /**
     * Wait for Supabase library to be loaded
     */
    async waitForSupabase() {
        return new Promise((resolve, reject) => {
            let attempts = 0;
            const maxAttempts = 100; // 10 seconds max wait
            
            const checkSupabase = () => {
                attempts++;
                
                console.log(`🔍 Checking Supabase (attempt ${attempts}/${maxAttempts})...`);
                console.log('supabase type:', typeof supabase);
                console.log('supabase object:', supabase);
                
                if (typeof supabase !== 'undefined' && supabase.createClient) {
                    console.log('✅ Supabase library loaded successfully');
                    resolve();
                } else if (attempts >= maxAttempts) {
                    console.error('❌ Supabase library failed to load after', maxAttempts, 'attempts');
                    reject(new Error(`Supabase library failed to load within 10 seconds. Attempts: ${attempts}`));
                } else {
                    setTimeout(checkSupabase, 100);
                }
            };
            
            checkSupabase();
        });
    }

    /**
     * Initialize Supabase client
     */
    initSupabase() {
        try {
            if (typeof supabase === 'undefined') {
                throw new Error('Supabase library not loaded');
            }
            
            this.supabase = supabase.createClient(
                this.config.supabaseUrl,
                this.config.supabaseServiceKey
            );
            
            // Verify the client was created properly
            console.log('🔍 Verifying Supabase client...');
            console.log('Client object:', this.supabase);
            console.log('Client methods:', Object.keys(this.supabase));
            console.log('Has table method:', typeof this.supabase.table);
            console.log('Has from method:', typeof this.supabase.from);
            
            if (!this.supabase || (typeof this.supabase.table !== 'function' && typeof this.supabase.from !== 'function')) {
                throw new Error('Supabase client created but missing required methods');
            }
            
            console.log('✅ Supabase client initialized successfully');
            this.updateConnectionStatus('connected');
        } catch (error) {
            console.error('❌ Failed to initialize Supabase:', error);
            this.updateConnectionStatus('disconnected');
            throw error;
        }
    }

    /**
     * Setup event listeners for UI interactions
     */
    setupEventListeners() {
        // Load claim button
        document.getElementById('loadClaimBtn').addEventListener('click', () => {
            const claimRef = document.getElementById('claimInput').value.trim();
            if (claimRef) {
                this.loadClaim(claimRef);
            }
        });

        // Enter key on claim input
        document.getElementById('claimInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const claimRef = e.target.value.trim();
                if (claimRef) {
                    this.loadClaim(claimRef);
                }
            }
        });

        // Refresh button
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.loadClaim(this.state.currentClaim?.claim_reference || this.config.defaultClaimReference);
        });

        // Document selector removed - we now display all content automatically

        // Zoom controls removed - following review-app pattern

        // Field mapping clicks
        document.addEventListener('click', (e) => {
            const fieldMapping = e.target.closest('.field-mapping');
            if (fieldMapping) {
                const field = fieldMapping.dataset.field;
                this.highlightField(field);
            }
        });

        // Hide tooltip on click outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.highlight-overlay')) {
                this.hideTooltip();
            }
        });
    }

    /**
     * Load claim data and generate AI highlights
     */
    async loadClaim(claimReference) {
        this.state.isLoading = true;
        this.state.currentClaim = { claim_reference: claimReference };
        this.showLoading();

        try {
            console.log(`📄 Loading claim data for ${claimReference}`);
            
            // Update UI with claim reference
            document.getElementById('claimReference').textContent = claimReference;
            document.getElementById('claimInput').value = claimReference;

            // Fetch claim data from Supabase
            const claimData = await this.fetchClaimData(claimReference);
            this.state.claimData = claimData;

            // Update claim header with basic info
            this.updateClaimHeader(claimData);

            // Fetch documents/attachments
            const attachments = await this.fetchAttachments(claimReference);
            this.state.attachments = attachments;

            // Update document list 
            this.populateDocumentList(attachments);

            // Display all content first (like review-app pattern)
            this.displayAllDocumentContent();

            // Then generate AI highlights for each piece of content
            await this.generateAIHighlightsForAllContent(claimReference);

            console.log('✅ Claim data loaded successfully');

        } catch (error) {
            console.error('❌ Failed to load claim:', error);
            this.showError(`Failed to load claim ${claimReference}: ${error.message}`);
        } finally {
            this.state.isLoading = false;
        }
    }

    /**
     * Fetch claim data from Supabase
     */
    async fetchClaimData(claimReference) {
        try {
            if (!this.supabase) {
                throw new Error('Supabase client not initialized');
            }

            console.log(`🔍 Fetching claim data for ${claimReference}`);
            console.log('Available methods:', Object.keys(this.supabase));
            
            let query;
            
            // Try different methods to access the table
            if (typeof this.supabase.table === 'function') {
                console.log('Using table() method');
                query = this.supabase.table('claims');
            } else if (typeof this.supabase.from === 'function') {
                console.log('Using from() method');
                query = this.supabase.from('claims');
            } else {
                throw new Error('No valid table access method found');
            }
            
            const { data, error } = await query
                .select('*')
                .eq('claim_reference', claimReference)
                .single();

            if (error) {
                console.error('Supabase error:', error);
                throw new Error(`Database error: ${error.message}`);
            }
            
            if (!data) {
                throw new Error(`Claim ${claimReference} not found in database`);
            }

            console.log('✅ Claim data fetched successfully');
            return data;
        } catch (error) {
            console.error('❌ Failed to fetch claim data:', error);
            throw error;
        }
    }

    /**
     * Fetch attachments for a claim and extract individual documents
     */
    async fetchAttachments(claimReference) {
        try {
            if (!this.supabase) {
                throw new Error('Supabase client not initialized');
            }

            console.log(`📎 Fetching attachments for ${claimReference}`);
            
            let query;
            
            // Try different methods to access the table
            if (typeof this.supabase.table === 'function') {
                console.log('Using table() method for attachments');
                query = this.supabase.table('attachments');
            } else if (typeof this.supabase.from === 'function') {
                console.log('Using from() method for attachments');
                query = this.supabase.from('attachments');
            } else {
                throw new Error('No valid table access method found');
            }
            
            const { data, error } = await query
                .select('*')
                .eq('claim_reference', claimReference);

            if (error) {
                console.error('Supabase error:', error);
                throw new Error(`Database error: ${error.message}`);
            }

            console.log(`✅ Found ${data?.length || 0} attachments`);
            
            // Extract individual documents from OCR structure
            const extractedDocuments = [];
            
            for (const attachment of data || []) {
                try {
                    const ocrText = attachment.ocr_text;
                    if (!ocrText || !ocrText.trim()) {
                        console.log(`Skipping attachment ${attachment.id} - no OCR text`);
                        continue;
                    }
                    
                    // Parse the OCR JSON structure
                    const ocrData = JSON.parse(ocrText);
                    const zurichResponse = ocrData.zurich_ocr_response || {};
                    const results = zurichResponse.results || [];
                    
                    if (results.length === 0) {
                        console.log(`Skipping attachment ${attachment.id} - no results in OCR data`);
                        continue;
                    }
                    
                    // Extract each document from the results
                    for (const result of results) {
                        if (!result.success || !result.extracted_text || !result.extracted_text.trim()) {
                            continue;
                        }
                        
                        // URL decode the filename for display
                        const displayFilename = decodeURIComponent(result.filename || `Document_${extractedDocuments.length + 1}`);
                        
                        // Create a document object that looks like what the frontend expects
                        extractedDocuments.push({
                            id: attachment.id,
                            file_name: displayFilename,
                            filename: displayFilename, // For backward compatibility
                            ocr_text: result.extracted_text, // This is now the actual extracted text
                            confidence: result.confidence || 0.0,
                            content_type: attachment.content_type,
                            file_size: result.extracted_text.length,
                            original_attachment_id: attachment.id,
                            claim_reference: attachment.claim_reference
                        });
                        
                        console.log(`✅ Extracted document: ${displayFilename} (${result.extracted_text.length} chars)`);
                    }
                    
                } catch (parseError) {
                    console.error(`Error parsing OCR data for attachment ${attachment.id}:`, parseError);
                    continue;
                }
            }
            
            console.log(`✅ Extracted ${extractedDocuments.length} documents from ${data?.length || 0} attachments`);
            return extractedDocuments;
        } catch (error) {
            console.error('❌ Failed to fetch attachments:', error);
            throw error;
        }
    }

    /**
     * Generate AI highlights for all content (review-app real-time pattern)
     */
    async generateAIHighlightsForAllContent(claimReference) {
        try {
            console.log('🧠 Generating AI highlights for all content...');
            
            // Update status for each document section
            const documentSections = document.querySelectorAll('.document-section');
            documentSections.forEach(section => {
                const statusElement = section.querySelector('.document-status');
                if (statusElement) {
                    statusElement.textContent = 'Analyzing...';
                    statusElement.className = 'document-status analyzing';
                }
            });
            
            // Create AbortController for 5-minute timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 300000); // 5 minutes
            
            const response = await fetch(`${this.config.backendUrl}/api/ai-explainability/highlights`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ claim_reference: claimReference }),
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            if (!response.ok) {
                throw new Error(`Backend returned status ${response.status}`);
            }
            const data = await response.json();
            this.state.explainabilityData = data;
            
            if (data.success) {
                console.log(`✅ Generated ${data.highlights_generated} highlights for ${data.documents_analyzed} documents`);
                
                // Apply highlights to each document section
                await this.applyHighlightsToAllContent(data.explainability_data);
                
                // Update confidence scores
                this.updateConfidenceScores(data.explainability_data);
                
                // Render color legend
                this.renderColorLegend(data.explainability_data);
                
                // Update AI insights
                this.updateAIInsights(data.explainability_data);
                
            } else {
                throw new Error(data.error_message || 'Failed to generate highlights');
            }

        } catch (error) {
            console.error('❌ Failed to generate AI highlights:', error);
            
            // Update all document sections to show error
            const documentSections = document.querySelectorAll('.document-section');
            documentSections.forEach(section => {
                const statusElement = section.querySelector('.document-status');
                if (statusElement) {
                    statusElement.textContent = 'Analysis Failed';
                    statusElement.className = 'document-status error';
                }
            });
            
            this.showError(`Failed to generate AI highlights: ${error.message}`);
        }
    }

    /**
     * Apply highlights to all document content sections
     */
    async applyHighlightsToAllContent(explainabilityData) {
        if (!explainabilityData || explainabilityData.length === 0) {
            console.warn('No explainability documents to process');
            return;
        }

        // Get all document sections on the page
        const documentSections = document.querySelectorAll('.document-section[data-document-index]');
        console.log(`Found ${documentSections.length} document sections to process`);
        console.log(`Received ${explainabilityData.length} explainability data entries`);

        // Map explainability data to document sections by matching content
        explainabilityData.forEach((docData, explainDataIndex) => {
            // Try to find matching document section by content similarity
            let matchedSection = null;
            let bestMatchIndex = -1;

            documentSections.forEach((section, sectionIndex) => {
                const contentElement = section.querySelector('.document-body .plain-text');
                if (contentElement) {
                    const sectionText = contentElement.textContent;
                    
                    // Check if any highlights from this explainability data match the section content
                    const hasMatchingHighlights = docData.highlights?.some(highlight => 
                        sectionText.includes(highlight.text)
                    );
                    
                    if (hasMatchingHighlights && !matchedSection) {
                        matchedSection = section;
                        bestMatchIndex = sectionIndex;
                    }
                }
            });

            // If we found a match, apply highlights
            if (matchedSection) {
                const statusElement = matchedSection.querySelector('.document-status');
                const contentElement = matchedSection.querySelector('.document-body .plain-text');
                
                if (contentElement) {
                    // Apply highlights to this document
                    const highlightedText = this.renderOCRWithHighlights(contentElement.textContent, docData);
                    contentElement.innerHTML = highlightedText;
                    
                    // Update status
                    if (statusElement) {
                        statusElement.textContent = `${docData.highlights?.length || 0} Highlights`;
                        statusElement.className = 'document-status completed';
                    }

                    console.log(`✅ Applied ${docData.highlights?.length || 0} highlights to document: ${matchedSection.dataset.filename}`);
                }
            } else {
                console.warn(`No matching document section found for explainability data ${explainDataIndex}`);
            }
        });

        // Update any sections that didn't get highlights
        documentSections.forEach(section => {
            const statusElement = section.querySelector('.document-status');
            if (statusElement && statusElement.textContent === 'Analyzing...') {
                statusElement.textContent = '0 Highlights';
                statusElement.className = 'document-status completed';
            }
        });
    }

    /**
     * Generate AI highlights using the backend explainability service (legacy method)
     */
    async generateAIHighlights(claimReference) {
        try {
            console.log('🧠 Generating AI highlights...');
            
            // Create AbortController for 5-minute timeout as requested
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 300000); // 5 minutes
            
            const response = await fetch(`${this.config.backendUrl}/api/ai-explainability/highlights`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ claim_reference: claimReference }),
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            if (!response.ok) {
                throw new Error(`Backend returned status ${response.status}`);
            }
            const data = await response.json();
            this.state.explainabilityData = data;
            
            if (data.success) {
                console.log(`✅ Generated ${data.highlights_generated} highlights for ${data.documents_analyzed} documents`);
                
                // Update confidence scores
                this.updateConfidenceScores(data.explainability_data);
                
                // Render color legend
                this.renderColorLegend(data.explainability_data);
                
                // Update AI insights
                this.updateAIInsights(data.explainability_data);
                
            } else {
                throw new Error(data.error_message || 'Failed to generate highlights');
            }

        } catch (error) {
            console.error('❌ Failed to generate AI highlights:', error);
            
            // Handle specific timeout error
            if (error.name === 'AbortError') {
                this.showError('AI highlight generation timed out after 5 minutes. The system may be processing large documents.');
            } else {
                this.showError(`AI highlight generation failed: ${error.message}`);
            }
            
            // Continue with basic functionality even if AI highlights fail
            this.state.explainabilityData = [];
        }
    }

    /**
     * Update claim header with claim data
     */
    updateClaimHeader(claimData) {
        // Update status
        const status = claimData.workflow_status || 'Processing';
        const statusElement = document.getElementById('claimStatus');
        statusElement.textContent = status;
        statusElement.className = `ml-2 px-2 py-1 rounded text-xs ${this.getStatusColorClass(status)}`;

        // Update other fields
        document.getElementById('claimPriority').textContent = claimData.priority_score ? 
            this.getPriorityLabel(claimData.priority_score) : 'Normal';
        document.getElementById('assignedAgent').textContent = claimData.assigned_agent || 'AI Assistant';
        document.getElementById('estimatedValue').textContent = claimData.estimated_value ? 
            `$${Number(claimData.estimated_value).toLocaleString()}` : '$0';
        document.getElementById('lastUpdated').textContent = claimData.created_at ? 
            this.formatDateTime(claimData.created_at) : 'Just now';
    }

    /**
     * Populate document list in left panel - works with OCR text arrays
     */
    populateDocumentList(attachments) {
        const container = document.getElementById('documentList');
        
        if (!attachments || attachments.length === 0) {
            container.innerHTML = '<p class="text-gray-500 text-sm">No documents available</p>';
            return;
        }

        container.innerHTML = attachments.map((doc, index) => `
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded cursor-pointer hover:bg-gray-100 transition"
                 onclick="window.dashboard.selectDocumentByIndex(${index})">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-file-alt text-blue-500"></i>
                    <div>
                        <div class="font-medium text-sm">${doc.file_name || `Document ${index + 1}`}</div>
                        <div class="text-xs text-gray-500">
                            ${doc.content_type || 'Unknown'} • ${this.formatFileSize(doc.file_size)}
                        </div>
                    </div>
                </div>
                <div class="text-xs text-gray-400">
                    ${doc.ocr_text ? '📝 OCR Available' : '📄 No OCR'}
                </div>
            </div>
        `).join('');
    }

    // Document selector method removed - we now display all content automatically

    /**
     * Display all document content first, then highlights (review-app pattern)
     */
    displayAllDocumentContent() {
        const container = document.getElementById('documentContent');
        if (!container) {
            console.warn('Document content container not found');
            return;
        }

        const claimData = this.state.claimData;
        const attachments = this.state.attachments;

        let content = '';

        // 1. Display email body first if available
        if (claimData && claimData.email_body) {
            content += `
                <div class="document-section">
                    <div class="document-header">
                        <h4>📧 Email Body</h4>
                        <span class="document-type">Original Claim Email</span>
                    </div>
                    <div class="document-body email-content">
                        ${this.escapeHtml(claimData.email_body)}
                    </div>
                </div>
                <hr class="document-separator">
            `;
        }

        // 2. Display all OCR extracted content with filenames (no highlights yet)
        if (attachments && attachments.length > 0) {
            attachments.forEach((attachment, index) => {
                const fileName = attachment.file_name || `Document ${index + 1}`;
                const ocrText = attachment.ocr_text;

                if (ocrText && ocrText.trim()) {
                    content += `
                        <div class="document-section" data-document-index="${index}" data-filename="${this.escapeHtml(fileName)}">
                            <div class="document-header">
                                <h4>📄 ${this.escapeHtml(fileName)}</h4>
                                <span class="document-type">OCR Extracted Content</span>
                                <span class="document-status">Ready for AI Analysis</span>
                            </div>
                            <div class="document-body ocr-content" id="ocrContent${index}">
                                <div class="plain-text">${this.escapeHtml(ocrText)}</div>
                            </div>
                        </div>
                        ${index < attachments.length - 1 ? '<hr class="document-separator">' : ''}
                    `;
                } else {
                    // Show filename even if no OCR text
                    content += `
                        <div class="document-section" data-document-index="${index}" data-filename="${this.escapeHtml(fileName)}">
                            <div class="document-header">
                                <h4>📄 ${this.escapeHtml(fileName)}</h4>
                                <span class="document-type">No OCR Text Available</span>
                                <span class="document-status">No Content</span>
                            </div>
                            <div class="document-body no-content">
                                <p>No extractable text content found in this document.</p>
                            </div>
                        </div>
                        ${index < attachments.length - 1 ? '<hr class="document-separator">' : ''}
                    `;
                }
            });
        }

        // If no email body and no attachments
        if (!content) {
            content = `
                <div class="document-section">
                    <div class="no-data">
                        <p>No email body or document content available for this claim.</p>
                    </div>
                </div>
            `;
        }

        container.innerHTML = content;

        // Show the document content area
        this.showDocumentContent();

        // Update confidence scores if explainability data exists
        if (this.state.explainabilityData && this.state.explainabilityData.length > 0) {
            this.updateConfidenceScores(this.state.explainabilityData);
            this.renderColorLegend(this.state.explainabilityData);
        }

        console.log('✅ Email body and OCR content displayed successfully');
    }

    /**
     * Render OCR text with AI highlights applied
     */
    renderOCRWithHighlights(ocrText, explainabilityData) {
        if (!explainabilityData || !explainabilityData.highlights || explainabilityData.highlights.length === 0) {
            // Return plain text if no highlights available
            return `<div class="ocr-text-plain">${this.escapeHtml(ocrText)}</div>`;
        }

        // Sort highlights by start position
        const highlights = explainabilityData.highlights
            .filter(h => h.start >= 0 && h.end <= ocrText.length && h.start < h.end)
            .sort((a, b) => a.start - b.start);

        if (highlights.length === 0) {
            return `<div class="ocr-text-plain">${this.escapeHtml(ocrText)}</div>`;
        }

        // Build highlighted text
        let highlightedText = '';
        let lastEnd = 0;

        highlights.forEach((highlight, index) => {
            // Add text before highlight
            highlightedText += this.escapeHtml(ocrText.substring(lastEnd, highlight.start));
            
            // Add highlighted text
            highlightedText += `
                <span class="highlight-text" 
                      style="background-color: ${highlight.color}; border-radius: 3px; padding: 2px 4px; cursor: pointer;"
                      data-highlight-index="${index}"
                      data-field="${highlight.field}"
                      data-explanation="${this.escapeHtml(highlight.explanation)}"
                      data-contribution="${highlight.contribution_score}"
                      data-type="${highlight.highlight_type}"
                      onmouseover="window.dashboard.showHighlightTooltip(event, this)"
                      onmouseout="window.dashboard.hideTooltip()"
                      onclick="window.dashboard.selectHighlight(this)">
                    ${this.escapeHtml(highlight.text)}
                </span>
            `;
            
            lastEnd = highlight.end;
        });

        // Add remaining text
        highlightedText += this.escapeHtml(ocrText.substring(lastEnd));

        return `<div class="ocr-text-highlighted" style="zoom: ${this.state.documentZoom}">${highlightedText}</div>`;
    }

    /**
     * Select and display a document by index (works with OCR text arrays)
     */
    selectDocumentByIndex(index) {
        const attachments = this.state.attachments || [];
        
        if (index < 0 || index >= attachments.length) {
            console.error('selectDocumentByIndex called with invalid index:', index);
            this.showError('Invalid document index.');
            return;
        }
        
        const docData = attachments[index];
        if (!docData) {
            console.error('Document not found at index:', index);
            this.showError('Document not found.');
            return;
        }

        console.log(`📖 Selecting document by index ${index}: ${docData.file_name || `Document ${index + 1}`}`);
        
        this.state.activeDocument = docData;
        
        // Update selector
        document.getElementById('documentSelector').value = index.toString();
        
        // Find explainability data for this document based on OCR text content
        const explainabilityData = this.findExplainabilityDataForDocument(docData);

        // Render document with highlights
        this.renderDocumentWithHighlights(docData, explainabilityData);
        
        // Show document content
        this.showDocumentContent();
    }

    /**
     * Backward compatibility: select document by filename (deprecated)
     */
    selectDocument(filename) {
        console.warn('selectDocument(filename) is deprecated, use selectDocumentByIndex(index) instead');
        
        if (!filename) {
            console.error('selectDocument called with undefined filename');
            this.showError('No document selected.');
            return;
        }
        
        const attachments = this.state.attachments || [];
        const index = attachments.findIndex(att => att.file_name === filename);
        
        if (index === -1) {
            console.error('Document not found for filename:', filename);
            this.showError('Document not found.');
            return;
        }
        
        this.selectDocumentByIndex(index);
    }

    /**
     * Find explainability data for a document based on OCR text content
     */
    findExplainabilityDataForDocument(docData) {
        if (!this.state.explainabilityData || !this.state.explainabilityData.explainability_data) {
            return null;
        }
        
        // Search through explainability data to find matching highlights
        for (const explainData of this.state.explainabilityData.explainability_data) {
            if (explainData.highlights && explainData.highlights.length > 0) {
                // Check if any highlight text matches content in the document's OCR text
                const hasMatchingHighlights = explainData.highlights.some(highlight => {
                    return docData.ocr_text && docData.ocr_text.includes(highlight.text);
                });
                
                if (hasMatchingHighlights) {
                    return explainData;
                }
            }
        }
        
        // If no specific match found, return the first explainability data
        return this.state.explainabilityData.explainability_data[0] || null;
    }

    /**
     * Render document content with AI-generated highlights
     */
    renderDocumentWithHighlights(documentData, explainabilityData) {
        const container = document.getElementById('documentContent');
        const text = documentData.ocr_text || '[No OCR text available for this document]';

        if (!explainabilityData || !explainabilityData.highlights.length) {
            // Render plain text if no highlights available
            container.innerHTML = `<div class="text-gray-700">${this.escapeHtml(text)}</div>`;
            return;
        }

        // Sort highlights by start position
        const highlights = explainabilityData.highlights
            .filter(h => h.start >= 0 && h.end <= text.length && h.start < h.end)
            .sort((a, b) => a.start - b.start);

        // Build highlighted text
        let highlightedText = '';
        let lastEnd = 0;

        highlights.forEach((highlight, index) => {
            // Add text before highlight
            highlightedText += this.escapeHtml(text.substring(lastEnd, highlight.start));
            
            // Add highlighted text
            highlightedText += `
                <span class="highlight-text" 
                      style="background-color: ${highlight.color}; border-radius: 3px; padding: 2px 4px; cursor: pointer;"
                      data-highlight-index="${index}"
                      data-field="${highlight.field}"
                      data-explanation="${this.escapeHtml(highlight.explanation)}"
                      data-contribution="${highlight.contribution_score}"
                      data-type="${highlight.highlight_type}"
                      onmouseover="window.dashboard.showHighlightTooltip(event, this)"
                      onmouseout="window.dashboard.hideTooltip()"
                      onclick="window.dashboard.selectHighlight(this)">
                    ${this.escapeHtml(highlight.text)}
                </span>
            `;
            
            lastEnd = highlight.end;
        });

        // Add remaining text
        highlightedText += this.escapeHtml(text.substring(lastEnd));

        container.innerHTML = `<div class="text-gray-700" style="zoom: ${this.state.documentZoom}">${highlightedText}</div>`;
        
        // Store highlights for reference
        this.state.activeHighlights = highlights;
    }

    /**
     * Show tooltip for highlight
     */
    showHighlightTooltip(event, element) {
        const tooltip = document.getElementById('highlightTooltip');
        const tooltipContent = document.getElementById('tooltipContent');
        
        const explanation = element.dataset.explanation;
        const contribution = parseFloat(element.dataset.contribution);
        const type = element.dataset.type;
        const field = element.dataset.field;
        
        tooltipContent.innerHTML = `
            <div class="font-semibold mb-1">${type.toUpperCase()}</div>
            <div class="mb-2">${explanation}</div>
            <div class="text-xs text-gray-300">
                Field: ${field} • Contribution: ${Math.round(contribution * 100)}%
            </div>
        `;
        
        // Position tooltip
        const rect = element.getBoundingClientRect();
        tooltip.style.left = `${rect.left + window.scrollX}px`;
        tooltip.style.top = `${rect.bottom + window.scrollY + 5}px`;
        tooltip.classList.remove('hidden');
    }

    /**
     * Hide tooltip
     */
    hideTooltip() {
        document.getElementById('highlightTooltip').classList.add('hidden');
    }

    /**
     * Select a highlight and show related field
     */
    selectHighlight(element) {
        // Remove previous selections
        document.querySelectorAll('.highlight-text.selected').forEach(el => {
            el.classList.remove('selected');
        });
        
        // Add selection to current highlight
        element.classList.add('selected');
        
        // Highlight related field
        const field = element.dataset.field;
        this.highlightField(field);
    }

    /**
     * Highlight field in left panel and show related highlights
     */
    highlightField(field) {
        // Remove previous field highlights
        document.querySelectorAll('.field-mapping.highlighted').forEach(el => {
            el.classList.remove('highlighted');
        });

        // Highlight selected field
        const fieldElement = document.querySelector(`[data-field="${field}"]`);
        if (fieldElement) {
            fieldElement.classList.add('highlighted');
            this.state.selectedField = field;
        }

        // Highlight related text highlights
        document.querySelectorAll('.highlight-text').forEach(el => {
            if (el.dataset.field === field) {
                el.style.boxShadow = '0 0 10px rgba(59, 130, 246, 0.5)';
            } else {
                el.style.boxShadow = 'none';
            }
        });

        // Scroll to first related highlight
        const firstRelatedHighlight = document.querySelector(`[data-field="${field}"]`);
        if (firstRelatedHighlight) {
            firstRelatedHighlight.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    /**
     * Update confidence scores display
     */
    updateConfidenceScores(explainabilityData) {
        if (!explainabilityData.length) return;

        // Calculate average confidence
        const avgConfidence = explainabilityData.reduce((sum, data) => sum + data.confidence_score, 0) / explainabilityData.length;
        const highlightAccuracy = Math.min(avgConfidence * 1.1, 1.0); // Slightly boost for highlight accuracy

        // Update overall confidence
        const overallPercent = Math.round(avgConfidence * 100);
        document.getElementById('overallConfidence').textContent = `${overallPercent}%`;
        document.getElementById('overallConfidenceFill').style.width = `${overallPercent}%`;

        // Update highlight accuracy
        const highlightPercent = Math.round(highlightAccuracy * 100);
        document.getElementById('highlightAccuracy').textContent = `${highlightPercent}%`;
        document.getElementById('highlightAccuracyFill').style.width = `${highlightPercent}%`;
    }

    /**
     * Render color legend
     */
    renderColorLegend(explainabilityData) {
        const container = document.getElementById('colorLegend');
        
        if (!explainabilityData.length) {
            container.innerHTML = '<p class="text-gray-500 text-sm">No color legend available</p>';
            return;
        }

        // Collect all unique colors and their meanings
        const colorMap = new Map();
        explainabilityData.forEach(data => {
            data.color_legend.forEach(legend => {
                colorMap.set(legend.color, legend);
            });
        });

        if (colorMap.size === 0) {
            container.innerHTML = '<p class="text-gray-500 text-sm">No color legend available</p>';
            return;
        }

        container.innerHTML = Array.from(colorMap.values()).map(legend => `
            <div class="flex items-center space-x-3">
                <div class="w-4 h-4 rounded" style="background-color: ${legend.color}"></div>
                <div class="flex-1">
                    <div class="font-medium text-sm">${legend.meaning}</div>
                    <div class="text-xs text-gray-500">${legend.examples.join(', ')}</div>
                </div>
            </div>
        `).join('');
    }

    /**
     * Update AI insights and summary
     */
    updateAIInsights(explainabilityData) {
        const summaryContainer = document.getElementById('aiSummary');
        const notesContainer = document.getElementById('processingNotes');

        if (!explainabilityData.length) {
            summaryContainer.textContent = 'No AI analysis available';
            notesContainer.innerHTML = '<li>No processing notes available</li>';
            return;
        }

        // Combine summaries
        const summaries = explainabilityData
            .map(data => data.summary)
            .filter(summary => summary && summary.trim())
            .join(' ');

        summaryContainer.textContent = summaries || 'AI analysis in progress...';

        // Combine processing notes
        const allNotes = explainabilityData
            .flatMap(data => data.processing_notes || [])
            .filter(note => note && note.trim());

        notesContainer.innerHTML = allNotes.length > 0 
            ? allNotes.map(note => `<li>${this.escapeHtml(note)}</li>`).join('')
            : '<li>No processing notes available</li>';
    }

    // Zoom functionality removed - following review-app pattern

    /**
     * Show loading state
     */
    showLoading() {
        const loadingState = document.getElementById('loadingState');
        const documentContent = document.getElementById('documentContent');
        const noDataState = document.getElementById('noDataState');
        
        if (loadingState) loadingState.classList.remove('hidden');
        if (documentContent) documentContent.style.display = 'none';
        if (noDataState) noDataState.classList.add('hidden');
    }

    /**
     * Show document content (replaces showDocumentViewer)
     */
    showDocumentContent() {
        const loadingState = document.getElementById('loadingState');
        const documentContent = document.getElementById('documentContent');
        const noDataState = document.getElementById('noDataState');
        
        if (loadingState) loadingState.classList.add('hidden');
        if (documentContent) documentContent.style.display = 'block';
        if (noDataState) noDataState.classList.add('hidden');
    }

    /**
     * Show no data state
     */
    showNoData() {
        const loadingState = document.getElementById('loadingState');
        const documentContent = document.getElementById('documentContent');
        const noDataState = document.getElementById('noDataState');
        
        if (loadingState) loadingState.classList.add('hidden');
        if (documentContent) documentContent.style.display = 'none';
        if (noDataState) noDataState.classList.remove('hidden');
    }

    /**
     * Update connection status indicator
     */
    updateConnectionStatus(status) {
        const indicator = document.getElementById('connectionStatus');
        
        if (status === 'connected') {
            indicator.innerHTML = '<i class="fas fa-circle text-green-400 pulse"></i> Connected';
        } else {
            indicator.innerHTML = '<i class="fas fa-circle text-red-400"></i> Disconnected';
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        console.error('Error:', message);
        
        // Create error toast (simple implementation)
        const errorDiv = document.createElement('div');
        errorDiv.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded shadow-lg z-50';
        errorDiv.innerHTML = `
            <div class="flex items-center space-x-2">
                <i class="fas fa-exclamation-triangle"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-2">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(errorDiv);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentElement) {
                errorDiv.remove();
            }
        }, 5000);
    }

    // ==================== UTILITY METHODS ====================

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    getStatusColorClass(status) {
        const statusMap = {
            'NEW': 'bg-blue-100 text-blue-800',
            'PROCESSING': 'bg-yellow-100 text-yellow-800',
            'REVIEWED': 'bg-green-100 text-green-800',
            'COMPLETED': 'bg-gray-100 text-gray-800',
            'ERROR': 'bg-red-100 text-red-800'
        };
        return statusMap[status?.toUpperCase()] || 'bg-gray-100 text-gray-800';
    }

    getPriorityLabel(score) {
        if (score >= 80) return 'Critical';
        if (score >= 60) return 'High';
        if (score >= 40) return 'Medium';
        return 'Low';
    }

    formatDateTime(dateString) {
        return new Date(dateString).toLocaleString();
    }

    formatFileSize(bytes) {
        if (!bytes) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    getElementSafe(id) {
        const el = typeof document !== 'undefined' && document.getElementById ? document.getElementById(id) : null;
        if (!el) {
            console.error(`Element with id '${id}' not found.`);
        }
        return el;
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new ExplainableClaimsDashboard();
});

// Make dashboard globally available for onclick handlers
window.dashboard = null; 