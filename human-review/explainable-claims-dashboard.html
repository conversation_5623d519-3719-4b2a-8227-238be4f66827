<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zurich Claims Explainability Dashboard</title>
    
    <!-- Modern CSS Framework -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Local Supabase Mock (fallback) -->
    <script src="supabase-mock.js"></script>
    
    <!-- Supabase Client CDN -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script>
        // Check if CDN loaded successfully
        if (typeof supabase !== 'undefined' && supabase.createClient) {
            console.log('✅ CDN Supabase loaded successfully');
        } else {
            console.log('⚠️ CDN Supabase failed, using local mock');
            // The mock should already be loaded and auto-initialized
        }
    </script>
    
    <!-- Cache busting for development -->
    <script>
        // Force reload if this is a cached version
        if (performance.navigation.type === 1) {
            console.log('Page was reloaded, clearing any cached data');
        }
    </script>
    
    <!-- PDF.js for document viewing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    
    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom Styles -->
    <style>
        .highlight-overlay {
            position: absolute;
            border-radius: 3px;
            opacity: 0.3;
            cursor: pointer;
            transition: all 0.2s ease;
            z-index: 10;
        }
        
        .highlight-overlay:hover {
            opacity: 0.6;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        }
        
        .highlight-overlay.active {
            opacity: 0.8;
            border: 2px solid #1f2937;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
        }
        
        .document-viewer {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            position: relative;
            overflow: auto;
            min-height: 600px;
        }
        
        .text-document {
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .claims-data-panel {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .field-mapping {
            cursor: pointer;
            transition: all 0.2s ease;
            border-radius: 4px;
            padding: 8px;
        }
        
        .field-mapping:hover {
            background-color: #f3f4f6;
            transform: translateY(-1px);
        }
        
        .field-mapping.highlighted {
            background-color: #dbeafe;
            border-left: 4px solid #3b82f6;
        }
        
        .confidence-meter {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .confidence-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.5s ease;
        }
        
        .loading-spinner {
            border: 3px solid #f3f4f6;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: .5;
            }
        }
        
        /* Document Section Styles */
        .document-section {
            margin-bottom: 2rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .document-header {
            background: #f9fafb;
            padding: 1rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .document-header h4 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 600;
            color: #374151;
        }
        
        .document-type {
            font-size: 0.875rem;
            color: #6b7280;
            font-style: italic;
        }

        .document-status {
            font-size: 0.75rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .document-status.analyzing {
            background: #fef3c7;
            color: #d97706;
        }

        .document-status.completed {
            background: #d1fae5;
            color: #065f46;
        }

        .document-status.error {
            background: #fee2e2;
            color: #dc2626;
        }

        .plain-text {
            white-space: pre-wrap;
            font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
            font-size: 0.875rem;
            background: #f8fafc;
            padding: 1rem;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
        }
        
        .document-body {
            padding: 1.5rem;
            line-height: 1.6;
            font-family: 'Georgia', serif;
        }
        
        .email-content {
            background: #fefef9;
            border-left: 4px solid #10b981;
        }
        
        .ocr-content {
            background: #fafcff;
            border-left: 4px solid #3b82f6;
        }
        
        .no-content {
            background: #fef2f2;
            border-left: 4px solid #ef4444;
            color: #7f1d1d;
            text-align: center;
        }
        
        .document-separator {
            margin: 2rem 0;
            border: 0;
            border-top: 2px solid #e5e7eb;
        }
        
        .ocr-text-plain, .ocr-text-highlighted {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .highlight-text.selected {
            box-shadow: 0 0 0 2px #3b82f6;
            transform: scale(1.02);
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Top Navigation -->
    <nav class="bg-blue-900 text-white shadow-lg">
        <div class="container mx-auto px-6 py-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <h1 class="text-xl font-bold">Zurich Claims Explainability Dashboard</h1>
                    <span class="text-sm bg-blue-700 px-2 py-1 rounded">AI-Powered</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm" id="connectionStatus">
                        <i class="fas fa-circle text-green-400 pulse"></i> Connected
                    </span>
                    <div class="flex items-center space-x-2">
                        <input type="text" id="claimInput" placeholder="Enter claim reference" 
                               class="px-3 py-2 text-sm bg-blue-800 text-white border border-blue-600 rounded focus:outline-none focus:border-blue-400" 
                               value="CLM-85228383">
                        <button id="loadClaimBtn" class="bg-blue-700 hover:bg-blue-600 px-4 py-2 rounded transition text-sm">
                            <i class="fas fa-search"></i> Load
                        </button>
                    </div>
                    <button id="refreshBtn" class="bg-blue-700 hover:bg-blue-600 px-4 py-2 rounded transition">
                        <i class="fas fa-refresh"></i> Refresh
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mx-auto px-6 py-8">
        <!-- Claim Header -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="flex justify-between items-start">
                <div>
                    <h2 class="text-2xl font-bold text-gray-800 mb-2">
                        Claim <span id="claimReference" class="text-blue-600">CLM-85228383</span>
                    </h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                            <span class="text-gray-600">Status:</span>
                            <span id="claimStatus" class="ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs">Processing</span>
                        </div>
                        <div>
                            <span class="text-gray-600">Priority:</span>
                            <span id="claimPriority" class="ml-2 font-medium">High</span>
                        </div>
                        <div>
                            <span class="text-gray-600">Agent:</span>
                            <span id="assignedAgent" class="ml-2">AI Assistant</span>
                        </div>
                        <div>
                            <span class="text-gray-600">Last Updated:</span>
                            <span id="lastUpdated" class="ml-2">Just now</span>
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold text-green-600" id="estimatedValue">$0</div>
                    <div class="text-sm text-gray-600">Estimated Value</div>
                </div>
            </div>
        </div>

        <!-- Main Dashboard Layout -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Left Panel: Claims Data & Analysis -->
            <div class="lg:col-span-1 space-y-6">
                <!-- AI Confidence Score -->
                <div class="claims-data-panel p-6">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i class="fas fa-brain text-purple-500 mr-2"></i>
                        AI Analysis Confidence
                    </h3>
                    <div class="space-y-4">
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span>Overall Confidence</span>
                                <span id="overallConfidence">0%</span>
                            </div>
                            <div class="confidence-meter">
                                <div id="overallConfidenceFill" class="confidence-fill bg-green-500" style="width: 0%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span>Highlight Accuracy</span>
                                <span id="highlightAccuracy">0%</span>
                            </div>
                            <div class="confidence-meter">
                                <div id="highlightAccuracyFill" class="confidence-fill bg-blue-500" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Color Legend -->
                <div class="claims-data-panel p-6">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i class="fas fa-palette text-indigo-500 mr-2"></i>
                        Highlight Legend
                    </h3>
                    <div id="colorLegend" class="space-y-3">
                        <!-- Color legend items will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Analysis Levels -->
                <div class="claims-data-panel p-6">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i class="fas fa-layer-group text-blue-500 mr-2"></i>
                        Analysis Levels
                    </h3>
                    <div class="space-y-4">
                        <div class="field-mapping" data-field="level01">
                            <div class="flex justify-between items-center">
                                <span class="font-medium">Level 1: Initial Assessment</span>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                            <div class="text-sm text-gray-600 mt-1" id="level01Summary">
                                Click to see related highlights
                            </div>
                        </div>
                        <div class="field-mapping" data-field="level02">
                            <div class="flex justify-between items-center">
                                <span class="font-medium">Level 2: Coverage Analysis</span>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                            <div class="text-sm text-gray-600 mt-1" id="level02Summary">
                                Click to see related highlights
                            </div>
                        </div>
                        <div class="field-mapping" data-field="level03">
                            <div class="flex justify-between items-center">
                                <span class="font-medium">Level 3: Fault Determination</span>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                            <div class="text-sm text-gray-600 mt-1" id="level03Summary">
                                Click to see related highlights
                            </div>
                        </div>
                        <div class="field-mapping" data-field="level04">
                            <div class="flex justify-between items-center">
                                <span class="font-medium">Level 4: Quantum Calculation</span>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                            <div class="text-sm text-gray-600 mt-1" id="level04Summary">
                                Click to see related highlights
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Document List -->
                <div class="claims-data-panel p-6">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i class="fas fa-file-alt text-green-500 mr-2"></i>
                        Documents
                    </h3>
                    <div id="documentList" class="space-y-2">
                        <!-- Document list will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Center & Right Panel: Document Viewer -->
            <div class="lg:col-span-2">
                <div class="claims-data-panel p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold flex items-center">
                            <i class="fas fa-file-text text-orange-500 mr-2"></i>
                            Claim Documents with AI Highlights
                        </h3>
                    </div>
                    
                    <!-- Loading State -->
                    <div id="loadingState" class="flex items-center justify-center p-12">
                        <div class="text-center">
                            <div class="loading-spinner mx-auto mb-4"></div>
                            <p class="text-gray-600">Loading claim data and generating AI highlights...</p>
                        </div>
                    </div>

                    <!-- Document Content Area -->
                    <div id="documentContent" class="document-content-area">
                        <!-- All email body and OCR content will be rendered here -->
                    </div>

                    <!-- No Data State -->
                    <div id="noDataState" class="hidden text-center p-12">
                        <i class="fas fa-file-excel text-gray-400 text-6xl mb-4"></i>
                        <p class="text-gray-600 text-lg">No documents available for this claim</p>
                        <p class="text-gray-500 text-sm">Documents will appear here once they are uploaded and processed</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Panel: AI Insights -->
        <div class="mt-8 bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold mb-4 flex items-center">
                <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                AI Insights & Summary
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-medium mb-2">Key Findings</h4>
                    <div id="aiSummary" class="text-gray-700 text-sm">
                        AI analysis in progress...
                    </div>
                </div>
                <div>
                    <h4 class="font-medium mb-2">Processing Notes</h4>
                    <div id="processingNotes" class="text-gray-600 text-sm">
                        <ul class="list-disc list-inside space-y-1">
                            <!-- Processing notes will be populated by JavaScript -->
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tooltip for Highlights -->
    <div id="highlightTooltip" class="fixed z-50 bg-gray-800 text-white px-3 py-2 rounded shadow-lg text-sm max-w-xs hidden">
        <div id="tooltipContent"></div>
        <div class="tooltip-arrow"></div>
    </div>

    <!-- Scripts -->
    <script src="explainable-claims-dashboard.js"></script>
</body>
</html> 