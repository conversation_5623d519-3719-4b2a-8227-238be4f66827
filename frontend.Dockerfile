# ZURICH CLAIMS EXPLAINABILITY DASHBOARD - FRONTEND DOCKERFILE
# Production-ready containerized frontend with nginx

# Use nginx alpine for small footprint and production performance
FROM nginx:alpine

# Set working directory
WORKDIR /usr/share/nginx/html

# Remove default nginx files
RUN rm -rf /usr/share/nginx/html/*

# Copy frontend application files
COPY frontend/ .

# Create nginx configuration for the frontend
COPY <<EOF /etc/nginx/conf.d/default.conf
server {
    listen 80;
    server_name localhost;
    
    # Root directory for frontend files
    root /usr/share/nginx/html;
    index index.html;
    
    # Main application route
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # Static assets with caching (except JS files for development)
    location ~* \.(css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }
    
    # JavaScript files - no caching for development
    location ~* \.js$ {
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
        add_header Access-Control-Allow-Origin "*";
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # API proxy to backend (for development)
    location /api/ {
        proxy_pass http://backend:8000/api/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # CORS headers
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization" always;
        
        # Handle preflight requests
        if (\$request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 204;
        }
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Error pages
    error_page 404 /index.html;
    error_page 500 502 503 504 /index.html;
}
EOF

# Create a startup script that configures environment variables dynamically
COPY <<EOF /docker-entrypoint.d/configure-environment.sh
#!/bin/sh
set -e

# Configure environment variables
BACKEND_URL=\${BACKEND_URL:-http://localhost:8000}
SUPABASE_URL=\${SUPABASE_URL:-https://tlduggpohclrgxbvuzhd.supabase.co}
SUPABASE_ANON_KEY=\${SUPABASE_ANON_KEY:-eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZHVnZ3BvaGNscmd4YnZ1emhkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3MDEwMzgsImV4cCI6MjA2NjI3NzAzOH0.Eud_SDfGulErh3yhqjaIMqM37eghuz-PVeRxknzxRfk}

echo "Configuring frontend environment variables..."
echo "Backend URL: \$BACKEND_URL"
echo "Supabase URL: \$SUPABASE_URL"

# Create environment configuration file
cat > /usr/share/nginx/html/env-config.js << EOL
// Environment configuration injected at runtime
window.BACKEND_URL = '\$BACKEND_URL';
window.SUPABASE_URL = '\$SUPABASE_URL';
window.SUPABASE_ANON_KEY = '\$SUPABASE_ANON_KEY';
EOL

echo "Frontend configuration complete"
EOF

# Make the script executable
RUN chmod +x /docker-entrypoint.d/configure-environment.sh

# Expose port 80
EXPOSE 80

# Add health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# Labels for better container management
LABEL maintainer="Zurich Insurance"
LABEL description="Zurich Claims Explainability Dashboard Frontend"
LABEL version="1.0.0"

# Start nginx
CMD ["nginx", "-g", "daemon off;"] 