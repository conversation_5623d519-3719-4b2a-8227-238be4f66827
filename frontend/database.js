/**
 * Database Integration Module
 * Handles all database operations for the Claims Review Frontend
 * Integrates with existing Supabase setup and API endpoints
 */

class DatabaseManager {
    constructor(config) {
        this.config = config;
        this.supabase = null;
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
        
        this.initializeSupabase();
    }

    async initializeSupabase() {
        try {
            console.log('🔗 Initializing real Supabase connection...');
            console.log('🔗 Supabase config:', this.config.SUPABASE_CONFIG);

            // Check if Supabase client is available
            if (typeof window.supabase === 'undefined') {
                console.error('❌ Supabase client not loaded! Make sure to include the Supabase JS library.');
                throw new Error('Supabase client not available');
            }

            // Initialize real Supabase client with service role key for full access
            this.supabase = window.supabase.createClient(
                this.config.SUPABASE_CONFIG.url,
                this.config.SUPABASE_CONFIG.serviceRoleKey
            );

            console.log('✅ Real Supabase connection initialized:', this.supabase);

            // Test the connection
            const { data, error } = await this.supabase
                .from('claims')
                .select('count(*)')
                .limit(1);

            if (error) {
                console.warn('⚠️ Supabase connection test failed:', error);
            } else {
                console.log('✅ Supabase connection test successful');
            }

        } catch (error) {
            console.error('💥 Failed to initialize Supabase:', error);
            console.log('🔄 Falling back to mock implementation...');

            // Fallback to mock implementation
            this.supabase = {
                from: (table) => ({
                    select: (columns = '*') => ({
                        eq: (column, value) => ({
                            single: () => this.mockDatabaseQuery(table, { [column]: value }, true),
                            execute: () => this.mockDatabaseQuery(table, { [column]: value })
                        }),
                        execute: () => this.mockDatabaseQuery(table)
                    }),
                    update: (data) => ({
                        eq: (column, value) => ({
                            execute: () => this.mockDatabaseUpdate(table, data, { [column]: value })
                        })
                    }),
                    insert: (data) => ({
                        execute: () => this.mockDatabaseInsert(table, data)
                    })
                })
            };
        }
    }

    async fetchClaimData(claimReference) {
        try {
            // Check cache first
            const cacheKey = `claim_${claimReference}`;
            const cached = this.getCachedData(cacheKey);
            if (cached) {
                console.log('Returning cached claim data');
                return cached;
            }

            console.log(`Fetching claim data for: ${claimReference}`);
            
            // Fetch claim from database
            const claimResponse = await this.supabase
                .from(this.config.DATABASE_SCHEMA.claims.table)
                .select('*')
                .eq(this.config.DATABASE_SCHEMA.claims.columns.claimReference, claimReference)
                .single();

            if (!claimResponse.data) {
                throw new Error(`Claim ${claimReference} not found`);
            }

            // Fetch attachments
            const attachmentsResponse = await this.supabase
                .from(this.config.DATABASE_SCHEMA.attachments.table)
                .select('*')
                .eq(this.config.DATABASE_SCHEMA.attachments.columns.claimReference, claimReference)
                .execute();

            const result = {
                claim: claimResponse.data,
                attachments: attachmentsResponse.data || []
            };

            // Cache the result
            this.setCachedData(cacheKey, result);
            
            return result;

        } catch (error) {
            console.error('Error fetching claim data:', error);
            throw error;
        }
    }

    async updateClaimStatus(claimReference, status, notes = null) {
        try {
            const updateData = {
                [this.config.DATABASE_SCHEMA.claims.columns.workflowStatus]: status,
                [this.config.DATABASE_SCHEMA.claims.columns.updatedAt]: new Date().toISOString()
            };

            if (notes) {
                updateData.notes = notes;
            }

            const response = await this.supabase
                .from(this.config.DATABASE_SCHEMA.claims.table)
                .update(updateData)
                .eq(this.config.DATABASE_SCHEMA.claims.columns.claimReference, claimReference)
                .execute();

            // Clear cache for this claim
            this.clearCachedData(`claim_${claimReference}`);

            return response;

        } catch (error) {
            console.error('Error updating claim status:', error);
            throw error;
        }
    }

    async fetchAnalysisData(claimReference) {
        try {
            const claimData = await this.fetchClaimData(claimReference);
            const claim = claimData.claim;

            // Parse analysis levels from JSONB columns
            const analysisData = {};

            // Parse each level of analysis with simplified naming
            for (let level = 1; level <= 4; level++) {
                const columnName = `0${level}_level_analysis`;
                const rawData = claim[columnName];

                if (rawData) {
                    try {
                        // Handle both string and object formats
                        analysisData[`level${level}`] = typeof rawData === 'string'
                            ? JSON.parse(rawData)
                            : rawData;
                    } catch (parseError) {
                        console.warn(`Failed to parse ${columnName}:`, parseError);
                        analysisData[`level${level}`] = null;
                    }
                }
            }

            return {
                claim,
                attachments: claimData.attachments,
                analysis: analysisData
            };

        } catch (error) {
            console.error('Error fetching analysis data:', error);
            throw error;
        }
    }

    async callExplainabilityAPI(claimReference, requestType = 'highlights') {
        try {
            console.log(`Calling explainability API for claim: ${claimReference}, type: ${requestType}`);

            const response = await fetch(`${this.config.API_CONFIG.baseUrl}${this.config.API_CONFIG.endpoints.explainability}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    claim_reference: claimReference,
                    request_type: requestType,
                    include_highlights: true,
                    include_explanations: true
                })
            });

            if (!response.ok) {
                console.warn(`Explainability API request failed: ${response.status}`);
                return null;
            }

            const result = await response.json();
            console.log('Successfully called explainability API');
            return result;

        } catch (error) {
            console.error('Error calling explainability API:', error);
            return null;
        }
    }

    async generateHighlightsFromAnalysis(analysisData, documentText, claimReference) {
        console.log('Generating highlights from analysis data');

        const highlights = [];
        let highlightId = 0;

        // Extract highlights from Level 1 analysis (extracted fields)
        if (analysisData.level1 && analysisData.level1.extracted_fields) {
            Object.entries(analysisData.level1.extracted_fields).forEach(([field, data]) => {
                if (data.value && documentText) {
                    const value = String(data.value);
                    const index = documentText.toLowerCase().indexOf(value.toLowerCase());
                    if (index !== -1) {
                        highlights.push({
                            id: `l1_${highlightId++}`,
                            start: index,
                            end: index + value.length,
                            text: value,
                            field: field,
                            confidence: data.confidence || 0.8,
                            explanation: `Level 1 Analysis: ${field.replace('_', ' ')}`,
                            type: 'extracted_field',
                            level: 'Level 1'
                        });
                    }
                }
            });
        }

        // Extract highlights from Level 2 analysis (coverage decisions)
        if (analysisData.level2 && analysisData.level2.coverage_decision) {
            const decision = analysisData.level2.coverage_decision;
            if (decision.reasoning && documentText) {
                // Look for key phrases in the reasoning
                const keyPhrases = this.extractKeyPhrasesFromReasoning(decision.reasoning);
                keyPhrases.forEach(phrase => {
                    const index = documentText.toLowerCase().indexOf(phrase.toLowerCase());
                    if (index !== -1) {
                        highlights.push({
                            id: `l2_${highlightId++}`,
                            start: index,
                            end: index + phrase.length,
                            text: phrase,
                            field: 'coverage_decision',
                            confidence: decision.confidence || 0.75,
                            explanation: `Level 2 Analysis: Coverage determination`,
                            type: 'coverage_analysis',
                            level: 'Level 2'
                        });
                    }
                });
            }
        }

        // Extract highlights from Level 3 analysis (fault determination)
        if (analysisData.level3 && analysisData.level3.fault_allocation) {
            const faultData = analysisData.level3.fault_allocation;
            if (faultData.reasoning && documentText) {
                const keyPhrases = this.extractKeyPhrasesFromReasoning(faultData.reasoning);
                keyPhrases.forEach(phrase => {
                    const index = documentText.toLowerCase().indexOf(phrase.toLowerCase());
                    if (index !== -1) {
                        highlights.push({
                            id: `l3_${highlightId++}`,
                            start: index,
                            end: index + phrase.length,
                            text: phrase,
                            field: 'fault_allocation',
                            confidence: faultData.confidence || 0.7,
                            explanation: `Level 3 Analysis: Fault determination`,
                            type: 'fault_analysis',
                            level: 'Level 3'
                        });
                    }
                });
            }
        }

        // Extract highlights from Level 4 analysis (quantum calculation)
        if (analysisData.level4 && analysisData.level4.settlement_recommendation) {
            const settlement = analysisData.level4.settlement_recommendation;
            if (settlement.breakdown && documentText) {
                Object.entries(settlement.breakdown).forEach(([component, amount]) => {
                    const amountStr = String(amount);
                    const index = documentText.toLowerCase().indexOf(amountStr.toLowerCase());
                    if (index !== -1) {
                        highlights.push({
                            id: `l4_${highlightId++}`,
                            start: index,
                            end: index + amountStr.length,
                            text: amountStr,
                            field: component,
                            confidence: settlement.confidence || 0.8,
                            explanation: `Level 4 Analysis: ${component.replace('_', ' ')} calculation`,
                            type: 'quantum_analysis',
                            level: 'Level 4'
                        });
                    }
                });
            }
        }

        // If no highlights found from Supabase data, optionally call explainability API
        if (highlights.length === 0 && claimReference) {
            console.log('No highlights from Supabase data, calling explainability API as fallback');
            const apiResult = await this.callExplainabilityAPI(claimReference, 'highlights');
            if (apiResult && apiResult.highlights) {
                return {
                    highlights: apiResult.highlights,
                    overall_confidence: apiResult.overall_confidence || 0.8,
                    processing_time: apiResult.processing_time || 0,
                    note: 'Highlights from explainability API (fallback)'
                };
            }
        }

        return {
            highlights: highlights,
            overall_confidence: this.calculateOverallConfidence(highlights),
            processing_time: 0,
            note: 'Highlights generated from Supabase analysis data'
        };
    }

    extractKeyPhrasesFromReasoning(reasoning) {
        // Extract meaningful phrases from reasoning text
        const phrases = [];
        const sentences = reasoning.split(/[.!?]+/);

        sentences.forEach(sentence => {
            // Look for phrases that might be in the document
            const words = sentence.trim().split(/\s+/);
            if (words.length >= 3 && words.length <= 8) {
                // Extract phrases of reasonable length
                const phrase = words.join(' ').trim();
                if (phrase.length > 10 && phrase.length < 100) {
                    phrases.push(phrase);
                }
            }
        });

        return phrases.slice(0, 5); // Limit to 5 phrases per reasoning section
    }

    calculateOverallConfidence(highlights) {
        if (highlights.length === 0) return 0;
        const totalConfidence = highlights.reduce((sum, h) => sum + h.confidence, 0);
        return totalConfidence / highlights.length;
    }

    async triggerAnalysisLevel(claimReference, level, inputData = null) {
        try {
            const endpoints = {
                1: this.config.API_CONFIG.endpoints.level01Analysis,
                2: this.config.API_CONFIG.endpoints.level02Coverage,
                3: this.config.API_CONFIG.endpoints.level03Fault,
                4: this.config.API_CONFIG.endpoints.level04Quantum
            };

            const endpoint = endpoints[level];
            if (!endpoint) {
                throw new Error(`Invalid analysis level: ${level}`);
            }

            const requestBody = {
                claim_reference: claimReference,
                ...inputData
            };

            const response = await fetch(`${this.config.API_CONFIG.baseUrl}${endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`Analysis API request failed: ${response.status}`);
            }

            const analysisResult = await response.json();
            
            // Clear cache to force refresh
            this.clearCachedData(`claim_${claimReference}`);
            
            return analysisResult;

        } catch (error) {
            console.error(`Error triggering level ${level} analysis:`, error);
            throw error;
        }
    }

    // Cache management methods
    getCachedData(key) {
        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.data;
        }
        return null;
    }

    setCachedData(key, data) {
        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });
    }

    clearCachedData(key) {
        this.cache.delete(key);
    }

    clearAllCache() {
        this.cache.clear();
    }

    // Mock database methods for development/testing
    async mockDatabaseQuery(table, conditions = {}, single = false) {
        // Simulate database delay
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Return mock data based on table
        if (table === 'claims') {
            const mockClaim = {
                id: '123e4567-e89b-12d3-a456-426614174000',
                claim_reference: 'CLM-85228383',
                email_subject: 'Vehicle Accident Claim - Policy #VEH-2024-001',
                email_body: 'Dear Claims Team, I am writing to report a vehicle accident...',
                '01_level_analysis': JSON.stringify({
                    confidence: 0.92,
                    extracted_fields: {
                        claimant_name: 'John Smith',
                        incident_date: '2024-03-15',
                        incident_location: 'Main St & Oak Ave, Toronto, ON',
                        claim_type: 'Vehicle Accident',
                        estimated_amount: '$15,000',
                        policy_number: 'VEH-2024-001'
                    }
                }),
                '02_level_analysis': JSON.stringify({
                    confidence: 0.88,
                    coverage_decision: 'COVERED',
                    coverage_details: 'Comprehensive coverage applies. Deductible: $500'
                }),
                '03_level_analysis': JSON.stringify({
                    confidence: 0.85,
                    fault_allocation: { 'Insured Party': 25, 'Third Party': 75 }
                }),
                '04_level_analysis': JSON.stringify({
                    confidence: 0.90,
                    settlement_recommendation: '$11,250',
                    quantum_breakdown: {
                        'Vehicle Damage': '$8,500',
                        'Medical Expenses': '$2,000',
                        'Rental Car': '$750'
                    }
                }),
                workflow_status: 'UNDER_REVIEW',
                created_at: new Date().toISOString()
            };
            
            return single ? { data: mockClaim } : { data: [mockClaim] };
        }
        
        if (table === 'attachments') {
            return { data: [] };
        }
        
        return { data: null };
    }

    async mockDatabaseUpdate(table, data, conditions) {
        await new Promise(resolve => setTimeout(resolve, 300));
        return { data: { ...data, updated_at: new Date().toISOString() } };
    }

    async mockDatabaseInsert(table, data) {
        await new Promise(resolve => setTimeout(resolve, 300));
        return { data: { ...data, id: Date.now().toString() } };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DatabaseManager;
} else {
    window.DatabaseManager = DatabaseManager;
}
