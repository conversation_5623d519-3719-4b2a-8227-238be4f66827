/**
 * Database Integration Module
 * Handles all database operations for the Claims Review Frontend
 * Integrates with existing Supabase setup and API endpoints
 */

class DatabaseManager {
    constructor(config) {
        this.config = config;
        this.supabase = null;
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
        
        this.initializeSupabase();
    }

    async initializeSupabase() {
        try {
            // Initialize Supabase client
            // In a real implementation, this would use the Supabase JavaScript client
            console.log('Initializing Supabase connection...');
            
            // For now, we'll simulate the connection
            this.supabase = {
                from: (table) => ({
                    select: (columns = '*') => ({
                        eq: (column, value) => ({
                            single: () => this.mockDatabaseQuery(table, { [column]: value }, true),
                            execute: () => this.mockDatabaseQuery(table, { [column]: value })
                        }),
                        execute: () => this.mockDatabaseQuery(table)
                    }),
                    update: (data) => ({
                        eq: (column, value) => ({
                            execute: () => this.mockDatabaseUpdate(table, data, { [column]: value })
                        })
                    }),
                    insert: (data) => ({
                        execute: () => this.mockDatabaseInsert(table, data)
                    })
                })
            };
            
            console.log('Supabase connection initialized');
        } catch (error) {
            console.error('Failed to initialize Supabase:', error);
        }
    }

    async fetchClaimData(claimReference) {
        try {
            // Check cache first
            const cacheKey = `claim_${claimReference}`;
            const cached = this.getCachedData(cacheKey);
            if (cached) {
                console.log('Returning cached claim data');
                return cached;
            }

            console.log(`Fetching claim data for: ${claimReference}`);
            
            // Fetch claim from database
            const claimResponse = await this.supabase
                .from(this.config.DATABASE_SCHEMA.claims.table)
                .select('*')
                .eq(this.config.DATABASE_SCHEMA.claims.columns.claimReference, claimReference)
                .single();

            if (!claimResponse.data) {
                throw new Error(`Claim ${claimReference} not found`);
            }

            // Fetch attachments
            const attachmentsResponse = await this.supabase
                .from(this.config.DATABASE_SCHEMA.attachments.table)
                .select('*')
                .eq(this.config.DATABASE_SCHEMA.attachments.columns.claimReference, claimReference)
                .execute();

            const result = {
                claim: claimResponse.data,
                attachments: attachmentsResponse.data || []
            };

            // Cache the result
            this.setCachedData(cacheKey, result);
            
            return result;

        } catch (error) {
            console.error('Error fetching claim data:', error);
            throw error;
        }
    }

    async updateClaimStatus(claimReference, status, notes = null) {
        try {
            const updateData = {
                [this.config.DATABASE_SCHEMA.claims.columns.workflowStatus]: status,
                [this.config.DATABASE_SCHEMA.claims.columns.updatedAt]: new Date().toISOString()
            };

            if (notes) {
                updateData.notes = notes;
            }

            const response = await this.supabase
                .from(this.config.DATABASE_SCHEMA.claims.table)
                .update(updateData)
                .eq(this.config.DATABASE_SCHEMA.claims.columns.claimReference, claimReference)
                .execute();

            // Clear cache for this claim
            this.clearCachedData(`claim_${claimReference}`);

            return response;

        } catch (error) {
            console.error('Error updating claim status:', error);
            throw error;
        }
    }

    async fetchAnalysisData(claimReference) {
        try {
            const claimData = await this.fetchClaimData(claimReference);
            const claim = claimData.claim;

            // Parse analysis levels from JSONB columns
            const analysisData = {};

            // Parse each level of analysis
            for (let level = 1; level <= 4; level++) {
                const columnName = `0${level}_level_analysis`;
                const rawData = claim[columnName];
                
                if (rawData) {
                    try {
                        // Handle both string and object formats
                        analysisData[columnName] = typeof rawData === 'string' 
                            ? JSON.parse(rawData) 
                            : rawData;
                    } catch (parseError) {
                        console.warn(`Failed to parse ${columnName}:`, parseError);
                        analysisData[columnName] = null;
                    }
                }
            }

            return {
                claim,
                attachments: claimData.attachments,
                analysis: analysisData
            };

        } catch (error) {
            console.error('Error fetching analysis data:', error);
            throw error;
        }
    }

    async generateHighlights(claimReference, documentText) {
        try {
            console.log(`Generating highlights for claim: ${claimReference}`);

            // Call the existing AI explainability API
            const response = await fetch(`${this.config.API_CONFIG.baseUrl}${this.config.API_CONFIG.endpoints.highlights}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.config.SUPABASE_CONFIG.serviceRoleKey}`
                },
                body: JSON.stringify({
                    claim_reference: claimReference,
                    document_text: documentText,
                    highlight_types: ['extracted_fields', 'key_entities', 'important_phrases'],
                    confidence_threshold: 0.7
                })
            });

            if (!response.ok) {
                console.warn(`Highlight API request failed: ${response.status}, falling back to mock data`);
                return this.generateMockHighlights(documentText, claimReference);
            }

            const highlightData = await response.json();

            // Validate and format the response
            if (highlightData && highlightData.highlights) {
                return {
                    highlights: highlightData.highlights.map((highlight, index) => ({
                        id: highlight.id || `highlight_${index}`,
                        start: highlight.start_position || highlight.start,
                        end: highlight.end_position || highlight.end,
                        text: highlight.text || highlight.highlighted_text,
                        field: highlight.field_name || highlight.field,
                        confidence: highlight.confidence || 0.8,
                        explanation: highlight.explanation || `AI identified this as ${highlight.field}`,
                        type: highlight.type || 'extracted_field'
                    })),
                    overall_confidence: highlightData.overall_confidence || 0.8,
                    processing_time: highlightData.processing_time || 0
                };
            }

            return this.generateMockHighlights(documentText, claimReference);

        } catch (error) {
            console.error('Error generating highlights:', error);
            return this.generateMockHighlights(documentText, claimReference);
        }
    }

    generateMockHighlights(documentText, claimReference) {
        console.log('Generating mock highlights for development/testing');

        const mockHighlights = [];
        const commonFields = [
            { field: 'claimant_name', patterns: ['John Smith', 'Jane Doe', 'Robert Johnson'] },
            { field: 'incident_date', patterns: ['2024-03-15', 'March 15, 2024', '15/03/2024'] },
            { field: 'incident_location', patterns: ['Main St & Oak Ave', 'Toronto, ON', 'Highway 401'] },
            { field: 'policy_number', patterns: ['VEH-2024-001', 'POL-123456', 'AUTO-789012'] },
            { field: 'claim_type', patterns: ['Vehicle Accident', 'Property Damage', 'Collision'] },
            { field: 'estimated_amount', patterns: ['$15,000', '$12,500', '$8,750'] }
        ];

        commonFields.forEach((fieldData, fieldIndex) => {
            fieldData.patterns.forEach((pattern, patternIndex) => {
                const index = documentText.toLowerCase().indexOf(pattern.toLowerCase());
                if (index !== -1) {
                    mockHighlights.push({
                        id: `mock_${fieldIndex}_${patternIndex}`,
                        start: index,
                        end: index + pattern.length,
                        text: pattern,
                        field: fieldData.field,
                        confidence: 0.85 + (Math.random() * 0.1), // 85-95% confidence
                        explanation: `AI identified this as ${fieldData.field.replace('_', ' ')}`,
                        type: 'mock_extracted_field'
                    });
                }
            });
        });

        return {
            highlights: mockHighlights,
            overall_confidence: 0.87,
            processing_time: 150,
            note: 'Mock highlights generated for development'
        };
    }

    async triggerAnalysisLevel(claimReference, level, inputData = null) {
        try {
            const endpoints = {
                1: this.config.API_CONFIG.endpoints.level01Analysis,
                2: this.config.API_CONFIG.endpoints.level02Coverage,
                3: this.config.API_CONFIG.endpoints.level03Fault,
                4: this.config.API_CONFIG.endpoints.level04Quantum
            };

            const endpoint = endpoints[level];
            if (!endpoint) {
                throw new Error(`Invalid analysis level: ${level}`);
            }

            const requestBody = {
                claim_reference: claimReference,
                ...inputData
            };

            const response = await fetch(`${this.config.API_CONFIG.baseUrl}${endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`Analysis API request failed: ${response.status}`);
            }

            const analysisResult = await response.json();
            
            // Clear cache to force refresh
            this.clearCachedData(`claim_${claimReference}`);
            
            return analysisResult;

        } catch (error) {
            console.error(`Error triggering level ${level} analysis:`, error);
            throw error;
        }
    }

    // Cache management methods
    getCachedData(key) {
        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.data;
        }
        return null;
    }

    setCachedData(key, data) {
        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });
    }

    clearCachedData(key) {
        this.cache.delete(key);
    }

    clearAllCache() {
        this.cache.clear();
    }

    // Mock database methods for development/testing
    async mockDatabaseQuery(table, conditions = {}, single = false) {
        // Simulate database delay
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Return mock data based on table
        if (table === 'claims') {
            const mockClaim = {
                id: '123e4567-e89b-12d3-a456-426614174000',
                claim_reference: 'CLM-85228383',
                email_subject: 'Vehicle Accident Claim - Policy #VEH-2024-001',
                email_body: 'Dear Claims Team, I am writing to report a vehicle accident...',
                '01_level_analysis': JSON.stringify({
                    confidence: 0.92,
                    extracted_fields: {
                        claimant_name: 'John Smith',
                        incident_date: '2024-03-15',
                        incident_location: 'Main St & Oak Ave, Toronto, ON',
                        claim_type: 'Vehicle Accident',
                        estimated_amount: '$15,000',
                        policy_number: 'VEH-2024-001'
                    }
                }),
                '02_level_analysis': JSON.stringify({
                    confidence: 0.88,
                    coverage_decision: 'COVERED',
                    coverage_details: 'Comprehensive coverage applies. Deductible: $500'
                }),
                '03_level_analysis': JSON.stringify({
                    confidence: 0.85,
                    fault_allocation: { 'Insured Party': 25, 'Third Party': 75 }
                }),
                '04_level_analysis': JSON.stringify({
                    confidence: 0.90,
                    settlement_recommendation: '$11,250',
                    quantum_breakdown: {
                        'Vehicle Damage': '$8,500',
                        'Medical Expenses': '$2,000',
                        'Rental Car': '$750'
                    }
                }),
                workflow_status: 'UNDER_REVIEW',
                created_at: new Date().toISOString()
            };
            
            return single ? { data: mockClaim } : { data: [mockClaim] };
        }
        
        if (table === 'attachments') {
            return { data: [] };
        }
        
        return { data: null };
    }

    async mockDatabaseUpdate(table, data, conditions) {
        await new Promise(resolve => setTimeout(resolve, 300));
        return { data: { ...data, updated_at: new Date().toISOString() } };
    }

    async mockDatabaseInsert(table, data) {
        await new Promise(resolve => setTimeout(resolve, 300));
        return { data: { ...data, id: Date.now().toString() } };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DatabaseManager;
} else {
    window.DatabaseManager = DatabaseManager;
}
