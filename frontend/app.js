/**
 * Claims Review Application
 * Integrates mock UI with explainable claims dashboard logic
 */

class ClaimsReviewApp {
    constructor() {
        this.currentClaim = null;
        this.analysisData = null;
        this.documentContent = null;
        this.highlights = new Map();
        this.zoomLevel = 100;
        this.currentTheme = 'light';
        this.isMobileView = false;

        // Initialize configuration and database manager
        this.config = window.ClaimsReviewConfig;
        this.db = new DatabaseManager(this.config);

        this.init();
    }

    async init() {
        this.setupEventListeners();
        this.detectMobileView();
        this.initializeTheme();
        await this.loadClaimData();
    }

    setupEventListeners() {
        // Theme toggle
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }

        // Zoom controls
        const zoomIn = document.getElementById('zoom-in');
        const zoomOut = document.getElementById('zoom-out');
        if (zoomIn) zoomIn.addEventListener('click', () => this.adjustZoom(10));
        if (zoomOut) zoomOut.addEventListener('click', () => this.adjustZoom(-10));

        // Mobile toggle
        const mobileToggle = document.getElementById('mobile-toggle');
        if (mobileToggle) {
            mobileToggle.addEventListener('click', () => this.toggleMobilePanel());
        }

        // Action buttons
        const approveBtn = document.getElementById('approve-btn');
        const escalateBtn = document.getElementById('escalate-btn');
        const exportBtn = document.getElementById('export-btn');
        
        if (approveBtn) approveBtn.addEventListener('click', () => this.handleApprove());
        if (escalateBtn) escalateBtn.addEventListener('click', () => this.handleEscalate());
        if (exportBtn) exportBtn.addEventListener('click', () => this.handleExport());

        // Document highlights
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('highlight')) {
                this.handleHighlightClick(e.target);
            }
        });

        // Window resize
        window.addEventListener('resize', () => this.detectMobileView());
    }

    detectMobileView() {
        const wasMobile = this.isMobileView;
        this.isMobileView = window.innerWidth <= 768;
        
        if (wasMobile !== this.isMobileView) {
            this.updateMobileLayout();
        }
    }

    updateMobileLayout() {
        const analysisPanel = document.querySelector('.analysis-panel');
        const mobileToggle = document.getElementById('mobile-toggle');
        
        if (this.isMobileView) {
            if (mobileToggle) mobileToggle.style.display = 'block';
            if (analysisPanel) analysisPanel.classList.remove('mobile-visible');
        } else {
            if (mobileToggle) mobileToggle.style.display = 'none';
            if (analysisPanel) analysisPanel.classList.remove('mobile-visible');
        }
    }

    toggleMobilePanel() {
        const analysisPanel = document.querySelector('.analysis-panel');
        const toggleBtn = document.getElementById('mobile-toggle');
        
        if (analysisPanel) {
            const isVisible = analysisPanel.classList.contains('mobile-visible');
            analysisPanel.classList.toggle('mobile-visible');
            
            if (toggleBtn) {
                toggleBtn.querySelector('.toggle-text').textContent = 
                    isVisible ? 'Show Analysis' : 'Hide Analysis';
            }
        }
    }

    initializeTheme() {
        // Check for saved theme preference or default to light
        const savedTheme = localStorage.getItem('claims-review-theme') || 'light';
        this.setTheme(savedTheme);
    }

    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
    }

    setTheme(theme) {
        this.currentTheme = theme;
        document.documentElement.setAttribute('data-color-scheme', theme);
        localStorage.setItem('claims-review-theme', theme);
        
        const themeIcon = document.querySelector('.theme-icon');
        if (themeIcon) {
            themeIcon.textContent = theme === 'light' ? '🌙' : '☀️';
        }
    }

    adjustZoom(delta) {
        this.zoomLevel = Math.max(50, Math.min(200, this.zoomLevel + delta));
        
        const documentContent = document.getElementById('document-content');
        const zoomDisplay = document.querySelector('.zoom-level');
        
        if (documentContent) {
            documentContent.style.transform = `scale(${this.zoomLevel / 100})`;
            documentContent.style.transformOrigin = 'top left';
        }
        
        if (zoomDisplay) {
            zoomDisplay.textContent = `${this.zoomLevel}%`;
        }
    }

    async loadClaimData() {
        this.showLoading(true);
        
        try {
            // Get claim reference from URL or default
            const urlParams = new URLSearchParams(window.location.search);
            const claimRef = urlParams.get('claim') || 'CLM-85228383';
            
            await this.fetchClaimFromDatabase(claimRef);
            
            if (!this.currentClaim) {
                // Fallback to mock data if database fetch fails
                await this.loadMockData(claimRef);
            }
            
            this.renderClaimData();
            this.renderAnalysisLevels();
            this.generateDocumentHighlights();
            
        } catch (error) {
            console.error('Error loading claim data:', error);
            await this.loadMockData('CLM-85228383');
            this.renderClaimData();
            this.renderAnalysisLevels();
        } finally {
            this.showLoading(false);
        }
    }

    async fetchClaimFromDatabase(claimRef) {
        try {
            console.log(`Fetching claim ${claimRef} from database...`);

            // Fetch claim data directly from Supabase
            const claimData = await this.fetchClaimData(claimRef);
            if (!claimData) {
                console.warn(`No claim found for ${claimRef}`);
                return null;
            }

            this.currentClaim = claimData;

            // Fetch attachments and extract documents
            const attachments = await this.fetchAttachments(claimRef);
            this.attachments = attachments;

            // Display all content first (email + documents)
            this.displayAllDocumentContent();

            // Then generate AI highlights
            await this.generateAIHighlightsForAllContent(claimRef);

            return { claim: claimData, attachments: attachments };

        } catch (error) {
            console.error('Database fetch error:', error);
            return null;
        }
    }

    async fetchClaimData(claimReference) {
        try {
            console.log(`🔍 Fetching claim data for ${claimReference}`);

            const { data, error } = await this.db.supabase
                .from('claims')
                .select('*')
                .eq('claim_reference', claimReference)
                .single();

            if (error) {
                console.error('Supabase error:', error);
                throw new Error(`Database error: ${error.message}`);
            }

            if (!data) {
                throw new Error(`Claim ${claimReference} not found in database`);
            }

            console.log('✅ Claim data fetched successfully');
            return data;
        } catch (error) {
            console.error('❌ Failed to fetch claim data:', error);
            throw error;
        }
    }

    async fetchAttachments(claimReference) {
        try {
            console.log(`📎 Fetching attachments for ${claimReference}`);

            const { data, error } = await this.db.supabase
                .from('attachments')
                .select('*')
                .eq('claim_reference', claimReference);

            if (error) {
                console.error('Supabase error:', error);
                throw new Error(`Database error: ${error.message}`);
            }

            console.log(`✅ Found ${data?.length || 0} attachments`);

            // Extract individual documents from OCR structure
            const extractedDocuments = [];

            for (const attachment of data || []) {
                try {
                    const ocrText = attachment.ocr_text;
                    if (!ocrText || !ocrText.trim()) {
                        console.log(`Skipping attachment ${attachment.id} - no OCR text`);
                        continue;
                    }

                    // Parse the OCR JSON structure
                    const ocrData = JSON.parse(ocrText);
                    const zurichResponse = ocrData.zurich_ocr_response || {};
                    const results = zurichResponse.results || [];

                    if (results.length === 0) {
                        console.log(`Skipping attachment ${attachment.id} - no results in OCR data`);
                        continue;
                    }

                    // Extract each document from the results
                    for (const result of results) {
                        if (!result.success || !result.extracted_text || !result.extracted_text.trim()) {
                            continue;
                        }

                        // URL decode the filename for display
                        const displayFilename = decodeURIComponent(result.filename || `Document_${extractedDocuments.length + 1}`);

                        // Create a document object
                        extractedDocuments.push({
                            id: attachment.id,
                            file_name: displayFilename,
                            filename: displayFilename,
                            ocr_text: result.extracted_text,
                            confidence: result.confidence || 0.0,
                            content_type: attachment.content_type,
                            file_size: result.extracted_text.length,
                            original_attachment_id: attachment.id,
                            claim_reference: attachment.claim_reference
                        });

                        console.log(`✅ Extracted document: ${displayFilename} (${result.extracted_text.length} chars)`);
                    }

                } catch (parseError) {
                    console.error(`Error parsing OCR data for attachment ${attachment.id}:`, parseError);
                    continue;
                }
            }

            console.log(`✅ Extracted ${extractedDocuments.length} documents from ${data?.length || 0} attachments`);
            return extractedDocuments;
        } catch (error) {
            console.error('❌ Failed to fetch attachments:', error);
            throw error;
        }
    }

    displayAllDocumentContent() {
        const container = document.getElementById('document-content');
        if (!container) {
            console.warn('Document content container not found');
            return;
        }

        const claimData = this.currentClaim;
        const attachments = this.attachments || [];

        let content = '';

        // 1. Display email body first if available
        if (claimData && claimData.email_body) {
            content += `
                <div class="document-section mb-6">
                    <div class="document-header bg-blue-50 p-4 rounded-t-lg border-b">
                        <h3 class="text-lg font-semibold text-blue-800 flex items-center">
                            <span class="mr-2">📧</span>
                            Email Body
                            <span class="ml-auto text-sm font-normal text-blue-600">Original Claim Email</span>
                        </h3>
                    </div>
                    <div class="document-content p-4 bg-white rounded-b-lg border border-t-0">
                        <div class="prose max-w-none email-content">
                            ${this.escapeHtml(claimData.email_body)}
                        </div>
                    </div>
                </div>
            `;
        }

        // 2. Display all OCR extracted content with filenames (expandable/collapsible)
        if (attachments && attachments.length > 0) {
            attachments.forEach((attachment, index) => {
                const fileName = attachment.file_name || `Document ${index + 1}`;
                const ocrText = attachment.ocr_text;

                if (ocrText && ocrText.trim()) {
                    content += `
                        <div class="document-section mb-6" data-document-index="${index}" data-filename="${this.escapeHtml(fileName)}">
                            <div class="document-header bg-gray-50 p-4 rounded-t-lg border-b cursor-pointer hover:bg-gray-100 transition-colors"
                                 onclick="this.nextElementSibling.classList.toggle('hidden'); this.querySelector('.toggle-icon').classList.toggle('rotate-180');">
                                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                                    <span class="mr-2">📄</span>
                                    ${this.escapeHtml(fileName)}
                                    <span class="ml-auto flex items-center">
                                        <span class="document-status text-sm font-normal text-gray-600 mr-2">Ready for AI Analysis</span>
                                        <svg class="toggle-icon w-5 h-5 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </span>
                                </h3>
                                <p class="text-sm text-gray-600">OCR Extracted Content • Click to expand/collapse</p>
                            </div>
                            <div class="document-content p-4 bg-white rounded-b-lg border border-t-0 hidden">
                                <div class="prose max-w-none ocr-content" id="ocrContent${index}">
                                    <div class="plain-text">${this.escapeHtml(ocrText)}</div>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    // Show filename even if no OCR text
                    content += `
                        <div class="document-section mb-6" data-document-index="${index}" data-filename="${this.escapeHtml(fileName)}">
                            <div class="document-header bg-gray-50 p-4 rounded-lg border">
                                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                                    <span class="mr-2">📄</span>
                                    ${this.escapeHtml(fileName)}
                                    <span class="ml-auto text-sm font-normal text-gray-500">No Content</span>
                                </h3>
                                <p class="text-sm text-gray-600">No OCR Text Available</p>
                            </div>
                        </div>
                    `;
                }
            });
        }

        // If no email body and no attachments
        if (!content) {
            content = `
                <div class="document-section">
                    <div class="no-data text-center py-8">
                        <p class="text-gray-500">No email body or document content available for this claim.</p>
                    </div>
                </div>
            `;
        }

        container.innerHTML = content;
        console.log('✅ Email body and OCR content displayed successfully');
    }

    async generateAIHighlightsForAllContent(claimReference) {
        try {
            console.log('🧠 Generating AI highlights for all content...');

            // Update status for each document section
            const documentSections = document.querySelectorAll('.document-section');
            documentSections.forEach(section => {
                const statusElement = section.querySelector('.document-status');
                if (statusElement) {
                    statusElement.textContent = 'Analyzing...';
                    statusElement.className = 'document-status text-sm font-normal text-yellow-600 mr-2';
                }
            });

            // Call the AI explainability API
            const response = await fetch(`${API_CONFIG.baseUrl}${API_CONFIG.endpoints.explainability}/highlights`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ claim_reference: claimReference })
            });

            if (!response.ok) {
                throw new Error(`Backend returned status ${response.status}`);
            }
            const data = await response.json();

            if (data.success) {
                console.log(`✅ Generated ${data.highlights_generated} highlights for ${data.documents_analyzed} documents`);

                // Apply highlights to each document section
                await this.applyHighlightsToAllContent(data.explainability_data);

            } else {
                throw new Error(data.error_message || 'Failed to generate highlights');
            }

        } catch (error) {
            console.error('❌ Failed to generate AI highlights:', error);

            // Update all document sections to show error
            const documentSections = document.querySelectorAll('.document-section');
            documentSections.forEach(section => {
                const statusElement = section.querySelector('.document-status');
                if (statusElement) {
                    statusElement.textContent = 'Analysis Failed';
                    statusElement.className = 'document-status text-sm font-normal text-red-600 mr-2';
                }
            });
        }
    }

    async applyHighlightsToAllContent(explainabilityData) {
        if (!explainabilityData || explainabilityData.length === 0) {
            console.warn('No explainability documents to process');
            return;
        }

        // Get all document sections on the page
        const documentSections = document.querySelectorAll('.document-section[data-document-index]');
        console.log(`Found ${documentSections.length} document sections to process`);
        console.log(`Received ${explainabilityData.length} explainability data entries`);

        // Map explainability data to document sections by matching content
        explainabilityData.forEach((docData, explainDataIndex) => {
            // Try to find matching document section by content similarity
            let matchedSection = null;

            documentSections.forEach((section, sectionIndex) => {
                const contentElement = section.querySelector('.document-content .plain-text');
                if (contentElement) {
                    const sectionText = contentElement.textContent;

                    // Check if any highlights from this explainability data match the section content
                    const hasMatchingHighlights = docData.highlights?.some(highlight =>
                        sectionText.includes(highlight.text)
                    );

                    if (hasMatchingHighlights && !matchedSection) {
                        matchedSection = section;
                    }
                }
            });

            // If we found a match, apply highlights
            if (matchedSection) {
                const statusElement = matchedSection.querySelector('.document-status');
                const contentElement = matchedSection.querySelector('.document-content .plain-text');

                if (contentElement) {
                    // Apply highlights to this document
                    const highlightedText = this.renderOCRWithHighlights(contentElement.textContent, docData);
                    contentElement.innerHTML = highlightedText;

                    // Update status
                    if (statusElement) {
                        statusElement.textContent = `${docData.highlights?.length || 0} Highlights`;
                        statusElement.className = 'document-status text-sm font-normal text-green-600 mr-2';
                    }

                    console.log(`✅ Applied ${docData.highlights?.length || 0} highlights to document: ${matchedSection.dataset.filename}`);
                }
            } else {
                console.warn(`No matching document section found for explainability data ${explainDataIndex}`);
            }
        });

        // Update any sections that didn't get highlights
        documentSections.forEach(section => {
            const statusElement = section.querySelector('.document-status');
            if (statusElement && statusElement.textContent === 'Analyzing...') {
                statusElement.textContent = '0 Highlights';
                statusElement.className = 'document-status text-sm font-normal text-gray-600 mr-2';
            }
        });
    }

    renderOCRWithHighlights(ocrText, explainabilityData) {
        if (!explainabilityData || !explainabilityData.highlights || explainabilityData.highlights.length === 0) {
            // Return plain text if no highlights available
            return this.escapeHtml(ocrText);
        }

        // Sort highlights by start position
        const highlights = explainabilityData.highlights
            .filter(h => h.start >= 0 && h.end <= ocrText.length && h.start < h.end)
            .sort((a, b) => a.start - b.start);

        if (highlights.length === 0) {
            return this.escapeHtml(ocrText);
        }

        // Build highlighted text
        let highlightedText = '';
        let lastEnd = 0;

        highlights.forEach((highlight, index) => {
            // Add text before highlight
            highlightedText += this.escapeHtml(ocrText.substring(lastEnd, highlight.start));

            // Add highlighted text
            highlightedText += `
                <span class="highlight-text inline-block px-2 py-1 rounded cursor-pointer"
                      style="background-color: ${highlight.color};"
                      data-highlight-index="${index}"
                      data-field="${highlight.field}"
                      data-explanation="${this.escapeHtml(highlight.explanation)}"
                      data-contribution="${highlight.contribution_score}"
                      data-type="${highlight.highlight_type}"
                      title="${highlight.explanation}">
                    ${this.escapeHtml(highlight.text)}
                </span>
            `;

            lastEnd = highlight.end;
        });

        // Add remaining text
        highlightedText += this.escapeHtml(ocrText.substring(lastEnd));

        return highlightedText;
    }

    async generateDocumentContent(claimData) {
        const claim = claimData.claim;
        const attachments = claimData.attachments || [];
        const level1Data = claimData.analysis.level1;

        // Extract fields for highlighting
        const fields = level1Data?.extracted_fields || {};

        // Get OCR text from attachments
        let ocrText = '';
        const processedAttachments = attachments.filter(att =>
            att.processing_status === 'completed' && att.ocr_text
        );

        if (processedAttachments.length > 0) {
            ocrText = processedAttachments.map(att => att.ocr_text).join('\n\n');
        }

        // Generate highlights using the API
        let highlightedContent = '';

        if (ocrText) {
            // Use OCR text with highlights generated from analysis data
            try {
                const highlightData = await this.db.generateHighlightsFromAnalysis(this.analysisData, ocrText, claim.claim_reference);
                highlightedContent = this.applyHighlightsToText(ocrText, highlightData.highlights, fields);
            } catch (error) {
                console.warn('Failed to generate highlights, using fallback:', error);
                highlightedContent = this.applyBasicHighlights(ocrText, fields);
            }
        } else {
            // Fallback to email body if no OCR text available
            const emailBody = claim.email_body || 'No document content available';
            highlightedContent = this.applyBasicHighlights(emailBody, fields);
        }

        // Build complete document content
        let content = `
            <div class="document-header">
                <h2>${claim.email_subject || 'Claim Document'}</h2>
                <div class="document-meta">
                    <p><strong>Claim Reference:</strong> ${claim.claim_reference}</p>
                    <p><strong>Status:</strong> <span id="claim-status" class="status ${this.getStatusClass(claim.workflow_status)}">${this.getStatusLabel(claim.workflow_status)}</span></p>
                    ${processedAttachments.length > 0 ? `<p><strong>Documents:</strong> ${processedAttachments.length} processed</p>` : ''}
                </div>
            </div>

            <div class="document-content">
                ${highlightedContent}
            </div>
        `;

        return content;
    }

    applyHighlightsToText(text, highlights, extractedFields) {
        let highlightedText = text;

        // Apply AI-generated highlights first
        if (highlights && highlights.length > 0) {
            highlights.forEach((highlight, index) => {
                const { start, end, field, confidence, text: highlightText } = highlight;

                if (start !== undefined && end !== undefined) {
                    // Use position-based highlighting
                    const before = highlightedText.substring(0, start);
                    const after = highlightedText.substring(end);
                    const highlighted = `<span class="highlight ai-highlight"
                        data-field="${field}"
                        data-confidence="${confidence}"
                        data-highlight-id="${index}"
                        title="AI Confidence: ${Math.round(confidence * 100)}%">${highlightText}</span>`;

                    highlightedText = before + highlighted + after;
                }
            });
        }

        // Apply extracted field highlights as fallback
        Object.entries(extractedFields).forEach(([field, value]) => {
            if (value && typeof value === 'string' && value.length > 2) {
                const regex = new RegExp(`\\b${value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
                highlightedText = highlightedText.replace(regex, (match) => {
                    // Don't double-highlight if already highlighted
                    if (highlightedText.indexOf(`<span class="highlight"`) !== -1 &&
                        highlightedText.indexOf(match) > highlightedText.lastIndexOf('<span class="highlight"')) {
                        return match;
                    }
                    return `<span class="highlight extracted-field"
                        data-field="${field}"
                        data-value="${value}"
                        title="Extracted: ${this.formatFieldLabel(field)}">${match}</span>`;
                });
            }
        });

        return `<div class="highlighted-document">${highlightedText}</div>`;
    }

    applyBasicHighlights(text, fields) {
        let highlightedText = text;

        // Apply basic highlighting based on extracted fields
        Object.entries(fields).forEach(([field, value]) => {
            if (value && typeof value === 'string' && value.length > 2) {
                const regex = new RegExp(`\\b${value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
                highlightedText = highlightedText.replace(regex,
                    `<span class="highlight basic-highlight"
                        data-field="${field}"
                        data-value="${value}"
                        title="Extracted: ${this.formatFieldLabel(field)}">${value}</span>`
                );
            }
        });

        return highlightedText;
    }

    getStatusClass(status) {
        const statusConfig = this.config?.STATUS_MAPPINGS?.workflow_status?.[status];
        return statusConfig?.class || 'status--info';
    }

    getStatusLabel(status) {
        const statusConfig = this.config?.STATUS_MAPPINGS?.workflow_status?.[status];
        return statusConfig?.label || status;
    }

    async loadMockData(claimRef) {
        // Mock data structure matching the database schema
        this.currentClaim = {
            claim_reference: claimRef,
            email_subject: "Vehicle Accident Claim - Policy #VEH-2024-001",
            email_body: "Dear Claims Team, I am writing to report a vehicle accident that occurred on March 15, 2024...",
            workflow_status: "Under Review",
            created_at: new Date().toISOString()
        };

        // Mock analysis data matching the 4-level structure
        this.analysisData = {
            level1: {
                confidence: 0.92,
                extracted_fields: {
                    claimant_name: "John Smith",
                    incident_date: "2024-03-15",
                    incident_location: "Main St & Oak Ave, Toronto, ON",
                    claim_type: "Vehicle Accident",
                    estimated_amount: "$15,000",
                    policy_number: "VEH-2024-001"
                }
            },
            level2: {
                confidence: 0.88,
                coverage_decision: "COVERED",
                coverage_details: "Comprehensive coverage applies. Deductible: $500",
                policy_limits: "$50,000 per incident"
            },
            level3: {
                confidence: 0.85,
                fault_allocation: {
                    "Insured Party": 25,
                    "Third Party": 75
                },
                fault_reasoning: "Based on police report and witness statements"
            },
            level4: {
                confidence: 0.90,
                settlement_recommendation: "$11,250",
                quantum_breakdown: {
                    "Vehicle Damage": "$8,500",
                    "Medical Expenses": "$2,000",
                    "Rental Car": "$750",
                    "Total Before Fault": "$11,250"
                }
            }
        };

        // Mock document content
        this.documentContent = `
            <h2>Vehicle Accident Claim Report</h2>
            <p><strong>Claimant:</strong> <span class="highlight" data-field="claimant_name">John Smith</span></p>
            <p><strong>Policy Number:</strong> <span class="highlight" data-field="policy_number">VEH-2024-001</span></p>
            <p><strong>Date of Incident:</strong> <span class="highlight" data-field="incident_date">March 15, 2024</span></p>
            <p><strong>Location:</strong> <span class="highlight" data-field="incident_location">Main St & Oak Ave, Toronto, ON</span></p>
            
            <h3>Incident Description</h3>
            <p>On <span class="highlight" data-field="incident_date">March 15, 2024</span>, at approximately 2:30 PM, I was involved in a vehicle accident at the intersection of <span class="highlight" data-field="incident_location">Main St & Oak Ave</span>. The other vehicle ran a red light and collided with the passenger side of my vehicle.</p>
            
            <p>The estimated damage to my vehicle is approximately <span class="highlight" data-field="estimated_amount">$15,000</span>. I have obtained repair estimates from two certified auto body shops.</p>
            
            <p>I was taken to the hospital for evaluation and treatment of minor injuries. Medical expenses are estimated at <span class="highlight" data-field="medical_expenses">$2,000</span>.</p>
            
            <p>A police report was filed at the scene (Report #: <span class="highlight" data-field="police_report">PR-2024-0315-001</span>).</p>
        `;
    }

    renderClaimData() {
        if (!this.currentClaim) return;

        // Update header information
        const claimRefElement = document.getElementById('claim-reference');
        const claimStatusElement = document.getElementById('claim-status');
        const documentTitleElement = document.getElementById('document-title');
        const documentClaimRefElement = document.getElementById('document-claim-ref');
        const analysisTimestampElement = document.getElementById('analysis-timestamp');

        if (claimRefElement) {
            claimRefElement.textContent = this.currentClaim.claim_reference;
        }

        if (claimStatusElement) {
            claimStatusElement.textContent = this.currentClaim.workflow_status || 'Under Review';
            claimStatusElement.className = 'status status--info';
        }

        if (documentTitleElement) {
            documentTitleElement.textContent = this.currentClaim.email_subject || 'Claim Document';
        }

        if (documentClaimRefElement) {
            documentClaimRefElement.textContent = this.currentClaim.claim_reference;
        }

        if (analysisTimestampElement) {
            const timestamp = new Date(this.currentClaim.created_at).toLocaleString();
            analysisTimestampElement.textContent = `Last updated: ${timestamp}`;
        }

        // Render document content
        const documentBodyElement = document.getElementById('document-body');
        if (documentBodyElement && this.documentContent) {
            documentBodyElement.innerHTML = this.documentContent;
        }

        // Update claimant name in document
        const claimantNameElement = document.getElementById('claimant-name');
        if (claimantNameElement && this.analysisData?.level1?.extracted_fields?.claimant_name) {
            claimantNameElement.textContent = this.analysisData.level1.extracted_fields.claimant_name;
        }
    }

    renderAnalysisLevels() {
        if (!this.analysisData) return;

        // Calculate overall confidence
        const confidences = [
            this.analysisData.level1?.confidence || 0,
            this.analysisData.level2?.confidence || 0,
            this.analysisData.level3?.confidence || 0,
            this.analysisData.level4?.confidence || 0
        ];

        const overallConfidence = Math.round(confidences.reduce((a, b) => a + b, 0) / confidences.length * 100);
        this.updateConfidenceBar('overall-confidence', overallConfidence);

        // Render each level
        this.renderLevel1Analysis();
        this.renderLevel2Analysis();
        this.renderLevel3Analysis();
        this.renderLevel4Analysis();
        this.renderRecommendations();
    }

    renderLevel1Analysis() {
        const level1Data = this.analysisData.level1;
        if (!level1Data) return;

        const confidence = Math.round(level1Data.confidence * 100);
        this.updateConfidenceBar('level1-confidence', confidence);

        const fieldsContainer = document.getElementById('level1-fields');
        if (fieldsContainer && level1Data.extracted_fields) {
            fieldsContainer.innerHTML = '';

            Object.entries(level1Data.extracted_fields).forEach(([key, value]) => {
                const fieldItem = document.createElement('div');
                fieldItem.className = 'field-item';
                fieldItem.innerHTML = `
                    <span class="field-label">${this.formatFieldLabel(key)}</span>
                    <span class="field-value">${value}</span>
                `;
                fieldsContainer.appendChild(fieldItem);
            });
        }
    }

    renderLevel2Analysis() {
        const level2Data = this.analysisData.level2;
        if (!level2Data) return;

        const confidence = Math.round(level2Data.confidence * 100);
        this.updateConfidenceBar('level2-confidence', confidence);

        const contentContainer = document.getElementById('level2-content');
        if (contentContainer) {
            contentContainer.innerHTML = `
                <div class="coverage-decision">
                    <h4>Coverage Decision</h4>
                    <p><strong>Status:</strong> <span class="status status--${level2Data.coverage_decision === 'COVERED' ? 'success' : 'error'}">${level2Data.coverage_decision}</span></p>
                    <p><strong>Details:</strong> ${level2Data.coverage_details || 'N/A'}</p>
                    <p><strong>Policy Limits:</strong> ${level2Data.policy_limits || 'N/A'}</p>
                </div>
            `;
        }
    }

    renderLevel3Analysis() {
        const level3Data = this.analysisData.level3;
        if (!level3Data) return;

        const confidence = Math.round(level3Data.confidence * 100);
        this.updateConfidenceBar('level3-confidence', confidence);

        const contentContainer = document.getElementById('level3-content');
        if (contentContainer && level3Data.fault_allocation) {
            let faultHtml = '<div class="fault-allocation"><h4>Fault Allocation</h4>';

            Object.entries(level3Data.fault_allocation).forEach(([party, percentage]) => {
                faultHtml += `
                    <div class="field-item">
                        <span class="field-label">${party}</span>
                        <span class="field-value">${percentage}%</span>
                    </div>
                `;
            });

            if (level3Data.fault_reasoning) {
                faultHtml += `<p><strong>Reasoning:</strong> ${level3Data.fault_reasoning}</p>`;
            }

            faultHtml += '</div>';
            contentContainer.innerHTML = faultHtml;
        }
    }

    renderLevel4Analysis() {
        const level4Data = this.analysisData.level4;
        if (!level4Data) return;

        const confidence = Math.round(level4Data.confidence * 100);
        this.updateConfidenceBar('level4-confidence', confidence);

        const contentContainer = document.getElementById('level4-content');
        if (contentContainer) {
            let quantumHtml = '<div class="quantum-breakdown"><h4>Settlement Breakdown</h4>';

            if (level4Data.quantum_breakdown) {
                Object.entries(level4Data.quantum_breakdown).forEach(([item, amount]) => {
                    quantumHtml += `
                        <div class="field-item">
                            <span class="field-label">${item}</span>
                            <span class="field-value">${amount}</span>
                        </div>
                    `;
                });
            }

            if (level4Data.settlement_recommendation) {
                quantumHtml += `
                    <div class="field-item" style="border-top: 2px solid var(--color-border); margin-top: var(--space-12); padding-top: var(--space-12);">
                        <span class="field-label"><strong>Recommended Settlement</strong></span>
                        <span class="field-value"><strong>${level4Data.settlement_recommendation}</strong></span>
                    </div>
                `;
            }

            quantumHtml += '</div>';
            contentContainer.innerHTML = quantumHtml;
        }
    }

    renderRecommendations() {
        const recommendationsContainer = document.getElementById('recommendations-content');
        if (!recommendationsContainer) return;

        const level2Data = this.analysisData.level2;
        const level4Data = this.analysisData.level4;

        let recommendations = [];

        if (level2Data?.coverage_decision === 'COVERED') {
            recommendations.push('✅ Claim is covered under the policy');
        }

        if (level4Data?.settlement_recommendation) {
            recommendations.push(`💰 Recommended settlement: ${level4Data.settlement_recommendation}`);
        }

        const overallConfidence = this.calculateOverallConfidence();
        if (overallConfidence >= 85) {
            recommendations.push('🎯 High confidence analysis - suitable for automated processing');
        } else if (overallConfidence >= 70) {
            recommendations.push('⚠️ Medium confidence - consider manual review');
        } else {
            recommendations.push('🔍 Low confidence - manual review required');
        }

        recommendationsContainer.innerHTML = recommendations.map(rec => `<p>${rec}</p>`).join('');
    }

    updateConfidenceBar(elementId, percentage) {
        const fillElement = document.getElementById(`${elementId}-fill`);
        const valueElement = document.getElementById(`${elementId}-value`);

        if (fillElement) {
            fillElement.style.width = `${percentage}%`;
        }

        if (valueElement) {
            valueElement.textContent = `${percentage}%`;
        }
    }

    calculateOverallConfidence() {
        if (!this.analysisData) return 0;

        const confidences = [
            this.analysisData.level1?.confidence || 0,
            this.analysisData.level2?.confidence || 0,
            this.analysisData.level3?.confidence || 0,
            this.analysisData.level4?.confidence || 0
        ];

        return Math.round(confidences.reduce((a, b) => a + b, 0) / confidences.length * 100);
    }

    formatFieldLabel(key) {
        return key
            .replace(/_/g, ' ')
            .replace(/\b\w/g, l => l.toUpperCase());
    }

    generateDocumentHighlights() {
        // Build highlight mapping for bidirectional interaction
        const highlights = document.querySelectorAll('.highlight');
        this.highlights.clear();

        highlights.forEach((highlight, index) => {
            const field = highlight.getAttribute('data-field');
            const confidence = highlight.getAttribute('data-confidence');
            const value = highlight.getAttribute('data-value');
            const highlightId = highlight.getAttribute('data-highlight-id');

            if (field) {
                this.highlights.set(field, {
                    element: highlight,
                    confidence: confidence ? parseFloat(confidence) : 0.9,
                    value: value || highlight.textContent,
                    highlightId: highlightId,
                    explanation: this.getFieldExplanation(field, value),
                    analysisLevel: this.getFieldAnalysisLevel(field)
                });

                // Add hover effects
                highlight.addEventListener('mouseenter', () => this.showHighlightTooltip(highlight, field));
                highlight.addEventListener('mouseleave', () => this.hideHighlightTooltip());
            }
        });
    }

    handleHighlightClick(element) {
        // Remove active class from all highlights and analysis items
        document.querySelectorAll('.highlight.active').forEach(el => {
            el.classList.remove('active');
        });
        document.querySelectorAll('.analysis-item.highlighted').forEach(el => {
            el.classList.remove('highlighted');
        });

        // Add active class to clicked highlight
        element.classList.add('active');

        const field = element.getAttribute('data-field');
        const value = element.getAttribute('data-value') || element.textContent;
        const confidence = element.getAttribute('data-confidence');
        const highlightData = this.highlights.get(field);

        if (highlightData) {
            // Highlight corresponding field in analysis panel
            this.highlightAnalysisField(field, value);

            // Show detailed information
            this.showHighlightDetails(field, value, confidence, highlightData);

            // Scroll to relevant analysis level
            const analysisLevel = highlightData.analysisLevel;
            if (analysisLevel) {
                this.scrollToAnalysisLevel(analysisLevel);
            }

            console.log(`Highlight clicked: ${field} = ${value}`, highlightData);
        }
    }

    highlightAnalysisField(field, value) {
        // Find and highlight the corresponding field in the analysis panel
        const analysisItems = document.querySelectorAll('.analysis-item, .field-item, .extracted-field');

        analysisItems.forEach(item => {
            const itemField = item.getAttribute('data-field');
            const itemText = item.textContent.toLowerCase();
            const fieldText = value.toLowerCase();

            // Match by field name or value
            if (itemField === field || itemText.includes(fieldText)) {
                item.classList.add('highlighted');

                // Add visual indicator
                if (!item.querySelector('.highlight-indicator')) {
                    const indicator = document.createElement('span');
                    indicator.className = 'highlight-indicator';
                    indicator.innerHTML = '📍';
                    indicator.title = 'Highlighted in document';
                    item.appendChild(indicator);
                }

                // Scroll into view
                item.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            }
        });
    }

    showHighlightDetails(field, value, confidence, highlightData) {
        // Create or update highlight details panel
        let detailsPanel = document.getElementById('highlight-details');

        if (!detailsPanel) {
            detailsPanel = document.createElement('div');
            detailsPanel.id = 'highlight-details';
            detailsPanel.className = 'highlight-details-panel';
            document.body.appendChild(detailsPanel);
        }

        const confidencePercent = confidence ? Math.round(parseFloat(confidence) * 100) : 90;
        const analysisLevel = highlightData.analysisLevel || 'Level 1';

        detailsPanel.innerHTML = `
            <div class="highlight-details-content">
                <div class="highlight-details-header">
                    <h4>Field Details</h4>
                    <button class="close-btn" onclick="this.parentElement.parentElement.parentElement.style.display='none'">×</button>
                </div>
                <div class="highlight-details-body">
                    <p><strong>Field:</strong> ${this.formatFieldLabel(field)}</p>
                    <p><strong>Value:</strong> ${value}</p>
                    <p><strong>Confidence:</strong>
                        <span class="confidence-badge confidence-${this.getConfidenceLevel(confidencePercent)}">
                            ${confidencePercent}%
                        </span>
                    </p>
                    <p><strong>Analysis Level:</strong> ${analysisLevel}</p>
                    <p><strong>Explanation:</strong> ${highlightData.explanation}</p>
                </div>
                <div class="highlight-details-actions">
                    <button onclick="window.claimsApp.scrollToAnalysisLevel('${highlightData.analysisLevel}')">
                        View in Analysis
                    </button>
                    <button onclick="window.claimsApp.exportFieldData('${field}', '${value}')">
                        Export Field
                    </button>
                </div>
            </div>
        `;

        detailsPanel.style.display = 'block';

        // Position near the clicked highlight
        const rect = highlightData.element.getBoundingClientRect();
        detailsPanel.style.top = `${rect.bottom + 10}px`;
        detailsPanel.style.left = `${Math.min(rect.left, window.innerWidth - 300)}px`;
    }

    getFieldExplanation(field, value) {
        const explanations = {
            'claimant_name': 'The name of the person making the claim',
            'incident_date': 'The date when the incident occurred',
            'incident_location': 'The location where the incident took place',
            'policy_number': 'The insurance policy number',
            'claim_type': 'The type of claim being made',
            'estimated_amount': 'The estimated cost or value of the claim',
            'vehicle_registration': 'The registration number of the vehicle involved',
            'police_report_number': 'The police report reference number'
        };

        return explanations[field] || `AI extracted field: ${this.formatFieldLabel(field)}`;
    }

    getFieldAnalysisLevel(field) {
        // Map fields to their primary analysis level
        const levelMapping = {
            'claimant_name': 'Level 1',
            'incident_date': 'Level 1',
            'incident_location': 'Level 1',
            'policy_number': 'Level 1',
            'claim_type': 'Level 1',
            'estimated_amount': 'Level 4',
            'coverage_decision': 'Level 2',
            'fault_allocation': 'Level 3',
            'settlement_recommendation': 'Level 4'
        };

        return levelMapping[field] || 'Level 1';
    }

    getConfidenceLevel(percentage) {
        if (percentage >= 85) return 'high';
        if (percentage >= 70) return 'medium';
        return 'low';
    }

    scrollToAnalysisLevel(level) {
        const levelElement = document.querySelector(`[data-level="${level}"], .level-${level.toLowerCase().replace(' ', '-')}`);
        if (levelElement) {
            levelElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
            levelElement.classList.add('highlighted-level');
            setTimeout(() => levelElement.classList.remove('highlighted-level'), 3000);
        }
    }

    showHighlightTooltip(element, field) {
        const tooltip = document.createElement('div');
        tooltip.className = 'highlight-tooltip';
        tooltip.innerHTML = `
            <strong>${this.formatFieldLabel(field)}</strong><br>
            Click to view in analysis panel
        `;

        document.body.appendChild(tooltip);

        const rect = element.getBoundingClientRect();
        tooltip.style.top = `${rect.top - tooltip.offsetHeight - 5}px`;
        tooltip.style.left = `${rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2)}px`;

        this.currentTooltip = tooltip;
    }

    hideHighlightTooltip() {
        if (this.currentTooltip) {
            this.currentTooltip.remove();
            this.currentTooltip = null;
        }
    }

    exportFieldData(field, value) {
        const fieldData = {
            field: field,
            value: value,
            confidence: this.highlights.get(field)?.confidence || 0,
            explanation: this.highlights.get(field)?.explanation || '',
            claim_reference: this.currentClaim?.claim_reference || '',
            timestamp: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(fieldData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `field-${field}-${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    async handleApprove() {
        if (!this.currentClaim) return;

        const settlement = this.analysisData?.level4?.settlement_recommendation || 'N/A';
        const confirmed = confirm(`Are you sure you want to approve this settlement of ${settlement}?`);
        if (!confirmed) return;

        try {
            this.showLoading(true);

            // Update claim status in database
            await this.db.updateClaimStatus(
                this.currentClaim.claim_reference,
                'APPROVED',
                `Settlement approved: ${settlement}`
            );

            // Update current claim object
            this.currentClaim.workflow_status = 'APPROVED';

            // Update UI
            const statusElement = document.getElementById('claim-status');
            if (statusElement) {
                const statusConfig = this.config.STATUS_MAPPINGS.workflow_status['APPROVED'];
                statusElement.textContent = statusConfig.label;
                statusElement.className = `status ${statusConfig.class}`;
            }

            alert('Claim approved successfully!');

        } catch (error) {
            console.error('Error approving claim:', error);
            alert('Error approving claim. Please try again.');
        } finally {
            this.showLoading(false);
        }
    }

    async handleEscalate() {
        if (!this.currentClaim) return;

        const reason = prompt('Please provide a reason for escalation:');
        if (!reason) return;

        try {
            this.showLoading(true);

            // Update claim status in database
            await this.db.updateClaimStatus(
                this.currentClaim.claim_reference,
                'ESCALATED',
                `Escalated for manual review: ${reason}`
            );

            // Update current claim object
            this.currentClaim.workflow_status = 'ESCALATED';

            // Update UI
            const statusElement = document.getElementById('claim-status');
            if (statusElement) {
                const statusConfig = this.config.STATUS_MAPPINGS.workflow_status['ESCALATED'];
                statusElement.textContent = statusConfig.label;
                statusElement.className = `status ${statusConfig.class}`;
            }

            alert('Claim escalated for manual review.');

        } catch (error) {
            console.error('Error escalating claim:', error);
            alert('Error escalating claim. Please try again.');
        } finally {
            this.showLoading(false);
        }
    }

    handleExport() {
        if (!this.currentClaim || !this.analysisData) return;

        // Generate export data
        const exportData = {
            claim_reference: this.currentClaim.claim_reference,
            timestamp: new Date().toISOString(),
            analysis_summary: {
                overall_confidence: this.calculateOverallConfidence(),
                level1_confidence: Math.round((this.analysisData.level1?.confidence || 0) * 100),
                level2_confidence: Math.round((this.analysisData.level2?.confidence || 0) * 100),
                level3_confidence: Math.round((this.analysisData.level3?.confidence || 0) * 100),
                level4_confidence: Math.round((this.analysisData.level4?.confidence || 0) * 100)
            },
            extracted_fields: this.analysisData.level1?.extracted_fields || {},
            coverage_decision: this.analysisData.level2?.coverage_decision || 'N/A',
            fault_allocation: this.analysisData.level3?.fault_allocation || {},
            settlement_recommendation: this.analysisData.level4?.settlement_recommendation || 'N/A'
        };

        // Create and download JSON file
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `claim-analysis-${this.currentClaim.claim_reference}-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    showLoading(show) {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            if (show) {
                loadingOverlay.classList.remove('hidden');
            } else {
                loadingOverlay.classList.add('hidden');
            }
        }
    }

    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.claimsApp = new ClaimsReviewApp();
});
