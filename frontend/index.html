<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Claims Document Review | AI-Powered Analysis</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container flex justify-between items-center">
            <div class="flex items-center gap-16">
                <h1 class="header__title">Claims Review</h1>
                <div class="claim-info">
                    <span class="claim-reference" id="claim-reference">CLM-85228383</span>
                    <span class="status status--info" id="claim-status">Under Review</span>
                </div>
            </div>
            <div class="flex items-center gap-8">
                <button class="btn btn--outline btn--sm" id="theme-toggle">
                    <span class="theme-icon">🌙</span>
                </button>
                <div class="confidence-indicator">
                    <span class="confidence-label">Overall Confidence</span>
                    <div class="confidence-bar">
                        <div class="confidence-fill" id="overall-confidence-fill" style="width: 85%"></div>
                        <span class="confidence-value" id="overall-confidence-value">85%</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <div class="review-layout">
                <!-- Document Viewer Section -->
                <section class="document-viewer">
                    <div class="viewer-controls">
                        <button class="btn btn--outline btn--sm" id="zoom-out">-</button>
                        <span class="zoom-level">100%</span>
                        <button class="btn btn--outline btn--sm" id="zoom-in">+</button>
                        <div class="page-nav">
                            <span id="page-info">Page 1 of 1</span>
                        </div>
                    </div>
                    
                    <div class="document-content" id="document-content">
                        <div class="document-page" id="document-page">
                            <div class="document-header">
                                <h2 id="document-title">Loading Document...</h2>
                                <p><strong>Claimant:</strong> <span class="highlight" data-field="claimant_name" id="claimant-name">Loading...</span></p>
                                <p><strong>Claim Reference:</strong> <span id="document-claim-ref">Loading...</span></p>
                            </div>
                            
                            <div class="document-body" id="document-body">
                                <p>Loading document content...</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- AI Analysis Panel -->
                <section class="analysis-panel">
                    <div class="panel-header">
                        <h2>AI Analysis Results</h2>
                        <span class="analysis-timestamp" id="analysis-timestamp">Processing...</span>
                    </div>

                    <div class="analysis-levels" id="analysis-levels">
                        <!-- Level 1: Extraction -->
                        <div class="analysis-level" data-level="level1">
                            <div class="level-header">
                                <h3>L1: Document Extraction & Classification</h3>
                                <div class="level-confidence">
                                    <div class="confidence-bar">
                                        <div class="confidence-fill" id="level1-confidence-fill" style="width: 0%"></div>
                                        <span class="confidence-value" id="level1-confidence-value">0%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="level-content">
                                <div class="extracted-fields" id="level1-fields">
                                    <!-- Fields will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>

                        <!-- Level 2: Coverage -->
                        <div class="analysis-level" data-level="level2">
                            <div class="level-header">
                                <h3>L2: Coverage Determination</h3>
                                <div class="level-confidence">
                                    <div class="confidence-bar">
                                        <div class="confidence-fill" id="level2-confidence-fill" style="width: 0%"></div>
                                        <span class="confidence-value" id="level2-confidence-value">0%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="level-content">
                                <div class="coverage-decision" id="level2-content">
                                    <!-- Coverage content will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>

                        <!-- Level 3: Fault -->
                        <div class="analysis-level" data-level="level3">
                            <div class="level-header">
                                <h3>L3: Fault Allocation</h3>
                                <div class="level-confidence">
                                    <div class="confidence-bar">
                                        <div class="confidence-fill" id="level3-confidence-fill" style="width: 0%"></div>
                                        <span class="confidence-value" id="level3-confidence-value">0%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="level-content">
                                <div class="fault-allocation" id="level3-content">
                                    <!-- Fault content will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>

                        <!-- Level 4: Quantum -->
                        <div class="analysis-level" data-level="level4">
                            <div class="level-header">
                                <h3>L4: Loss Quantum Assessment</h3>
                                <div class="level-confidence">
                                    <div class="confidence-bar">
                                        <div class="confidence-fill" id="level4-confidence-fill" style="width: 0%"></div>
                                        <span class="confidence-value" id="level4-confidence-value">0%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="level-content">
                                <div class="quantum-breakdown" id="level4-content">
                                    <!-- Quantum content will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recommendations -->
                    <div class="recommendations" id="recommendations">
                        <h3>AI Recommendations</h3>
                        <div id="recommendations-content">
                            <!-- Recommendations will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <button class="btn btn--primary" id="approve-btn">Approve Settlement</button>
                        <button class="btn btn--outline" id="escalate-btn">Escalate for Review</button>
                        <button class="btn btn--secondary" id="export-btn">Export Report</button>
                    </div>
                </section>
            </div>
        </div>
    </main>

    <!-- Mobile Toggle Button -->
    <button class="mobile-toggle" id="mobile-toggle">
        <span class="toggle-text">Show Analysis</span>
    </button>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner"></div>
        <p>Loading claim data...</p>
    </div>

    <!-- Configuration and Dependencies -->
    <script src="env-config.js"></script>
    <script src="config.js"></script>
    <script src="database.js"></script>
    <script src="app.js"></script>
</body>
</html>
