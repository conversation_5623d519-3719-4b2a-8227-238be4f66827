/**
 * Configuration file for Claims Review Frontend
 * Integrates with existing Supabase database and API endpoints
 */

// Supabase Configuration
// These should match the configuration from the existing explainable claims dashboard
const SUPABASE_CONFIG = {
    url: window.SUPABASE_URL || 'https://tlduggpohclrgxbvuzhd.supabase.co',
    anonKey: window.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZHVnZ3BvaGNscmd4YnZ1emhkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3MDEwMzgsImV4cCI6MjA2NjI3NzAzOH0.Eud_SDfGulErh3yhqjaIMqM37eghuz-PVeRxknzxRfk',
    serviceRoleKey: window.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZHVnZ3BvaGNscmd4YnZ1emhkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDcwMTAzOCwiZXhwIjoyMDY2Mjc3MDM4fQ.wDi5bG1O81a3HaXOujWshJZoGV6AhFgEuaoVcjam0SE'
};

// API Endpoints Configuration
const API_CONFIG = {
    baseUrl: window.BACKEND_URL || 'http://localhost:8000',
    endpoints: {
        explainability: '/api/ai-explainability'
    }
};

// Database Schema Mapping
// Maps the UI fields to the actual database columns
const DATABASE_SCHEMA = {
    claims: {
        table: 'claims',
        columns: {
            id: 'id',
            claimReference: 'claim_reference',
            emailSubject: 'email_subject',
            emailBody: 'email_body',
            level01Analysis: '01_level_analysis',
            level02Analysis: '02_level_analysis', 
            level03Analysis: '03_level_analysis',
            level04Analysis: '04_level_analysis',
            workflowStatus: 'workflow_status',
            priorityScore: 'priority_score',
            estimatedValue: 'estimated_value',
            incidentDate: 'incident_date',
            assignedAgent: 'assigned_agent',
            createdAt: 'created_at',
            updatedAt: 'updated_at'
        }
    },
    attachments: {
        table: 'attachments',
        columns: {
            id: 'id',
            claimReference: 'claim_reference',
            fileName: 'file_name',
            contentType: 'content_type',
            fileSize: 'file_size',
            ocrText: 'ocr_text',
            storagePath: 'storage_path',
            uploadDate: 'upload_date',
            processingStatus: 'processing_status',
            processedAt: 'processed_at',
            processingMetadata: 'processing_metadata'
        }
    }
};

// Analysis Level Configurations
const ANALYSIS_LEVELS = {
    level1: {
        name: 'Document Extraction & Classification',
        description: 'AI extracts key information from documents and classifies claim type',
        confidence_threshold: 0.8,
        fields: [
            'claimant_name',
            'incident_date', 
            'incident_location',
            'claim_type',
            'estimated_amount',
            'policy_number',
            'police_report_number'
        ]
    },
    level2: {
        name: 'Coverage Determination',
        description: 'AI analyzes policy coverage and determines if claim is covered',
        confidence_threshold: 0.75,
        decisions: ['COVERED', 'NOT_COVERED', 'PARTIAL_COVERAGE', 'REQUIRES_REVIEW']
    },
    level3: {
        name: 'Fault Allocation',
        description: 'AI determines fault percentages between parties',
        confidence_threshold: 0.7,
        fault_types: ['NO_FAULT', 'SINGLE_FAULT', 'SHARED_FAULT', 'DISPUTED']
    },
    level4: {
        name: 'Loss Quantum Assessment', 
        description: 'AI calculates settlement amounts and quantum breakdown',
        confidence_threshold: 0.8,
        components: [
            'vehicle_damage',
            'medical_expenses',
            'rental_car',
            'loss_of_use',
            'pain_and_suffering'
        ]
    }
};

// UI Configuration
const UI_CONFIG = {
    theme: {
        default: 'light',
        options: ['light', 'dark', 'auto']
    },
    zoom: {
        min: 50,
        max: 200,
        step: 10,
        default: 100
    },
    mobile: {
        breakpoint: 768
    },
    confidence: {
        high: 85,
        medium: 70,
        low: 50
    },
    polling: {
        interval: 30000, // 30 seconds
        maxRetries: 5
    }
};

// Status Mappings
const STATUS_MAPPINGS = {
    workflow_status: {
        'NEW': { label: 'New', class: 'status--info' },
        'IN_PROGRESS': { label: 'In Progress', class: 'status--warning' },
        'UNDER_REVIEW': { label: 'Under Review', class: 'status--info' },
        'APPROVED': { label: 'Approved', class: 'status--success' },
        'REJECTED': { label: 'Rejected', class: 'status--error' },
        'ESCALATED': { label: 'Escalated', class: 'status--warning' },
        'COMPLETED': { label: 'Completed', class: 'status--success' }
    },
    processing_status: {
        'pending': { label: 'Pending', class: 'status--info' },
        'processing': { label: 'Processing', class: 'status--warning' },
        'completed': { label: 'Completed', class: 'status--success' },
        'failed': { label: 'Failed', class: 'status--error' }
    }
};

// Export configuration
if (typeof module !== 'undefined' && module.exports) {
    // Node.js environment
    module.exports = {
        SUPABASE_CONFIG,
        API_CONFIG,
        DATABASE_SCHEMA,
        ANALYSIS_LEVELS,
        UI_CONFIG,
        STATUS_MAPPINGS
    };
} else {
    // Browser environment
    window.ClaimsReviewConfig = {
        SUPABASE_CONFIG,
        API_CONFIG,
        DATABASE_SCHEMA,
        ANALYSIS_LEVELS,
        UI_CONFIG,
        STATUS_MAPPINGS
    };
}
