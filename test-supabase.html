<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { background: #fee2e2; color: #dc2626; }
        .success { background: #d1fae5; color: #059669; }
    </style>
</head>
<body>
    <h1>🔍 Supabase Loading Test</h1>
    <div id="logs"></div>
    
    <script>
        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            logs.appendChild(div);
            console.log(message);
        }
        
        // Test 1: Check if supabase is available immediately
        log('Testing immediate supabase availability...');
        log(`typeof supabase: ${typeof supabase}`);
        log(`supabase object: ${supabase}`);
        
        if (typeof supabase !== 'undefined') {
            log('✅ Supabase is available immediately!', 'success');
        } else {
            log('❌ Supabase not available immediately', 'error');
        }
        
        // Test 2: Wait for supabase to load
        let attempts = 0;
        const maxAttempts = 50;
        
        function checkSupabase() {
            attempts++;
            log(`Attempt ${attempts}/${maxAttempts}: Checking supabase...`);
            
            if (typeof supabase !== 'undefined' && supabase.createClient) {
                log('✅ Supabase loaded successfully!', 'success');
                log(`supabase.createClient type: ${typeof supabase.createClient}`);
                
                // Test creating a client
                try {
                    const client = supabase.createClient(
                        'https://tlduggpohclrgxbvuzhd.supabase.co',
                        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZHVnZ3BvaGNscmd4YnZ1emhkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDcwMTAzOCwiZXhwIjoyMDY2Mjc3MDM4fQ.wDi5bG1O81a3HaXOujWshJZoGV6AhFgEuaoVcjam0SE'
                    );
                    log('✅ Supabase client created successfully!', 'success');
                    log(`Client methods: ${Object.keys(client).join(', ')}`);
                    
                    // Test a simple query
                    client.from('claims').select('count').limit(1).then(result => {
                        log('✅ Database query successful!', 'success');
                        log(`Query result: ${JSON.stringify(result)}`);
                    }).catch(error => {
                        log(`❌ Database query failed: ${error.message}`, 'error');
                    });
                    
                } catch (error) {
                    log(`❌ Failed to create Supabase client: ${error.message}`, 'error');
                }
                
            } else if (attempts >= maxAttempts) {
                log('❌ Supabase failed to load after maximum attempts', 'error');
            } else {
                setTimeout(checkSupabase, 100);
            }
        }
        
        // Start checking after a short delay
        setTimeout(checkSupabase, 100);
    </script>
    
    <!-- Try different Supabase CDN sources -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script>
        log('Primary CDN script loaded');
    </script>
    
    <!-- Fallback CDN -->
    <script>
        if (typeof supabase === 'undefined') {
            log('Primary CDN failed, trying fallback...');
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.min.js';
            script.onload = () => log('Fallback CDN loaded');
            script.onerror = () => log('Fallback CDN failed', 'error');
            document.head.appendChild(script);
        }
    </script>
</body>
</html> 