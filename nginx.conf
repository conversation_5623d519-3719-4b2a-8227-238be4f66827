events {
    worker_connections 1024;
}

http {
    upstream zurich_backend {
        server zurich-backend:8000;
    }

    # Additional upstreams for future services
    # upstream zurich_workflow {
    #     server zurich-backend-workflow:8001;
    # }
    
    # upstream zurich_documents {
    #     server zurich-backend-documents:8002;
    # }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;

    server {
        listen 80;
        server_name localhost;

        # Redirect HTTP to HTTPS in production
        # return 301 https://$server_name$request_uri;

        # Main API routes
        location /api/ {
            proxy_pass http://zurich_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Rate limiting
            limit_req zone=api burst=20 nodelay;
            
            # Timeouts
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        # Route specific API paths to different services (when adding more APIs)
        # location /api/workflow/ {
        #     proxy_pass http://zurich_workflow;
        #     proxy_set_header Host $host;
        #     proxy_set_header X-Real-IP $remote_addr;
        #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #     proxy_set_header X-Forwarded-Proto $scheme;
        #     limit_req zone=api burst=20 nodelay;
        # }

        # location /api/documents/ {
        #     proxy_pass http://zurich_documents;
        #     proxy_set_header Host $host;
        #     proxy_set_header X-Real-IP $remote_addr;
        #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #     proxy_set_header X-Forwarded-Proto $scheme;
        #     limit_req zone=api burst=20 nodelay;
        # }

        # Health check endpoint (no rate limiting)
        location /api/health {
            proxy_pass http://zurich_backend/api/health;
            access_log off;
        }

        # API docs
        location /api/docs {
            proxy_pass http://zurich_backend/api/docs;
        }

        # Default route
        location / {
            proxy_pass http://zurich_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # SSL/HTTPS configuration (uncomment for production)
    # server {
    #     listen 443 ssl http2;
    #     server_name your-domain.com;
    #     
    #     ssl_certificate /etc/nginx/ssl/cert.pem;
    #     ssl_certificate_key /etc/nginx/ssl/key.pem;
    #     
    #     ssl_protocols TLSv1.2 TLSv1.3;
    #     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    #     ssl_prefer_server_ciphers off;
    #     
    #     location / {
    #         proxy_pass http://zurich_backend;
    #         proxy_set_header Host $host;
    #         proxy_set_header X-Real-IP $remote_addr;
    #         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #         proxy_set_header X-Forwarded-Proto $scheme;
    #         
    #         limit_req zone=api burst=20 nodelay;
    #     }
    # }
} 