{"name": "Level 01 Analysis Workflow", "nodes": [{"parameters": {}, "name": "When clicking \"Test workflow\"", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [200, 300], "id": "manual-trigger"}, {"parameters": {"method": "POST", "url": "http://localhost:8000/api/level01-analysis/simple", "authentication": "none", "requestFormat": "json", "bodyParameters": {"parameters": [{"name": "claimId", "value": "={{ $json.claimId || 'AUTO001' }}"}, {"name": "emailSubject", "value": "={{ $json.emailSubject || 'Car Accident - Policy POL789456' }}"}, {"name": "emailContent", "value": "={{ $json.emailContent || 'Sample auto claim with policy number POL789456. Accident occurred yesterday at Main St and Oak Ave. Rear-end collision with significant damage. Police report #2024-001234.' }}"}, {"name": "attachmentNames", "value": "={{ $json.attachmentNames || ['police_report.pdf', 'damage_photos.jpg'] }}"}, {"name": "attachmentsText", "value": "={{ $json.attachmentsText || ['Police report details', 'Photo descriptions'] }}"}, {"name": "preprocessingNotes", "value": "={{ $json.preprocessingNotes || [] }}"}]}, "options": {"timeout": 60000, "retry": {"enabled": true, "times": 2, "wait": 5000}}}, "name": "Level 01 Analysis API Call", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [400, 300], "id": "level01-api-call"}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.exitPath }}", "operation": "equal", "value2": "DOCUMENTS_INSUFFICIENT"}]}}, "name": "Route by Exit Path", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [600, 300], "id": "exit-path-router"}, {"parameters": {"method": "POST", "url": "http://localhost:8000/api/send-document-request", "authentication": "none", "requestFormat": "json", "bodyParameters": {"parameters": [{"name": "claimId", "value": "={{ $json.claimId }}"}, {"name": "documentsNeeded", "value": "={{ $json.documentsNeeded }}"}, {"name": "estimatedTimeToResolution", "value": "={{ $json.estimatedTimeToResolution }}"}, {"name": "priority", "value": "={{ $json.priorityLevel }}"}]}}, "name": "Request Additional Documents", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [800, 200], "id": "request-documents"}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.exitPath }}", "operation": "equal", "value2": "POLICY_LOOKUP_NEEDED"}]}}, "name": "Policy Lookup Check", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [800, 300], "id": "policy-lookup-check"}, {"parameters": {"method": "GET", "url": "http://localhost:8000/api/policy-lookup/{{ $json.policyNumber }}", "authentication": "none", "options": {"timeout": 30000}}, "name": "Lookup Policy Details", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1000, 300], "id": "policy-lookup"}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.exitPath }}", "operation": "equal", "value2": "PROCEED_TO_LEVEL02"}]}}, "name": "Level 02 Ready Check", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [800, 400], "id": "level02-ready-check"}, {"parameters": {"method": "POST", "url": "http://localhost:8000/api/level02-coverage-analysis", "authentication": "none", "requestFormat": "json", "bodyParameters": {"parameters": [{"name": "claimId", "value": "={{ $json.claimId }}"}, {"name": "level01Analysis", "value": "={{ $json }}"}, {"name": "priority", "value": "={{ $json.priorityLevel }}"}]}}, "name": "Proceed to Level 02 Analysis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1000, 400], "id": "level02-analysis"}, {"parameters": {"message": "Document request sent for claim {{ $json.claimId }}. Required documents: {{ $json.documentsNeeded.join(', ') }}. Expected resolution: {{ $json.estimatedTimeToResolution }}", "options": {"includeCredentials": false}}, "name": "Log Document Request", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1200, 200], "id": "log-document-request"}, {"parameters": {"message": "Policy lookup completed for claim {{ $json.claimId }}. Policy number: {{ $json.policyNumber }}. Ready for Level 02 analysis.", "options": {"includeCredentials": false}}, "name": "Log Policy Lookup", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1200, 300], "id": "log-policy-lookup"}, {"parameters": {"message": "Claim {{ $json.claimId }} proceeded to Level 02 analysis. Confidence: {{ $json.confidenceScore }}. Priority: {{ $json.priorityLevel }}", "options": {"includeCredentials": false}}, "name": "Log Level 02 Progression", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1200, 400], "id": "log-level02-progression"}, {"parameters": {"method": "POST", "url": "http://localhost:8000/api/notifications/human-review", "authentication": "none", "requestFormat": "json", "bodyParameters": {"parameters": [{"name": "claimId", "value": "={{ $json.claimId }}"}, {"name": "reason", "value": "Human review required based on Level 01 analysis"}, {"name": "humanReviewItems", "value": "={{ $json.humanReviewItems || [] }}"}, {"name": "priority", "value": "={{ $json.priorityLevel }}"}]}, "options": {"conditions": {"boolean": [{"value1": "={{ $json.humanReviewRequired }}", "operation": "equal", "value2": true}]}}}, "name": "Flag for Human Review", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [600, 500], "id": "human-review-flag"}, {"parameters": {"errorMessage": "Level 01 Analysis failed for claim {{ $json.claimId }}. Error: {{ $json.error }}. Manual intervention required.", "options": {"includeCredentials": false}}, "name": "Handle Analysis Error", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [400, 500], "id": "handle-error"}], "connections": {"When clicking \"Test workflow\"": {"main": [[{"node": "Level 01 Analysis API Call", "type": "main", "index": 0}]]}, "Level 01 Analysis API Call": {"main": [[{"node": "Route by Exit Path", "type": "main", "index": 0}, {"node": "Flag for Human Review", "type": "main", "index": 0}]], "error": [[{"node": "Handle Analysis Error", "type": "main", "index": 0}]]}, "Route by Exit Path": {"main": [[{"node": "Request Additional Documents", "type": "main", "index": 0}], [{"node": "Policy Lookup Check", "type": "main", "index": 0}], [{"node": "Level 02 Ready Check", "type": "main", "index": 0}]]}, "Request Additional Documents": {"main": [[{"node": "Log Document Request", "type": "main", "index": 0}]]}, "Policy Lookup Check": {"main": [[{"node": "Lookup Policy Details", "type": "main", "index": 0}]]}, "Lookup Policy Details": {"main": [[{"node": "Log Policy Lookup", "type": "main", "index": 0}]]}, "Level 02 Ready Check": {"main": [[{"node": "Proceed to Level 02 Analysis", "type": "main", "index": 0}]]}, "Proceed to Level 02 Analysis": {"main": [[{"node": "Log Level 02 Progression", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-07-02T03:30:00.000Z", "updatedAt": "2024-07-02T03:30:00.000Z", "id": "level01-analysis", "name": "Level 01 Analysis"}], "triggerCount": 0, "updatedAt": "2024-07-02T03:30:00.000Z", "versionId": "1"}