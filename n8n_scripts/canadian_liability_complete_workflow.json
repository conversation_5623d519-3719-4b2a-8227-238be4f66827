{"name": "Canadian Liability Claims - Complete Workflow", "nodes": [{"parameters": {}, "name": "Start - <PERSON><PERSON>", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [200, 300], "id": "start-trigger"}, {"parameters": {"method": "POST", "url": "http://localhost:8000/api/classify-email", "authentication": "none", "requestFormat": "json", "bodyParameters": {"parameters": [{"name": "subject", "value": "={{ $json.emailSubject }}"}, {"name": "content", "value": "={{ $json.emailContent }}"}, {"name": "sender", "value": "={{ $json.sender }}"}, {"name": "attachments", "value": "={{ $json.attachments }}"}]}}, "name": "Email Classification", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [400, 300], "id": "email-classification"}, {"parameters": {"method": "POST", "url": "http://localhost:8000/api/level01-analysis/simple", "authentication": "none", "requestFormat": "json", "bodyParameters": {"parameters": [{"name": "claimId", "value": "={{ $json.uniqueId }}"}, {"name": "emailSubject", "value": "={{ $json.subject }}"}, {"name": "emailContent", "value": "={{ $json.emailBody }}"}, {"name": "claimType", "value": "={{ $json.classification }}"}, {"name": "attachmentNames", "value": "={{ $json.attachmentNames }}"}]}}, "name": "Level 1 - Initial Analysis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [600, 300], "id": "level01-analysis"}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.exitPath }}", "operation": "equals", "value2": "PROCEED_TO_LEVEL02"}]}}, "name": "Check L1 Exit Path", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [800, 300], "id": "check-l1-exit"}, {"parameters": {"method": "POST", "url": "http://localhost:8000/api/level02-coverage/analyze", "authentication": "none", "requestFormat": "json", "bodyParameters": {"parameters": [{"name": "claim_id", "value": "={{ $json.claimId }}"}, {"name": "policy_number", "value": "={{ $json.policyNumber }}"}]}}, "name": "Level 2 - Coverage Analysis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1000, 300], "id": "level02-coverage"}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.coverage_status }}", "operation": "equals", "value2": "COVERED"}]}}, "name": "Check Coverage", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1200, 300], "id": "check-coverage"}, {"parameters": {"method": "POST", "url": "http://localhost:8000/api/level03-fault/analyze", "authentication": "none", "requestFormat": "json", "bodyParameters": {"parameters": [{"name": "claim_id", "value": "={{ $json.claim_id }}"}, {"name": "province", "value": "={{ $json.province || 'Ontario' }}"}, {"name": "accident_type", "value": "={{ $json.claim_type }}"}, {"name": "circumstances", "value": "={{ { weather: $json.weatherConditions, witnesses: $json.witnessCount } }}"}]}}, "name": "Level 3 - Fault Determination", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1400, 300], "id": "level03-fault"}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.proceed_to_quantum }}", "value2": true}]}}, "name": "Proceed to Quantum?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1600, 300], "id": "check-quantum"}, {"parameters": {"method": "POST", "url": "http://localhost:8000/api/level04-quantum/calculate", "authentication": "none", "requestFormat": "json", "bodyParameters": {"parameters": [{"name": "claim_id", "value": "={{ $json.claim_id }}"}, {"name": "province", "value": "={{ $json.province }}"}, {"name": "injuries", "value": "={{ $json.injuries || [] }}"}, {"name": "treatment_plan", "value": "={{ $json.treatment_plan || {} }}"}, {"name": "income_info", "value": "={{ $json.income_info || {} }}"}, {"name": "claimant_fault_percentage", "value": "={{ $json.fault_parties.find(p => p.party_type === 'claimant')?.fault_percentage || 0 }}"}]}}, "name": "Level 4 - Quantum Calculation", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1800, 300], "id": "level04-quantum"}, {"parameters": {"functionCode": "// Compile final settlement recommendation\nconst claimId = items[0].json.claim_id;\nconst quantum = items[0].json.quantum_breakdown;\nconst settlement = items[0].json.recommended_settlement;\nconst confidence = items[0].json.calculation_confidence;\n\n// Determine approval level\nlet approvalLevel = 'Adjuster';\nif (settlement > 200000) {\n  approvalLevel = 'Executive';\n} else if (settlement > 50000) {\n  approvalLevel = 'Manager';\n}\n\n// Format currency\nconst formatCurrency = (amount) => {\n  return new Intl.NumberFormat('en-CA', {\n    style: 'currency',\n    currency: 'CAD'\n  }).format(amount);\n};\n\nreturn [{\n  json: {\n    claimId: claimId,\n    finalRecommendation: {\n      settlementAmount: formatCurrency(settlement),\n      approvalRequired: approvalLevel,\n      confidence: `${(confidence * 100).toFixed(0)}%`,\n      breakdown: {\n        totalDamages: formatCurrency(quantum.total_damages),\n        faultReduction: formatCurrency(quantum.fault_reduction_amount),\n        netRecoverable: formatCurrency(quantum.net_recoverable)\n      },\n      nextSteps: [\n        `1. Send settlement offer of ${formatCurrency(settlement)}`,\n        `2. Obtain ${approvalLevel} approval`,\n        '3. Prepare release documents',\n        '4. Process payment upon acceptance'\n      ]\n    },\n    timestamp: new Date().toISOString()\n  }\n}];"}, "name": "Prepare Final Decision", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [2000, 300], "id": "prepare-decision"}, {"parameters": {"method": "POST", "url": "http://localhost:8000/api/notifications/settlement-recommendation", "authentication": "none", "requestFormat": "json", "bodyParameters": {"parameters": [{"name": "claimId", "value": "={{ $json.claimId }}"}, {"name": "recommendation", "value": "={{ $json.finalRecommendation }}"}]}}, "name": "Send Settlement Notification", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2200, 300], "id": "send-notification"}, {"parameters": {"values": {"string": [{"name": "status", "value": "Coverage Denied"}, {"name": "reason", "value": "={{ $json.exclusion_triggered || 'Policy exclusion applies' }}"}]}}, "name": "Coverage Denied", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1200, 500], "id": "coverage-denied"}, {"parameters": {"values": {"string": [{"name": "status", "value": "Investigation Required"}, {"name": "reason", "value": "={{ $json.investigation_points.join(', ') }}"}]}}, "name": "Investigation Required", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1600, 500], "id": "investigation-required"}, {"parameters": {"functionCode": "// Canadian Province Detection\nconst content = items[0].json.emailContent || '';\nconst subject = items[0].json.emailSubject || '';\nconst combined = (subject + ' ' + content).toLowerCase();\n\nconst provinceKeywords = {\n  'Ontario': ['ontario', 'toronto', 'ottawa', 'mississauga', 'hamilton'],\n  'British Columbia': ['british columbia', 'bc', 'vancouver', 'victoria', 'surrey'],\n  'Alberta': ['alberta', 'calgary', 'edmonton', 'red deer'],\n  'Quebec': ['quebec', 'québec', 'montreal', 'montréal', 'laval'],\n  'Manitoba': ['manitoba', 'winnipeg', 'brandon'],\n  'Saskatchewan': ['saskatchewan', 'regina', 'saskatoon'],\n  'Nova Scotia': ['nova scotia', 'halifax', 'dartmouth'],\n  'New Brunswick': ['new brunswick', 'fredericton', 'moncton'],\n  'Newfoundland and Labrador': ['newfoundland', 'labrador', 'st. john\\'s'],\n  'Prince Edward Island': ['prince edward island', 'pei', 'charlottetown'],\n  'Northwest Territories': ['northwest territories', 'yellowknife'],\n  'Yukon': ['yukon', 'whitehorse'],\n  'Nunavut': ['nunavut', 'iqaluit']\n};\n\nlet detectedProvince = 'Ontario'; // Default\n\nfor (const [province, keywords] of Object.entries(provinceKeywords)) {\n  if (keywords.some(keyword => combined.includes(keyword))) {\n    detectedProvince = province;\n    break;\n  }\n}\n\n// Detect claim type\nlet claimType = 'general_liability';\nif (combined.includes('vehicle') || combined.includes('car') || combined.includes('accident') || combined.includes('collision')) {\n  claimType = 'auto';\n} else if (combined.includes('slip') || combined.includes('fall') || combined.includes('premises')) {\n  claimType = 'slip_fall';\n}\n\nreturn [{\n  json: {\n    ...items[0].json,\n    detectedProvince: detectedProvince,\n    detectedClaimType: claimType,\n    processingNote: `Detected ${detectedProvince} ${claimType} claim`\n  }\n}];"}, "name": "Detect Province & Type", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [300, 300], "id": "detect-province"}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.priority }}", "operation": "equals", "value2": "URGENT"}]}}, "name": "High Priority?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [500, 400], "id": "check-priority"}, {"parameters": {"method": "POST", "url": "http://localhost:8000/api/notifications/urgent-claim", "authentication": "none", "requestFormat": "json", "bodyParameters": {"parameters": [{"name": "claimId", "value": "={{ $json.uniqueId }}"}, {"name": "priority", "value": "URGENT"}, {"name": "reason", "value": "High value claim or severe injury detected"}]}}, "name": "Urgent Notification", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [500, 500], "id": "urgent-notification"}], "connections": {"Start - Email Trigger": {"main": [[{"node": "Detect Province & Type", "type": "main", "index": 0}]]}, "Detect Province & Type": {"main": [[{"node": "Email Classification", "type": "main", "index": 0}]]}, "Email Classification": {"main": [[{"node": "Check High Priority?", "type": "main", "index": 0}, {"node": "Level 1 - Initial Analysis", "type": "main", "index": 0}]]}, "Level 1 - Initial Analysis": {"main": [[{"node": "Check L1 Exit Path", "type": "main", "index": 0}]]}, "Check L1 Exit Path": {"main": [[{"node": "Level 2 - Coverage Analysis", "type": "main", "index": 0}]]}, "Level 2 - Coverage Analysis": {"main": [[{"node": "Check Coverage", "type": "main", "index": 0}]]}, "Check Coverage": {"main": [[{"node": "Level 3 - Fault Determination", "type": "main", "index": 0}], [{"node": "Coverage Denied", "type": "main", "index": 0}]]}, "Level 3 - Fault Determination": {"main": [[{"node": "Proceed to Quantum?", "type": "main", "index": 0}]]}, "Proceed to Quantum?": {"main": [[{"node": "Level 4 - Quantum Calculation", "type": "main", "index": 0}], [{"node": "Investigation Required", "type": "main", "index": 0}]]}, "Level 4 - Quantum Calculation": {"main": [[{"node": "Prepare Final Decision", "type": "main", "index": 0}]]}, "Prepare Final Decision": {"main": [[{"node": "Send Settlement Notification", "type": "main", "index": 0}]]}, "High Priority?": {"main": [[{"node": "Urgent Notification", "type": "main", "index": 0}]]}}, "pinData": {"Start - Email Trigger": [{"json": {"emailSubject": "Rear-end collision on Highway 401 - Urgent", "emailContent": "I was stopped in traffic on Highway 401 near Toronto when another vehicle hit me from behind. The driver admitted fault to the police. I have whiplash and need physiotherapy. My 2022 Honda Civic has $8,500 in damages. Police report #TPS-2024-001234 attached.", "sender": "<EMAIL>", "attachments": ["police_report.pdf", "damage_photos.jpg", "medical_note.pdf"]}}]}, "settings": {"executionOrder": "v1"}, "tags": [{"name": "Canadian Claims", "id": "canadian-claims"}, {"name": "Complete Workflow", "id": "complete-workflow"}], "triggerCount": 0, "updatedAt": "2024-01-20T15:30:00.000Z", "versionId": "canadian-v1"}