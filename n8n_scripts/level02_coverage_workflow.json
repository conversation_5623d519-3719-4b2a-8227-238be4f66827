{"name": "Zurich Level 02 Coverage Analysis Workflow", "nodes": [{"parameters": {}, "id": "a7f5b394-5a15-4c52-98f6-8d2e34c7f912", "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"httpMethod": "POST", "path": "level02-coverage-webhook", "responseMode": "responseNode", "options": {"rawBody": true}}, "id": "b8c6e495-6b26-4d63-a9g7-9e3f45d8g023", "name": "Level 02 Coverage Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [420, 300], "webhookId": "level02-coverage-analysis"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "c9d7f506-7c37-4e74-b0h8-0f4g56e9h134", "leftValue": "={{ $json.claim_id }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "d0e8g617-8d48-5f85-c1i9-1g5h67f0i245", "leftValue": "={{ $json.level01_analysis }}", "rightValue": "", "operator": {"type": "object", "operation": "notEmpty"}}], "combinator": "and"}, "options": {}}, "id": "c9d7f506-7c37-4e74-b0h8-0f4g56e9h134", "name": "Validate Input Data", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [600, 300]}, {"parameters": {"method": "POST", "url": "http://localhost:8000/api/level02-coverage/simple", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpBasicAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "claim_id", "value": "={{ $json.claim_id }}"}, {"name": "level01_analysis", "value": "={{ $json.level01_analysis }}"}, {"name": "policy_documents", "value": "={{ $json.policy_documents || [] }}"}, {"name": "additional_evidence", "value": "={{ $json.additional_evidence || [] }}"}, {"name": "human_inputs", "value": "={{ $json.human_inputs || [] }}"}, {"name": "urgency_level", "value": "={{ $json.urgency_level || 'NORMAL' }}"}]}, "options": {"timeout": 30000, "retry": {"enabled": true, "maxRetries": 3, "waitBetween": 1000}}}, "id": "e1f9h728-9e59-6g96-d2j0-2h6i78g1j356", "name": "Level 02 Coverage Analysis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [780, 200], "continueOnFail": true}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "f2g0i839-0f60-7h07-e3k1-3i7j89h2k467", "leftValue": "={{ $json.success }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "f2g0i839-0f60-7h07-e3k1-3i7j89h2k467", "name": "Analysis Success Check", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [960, 200]}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.coverage_decision }}", "rightValue": "NOT_COVERED", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "not_covered"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.coverage_decision }}", "rightValue": "COVERED", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "covered"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.coverage_decision }}", "rightValue": "INFORMATION_REQUIRED", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "information_required"}]}, "options": {}}, "id": "g3h1j940-1g71-8i18-f4l2-4j8k90i3l578", "name": "Coverage Decision Router", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [1140, 80]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "h4i2k051-2h82-9j29-g5m3-5k9l01j4m689", "leftValue": "={{ $json.requires_human_review }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "or"}, "options": {}}, "id": "h4i2k051-2h82-9j29-g5m3-5k9l01j4m689", "name": "Human Review Check - NOT_COVERED", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1320, -80]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "i5j3l162-3i93-0k30-h6n4-6l0m12k5n790", "leftValue": "={{ $json.requires_legal_counsel }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "or"}, "options": {}}, "id": "i5j3l162-3i93-0k30-h6n4-6l0m12k5n790", "name": "Legal Review Check - COVERED", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1320, 80]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "j6k4m273-4j04-1l41-i7o5-7m1n23l6o801", "leftValue": "={{ $json.information_needed_count }}", "rightValue": 0, "operator": {"type": "number", "operation": "gt"}}], "combinator": "and"}, "options": {}}, "id": "j6k4m273-4j04-1l41-i7o5-7m1n23l6o801", "name": "Information Count Check", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1320, 240]}, {"parameters": {"method": "POST", "url": "http://localhost:8000/api/level02-coverage", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpBasicAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "claim_id", "value": "={{ $('Level 02 Coverage Webhook').item.json.claim_id }}"}, {"name": "level01_analysis", "value": "={{ $('Level 02 Coverage Webhook').item.json.level01_analysis }}"}, {"name": "policy_documents", "value": "={{ $('Level 02 Coverage Webhook').item.json.policy_documents || [] }}"}, {"name": "additional_evidence", "value": "={{ $('Level 02 Coverage Webhook').item.json.additional_evidence || [] }}"}, {"name": "human_inputs", "value": "={{ $('Level 02 Coverage Webhook').item.json.human_inputs || [] }}"}, {"name": "urgency_level", "value": "HIGH"}]}, "options": {"timeout": 60000}}, "id": "k7l5n384-5k15-2m52-j8p6-8n2o34m7p912", "name": "Detailed Coverage Analysis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1500, -80]}, {"parameters": {"resource": "message", "operation": "postToChannel", "channel": "#claims-denials", "text": "🚨 CLAIM DENIAL REQUIRING HUMAN REVIEW 🚨\n\n📋 Claim ID: {{ $json.claim_id }}\n❌ Decision: NOT_COVERED\n🎯 Confidence: {{ Math.round($json.confidence_score * 100) }}%\n⚖️ Legal Review: {{ $json.requires_legal_counsel ? 'REQUIRED' : 'Not Required' }}\n🔥 Priority: {{ $json.priority_level }}\n\n📄 Primary Reason: {{ $json.primary_reason || 'Detailed analysis required' }}\n⏱️ Timeline: {{ $json.estimated_resolution_timeline || 'To be determined' }}\n\n👤 Action Required: Senior claims adjuster review\n📞 Escalation: {{ $json.requires_legal_counsel ? 'Legal counsel consultation needed' : 'Claims management review' }}", "otherOptions": {}}, "id": "l8m6o495-6l26-3n63-k9q7-9o3p45n8q023", "name": "Notify Denial Review Team", "type": "n8n-nodes-base.slack", "typeVersion": 2.1, "position": [1680, -80]}, {"parameters": {"resource": "message", "operation": "postToChannel", "channel": "#claims-settlement", "text": "✅ COVERAGE CONFIRMED - PROCEED TO SETTLEMENT ✅\n\n📋 Claim ID: {{ $json.claim_id }}\n💚 Decision: COVERED\n🎯 Confidence: {{ Math.round($json.confidence_score * 100) }}%\n⚖️ Legal Review: {{ $json.requires_legal_counsel ? 'REQUIRED' : 'Not Required' }}\n🔥 Priority: {{ $json.priority_level }}\n\n📄 Primary Reason: {{ $json.primary_reason || 'Policy terms support coverage' }}\n⏱️ Expected Resolution: {{ $json.estimated_resolution_timeline || 'Standard processing time' }}\n\n🎯 Next Step: {{ $json.requires_legal_counsel ? 'Legal counsel review before settlement' : 'Proceed to settlement calculation' }}\n💰 Settlement Team: Ready for claim evaluation", "otherOptions": {}}, "id": "m9n7p506-7m37-4o74-l0r8-0p4q56o9r134", "name": "Notify Settlement Team", "type": "n8n-nodes-base.slack", "typeVersion": 2.1, "position": [1680, 80]}, {"parameters": {"method": "POST", "url": "http://localhost:8000/api/level02-coverage", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpBasicAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "claim_id", "value": "={{ $('Level 02 Coverage Webhook').item.json.claim_id }}"}, {"name": "level01_analysis", "value": "={{ $('Level 02 Coverage Webhook').item.json.level01_analysis }}"}, {"name": "policy_documents", "value": "={{ $('Level 02 Coverage Webhook').item.json.policy_documents || [] }}"}, {"name": "additional_evidence", "value": "={{ $('Level 02 Coverage Webhook').item.json.additional_evidence || [] }}"}, {"name": "human_inputs", "value": "={{ $('Level 02 Coverage Webhook').item.json.human_inputs || [] }}"}, {"name": "urgency_level", "value": "HIGH"}]}, "options": {"timeout": 60000}}, "id": "n0o8q617-8n48-5p85-m1s9-1q5r67p0s245", "name": "Get Information Requirements", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1500, 240]}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"leftValue": "={{ $json.analysis_results.allInformationRequests }}", "rightValue": "", "operator": {"type": "array", "operation": "isNotEmpty"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "has_requirements"}]}, "options": {}}, "id": "o1p9r728-9o59-6q96-n2t0-2r6s78q1t356", "name": "Information Available Check", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [1680, 240]}, {"parameters": {"jsCode": "// Process information requirements and categorize by source\nconst requirements = $input.first().json.analysis_results.allInformationRequests || [];\n\nconst categorized = {\n  claimant_requests: [],\n  third_party_requests: [],\n  agent_obtainable: [],\n  expert_required: [],\n  legal_required: []\n};\n\nrequirements.forEach(req => {\n  switch(req.informationSource) {\n    case 'CLAIMANT':\n      categorized.claimant_requests.push(req);\n      break;\n    case 'THIRD_PARTY':\n      categorized.third_party_requests.push(req);\n      break;\n    case 'INSURANCE_AGENT':\n      categorized.agent_obtainable.push(req);\n      break;\n    case 'EXPERT_ASSESSMENT':\n      categorized.expert_required.push(req);\n      break;\n    case 'LEGAL_COUNSEL':\n      categorized.legal_required.push(req);\n      break;\n  }\n});\n\n// Create summary\nconst summary = {\n  claim_id: $input.first().json.claim_id,\n  total_requirements: requirements.length,\n  claimant_count: categorized.claimant_requests.length,\n  third_party_count: categorized.third_party_requests.length,\n  agent_count: categorized.agent_obtainable.length,\n  expert_count: categorized.expert_required.length,\n  legal_count: categorized.legal_required.length,\n  categorized_requirements: categorized,\n  urgency_summary: {\n    immediate: requirements.filter(r => r.urgencyLevel === 'IMMEDIATE').length,\n    high: requirements.filter(r => r.urgencyLevel === 'HIGH').length,\n    normal: requirements.filter(r => r.urgencyLevel === 'NORMAL').length,\n    low: requirements.filter(r => r.urgencyLevel === 'LOW').length\n  }\n};\n\nreturn { json: summary };"}, "id": "p2q0s839-0p60-7r07-o3u1-3s7t89r2u467", "name": "Process Information Requirements", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1860, 240]}, {"parameters": {"resource": "message", "operation": "postToChannel", "channel": "#claims-information", "text": "📋 INFORMATION REQUIRED FOR COVERAGE DECISION 📋\n\n🆔 Claim ID: {{ $json.claim_id }}\n📊 Total Requirements: {{ $json.total_requirements }}\n\n📝 BREAKDOWN BY SOURCE:\n👤 Claimant: {{ $json.claimant_count }} items\n🏢 Third Party: {{ $json.third_party_count }} items\n🔍 Agent Can Obtain: {{ $json.agent_count }} items\n👨‍⚕️ Expert Assessment: {{ $json.expert_count }} items\n⚖️ Legal Counsel: {{ $json.legal_count }} items\n\n⏰ URGENCY BREAKDOWN:\n🚨 Immediate: {{ $json.urgency_summary.immediate }}\n🔥 High: {{ $json.urgency_summary.high }}\n📅 Normal: {{ $json.urgency_summary.normal }}\n⏳ Low: {{ $json.urgency_summary.low }}\n\n🎯 Action Required: Coordinate information gathering from multiple sources\n📞 Next Steps: Claims adjuster to initiate information requests", "otherOptions": {}}, "id": "q3r1t940-1q71-8s18-p4v2-4t8u90s3v578", "name": "Notify Information Team", "type": "n8n-nodes-base.slack", "typeVersion": 2.1, "position": [2040, 240]}, {"parameters": {"resource": "message", "operation": "postToChannel", "channel": "#claims-errors", "text": "❌ LEVEL 02 COVERAGE ANALYSIS FAILED ❌\n\n🆔 Claim ID: {{ $('Level 02 Coverage Webhook').item.json.claim_id }}\n🕐 Timestamp: {{ new Date().toISOString() }}\n\n🚨 Error Details:\n{{ $json.error || 'Analysis service unavailable' }}\n\n🔧 Technical Info:\n{{ JSON.stringify($json, null, 2) }}\n\n⚠️ Action Required: Technical team investigation\n🎯 Escalation: Manual coverage review needed\n📞 Contact: Claims IT support team", "otherOptions": {}}, "id": "r4s2u051-2r82-9t29-q5w3-5u9v01t4w689", "name": "Notify Analysis Error", "type": "n8n-nodes-base.slack", "typeVersion": 2.1, "position": [780, 400]}, {"parameters": {"resource": "message", "operation": "postToChannel", "channel": "#claims-errors", "text": "⚠️ INVALID INPUT DATA FOR LEVEL 02 ANALYSIS ⚠️\n\n🚨 Validation Failure:\nMissing required fields for Level 02 coverage analysis\n\n📋 Required Data:\n• claim_id: {{ $json.claim_id ? '✅ Present' : '❌ Missing' }}\n• level01_analysis: {{ $json.level01_analysis ? '✅ Present' : '❌ Missing' }}\n\n🔧 Input Data Received:\n{{ JSON.stringify($json, null, 2) }}\n\n⚠️ Action Required: Check Level 01 analysis workflow output\n🎯 Escalation: Data validation team review", "otherOptions": {}}, "id": "s5t3v162-3s93-0u30-r6x4-6v0w12u5x790", "name": "Notify Input Validation Error", "type": "n8n-nodes-base.slack", "typeVersion": 2.1, "position": [600, 480]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": true,\n  \"message\": \"Level 02 coverage analysis completed - Claim denial requires human review\",\n  \"claim_id\": $json.claim_id,\n  \"coverage_decision\": \"NOT_COVERED\",\n  \"next_action\": \"Human review and legal consultation\",\n  \"timestamp\": new Date().toISOString()\n} }}", "options": {}}, "id": "t6u4w273-4t04-1v41-s7y5-7w1x23v6y801", "name": "Response - Denial Review", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1860, -80]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": true,\n  \"message\": \"Level 02 coverage analysis completed - Coverage confirmed\",\n  \"claim_id\": $json.claim_id,\n  \"coverage_decision\": \"COVERED\",\n  \"next_action\": $json.requires_legal_counsel ? \"Legal review before settlement\" : \"Proceed to settlement\",\n  \"confidence_score\": $json.confidence_score,\n  \"timestamp\": new Date().toISOString()\n} }}", "options": {}}, "id": "u7v5x384-5u15-2w52-t8z6-8x2y34w7z912", "name": "Response - Coverage Confirmed", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1860, 80]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": true,\n  \"message\": \"Level 02 coverage analysis completed - Information required\",\n  \"claim_id\": $json.claim_id,\n  \"coverage_decision\": \"INFORMATION_REQUIRED\",\n  \"information_needed\": $json.total_requirements,\n  \"next_action\": \"Coordinate information gathering\",\n  \"requirements_summary\": $json.categorized_requirements,\n  \"timestamp\": new Date().toISOString()\n} }}", "options": {}}, "id": "v8w6y495-6v26-3x63-u9a7-9y3z45x8a023", "name": "Response - Information Required", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2220, 240]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": false,\n  \"message\": \"Level 02 coverage analysis failed\",\n  \"error\": $json.error || \"Analysis service unavailable\",\n  \"claim_id\": $('Level 02 Coverage Webhook').item.json.claim_id,\n  \"timestamp\": new Date().toISOString(),\n  \"next_action\": \"Manual review required\"\n} }}", "options": {}}, "id": "w9x7z506-7w37-4y74-v0b8-0z4a56y9b134", "name": "Response - Analysis Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [960, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": false,\n  \"message\": \"Invalid input data for Level 02 analysis\",\n  \"error\": \"Missing required fields: claim_id and/or level01_analysis\",\n  \"received_data\": $json,\n  \"timestamp\": new Date().toISOString(),\n  \"next_action\": \"Provide valid Level 01 analysis results\"\n} }}", "options": {}}, "id": "x0y8a617-8x48-5z85-w1c9-1a5b67z0c245", "name": "Response - Input Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [780, 480]}], "connections": {"Start": {"main": [[{"node": "Level 02 Coverage Webhook", "type": "main", "index": 0}]]}, "Level 02 Coverage Webhook": {"main": [[{"node": "Validate Input Data", "type": "main", "index": 0}]]}, "Validate Input Data": {"main": [[{"node": "Level 02 Coverage Analysis", "type": "main", "index": 0}], [{"node": "Notify Input Validation Error", "type": "main", "index": 0}]]}, "Level 02 Coverage Analysis": {"main": [[{"node": "Analysis Success Check", "type": "main", "index": 0}]]}, "Analysis Success Check": {"main": [[{"node": "Coverage Decision Router", "type": "main", "index": 0}], [{"node": "Notify Analysis Error", "type": "main", "index": 0}]]}, "Coverage Decision Router": {"main": [[{"node": "Human Review Check - NOT_COVERED", "type": "main", "index": 0}], [{"node": "Legal Review Check - COVERED", "type": "main", "index": 0}], [{"node": "Information Count Check", "type": "main", "index": 0}]]}, "Human Review Check - NOT_COVERED": {"main": [[{"node": "Detailed Coverage Analysis", "type": "main", "index": 0}], [{"node": "Notify Denial Review Team", "type": "main", "index": 0}]]}, "Legal Review Check - COVERED": {"main": [[{"node": "Notify Settlement Team", "type": "main", "index": 0}], [{"node": "Notify Settlement Team", "type": "main", "index": 0}]]}, "Information Count Check": {"main": [[{"node": "Get Information Requirements", "type": "main", "index": 0}], [{"node": "Notify Information Team", "type": "main", "index": 0}]]}, "Detailed Coverage Analysis": {"main": [[{"node": "Notify Denial Review Team", "type": "main", "index": 0}]]}, "Notify Denial Review Team": {"main": [[{"node": "Response - Denial Review", "type": "main", "index": 0}]]}, "Notify Settlement Team": {"main": [[{"node": "Response - Coverage Confirmed", "type": "main", "index": 0}]]}, "Get Information Requirements": {"main": [[{"node": "Information Available Check", "type": "main", "index": 0}]]}, "Information Available Check": {"main": [[{"node": "Process Information Requirements", "type": "main", "index": 0}]]}, "Process Information Requirements": {"main": [[{"node": "Notify Information Team", "type": "main", "index": 0}]]}, "Notify Information Team": {"main": [[{"node": "Response - Information Required", "type": "main", "index": 0}]]}, "Notify Analysis Error": {"main": [[{"node": "Response - Analysis Error", "type": "main", "index": 0}]]}, "Notify Input Validation Error": {"main": [[{"node": "Response - Input Error", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-12-17T10:00:00.000Z", "updatedAt": "2024-12-17T10:00:00.000Z", "id": "level02-coverage", "name": "Level 02 Coverage"}], "triggerCount": 1, "updatedAt": "2024-12-17T10:00:00.000Z", "versionId": "level02-coverage-v1.0"}