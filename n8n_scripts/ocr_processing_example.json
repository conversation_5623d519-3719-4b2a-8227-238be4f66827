{
  "name": "Zurich OCR Processing Workflow",
  "description": "Example n8n workflow for processing documents through Zurich OCR API",
  "version": "1.0.0",
  "nodes": [
    {
      "name": "Manual Trigger",
      "type": "n8n-nodes-base.manualTrigger",
      "position": [280, 360],
      "parameters": {},
      "typeVersion": 1
    },
    {
      "name": "HTTP Request - OCR Processing",
      "type": "n8n-nodes-base.httpRequest",
      "position": [500, 360],
      "parameters": {
                 "url": "http://localhost:8000/ocr/batch-process",
        "method": "POST",
        "sendBody": true,
        "contentType": "multipart-form-data",
        "bodyParameters": {
          "parameters": [
            {
              "name": "config",
              "value": "{\n  \"ocr_engine\": \"google\",\n  \"google_processor\": \"OCR_PROCESSOR\",\n  \"llm_routing_enabled\": false,\n  \"post_processing\": \"v1\",\n  \"preprocessing\": \"none\",\n  \"parallel_processing\": false\n}"
            }
          ]
        },
        "options": {
          "timeout": 300000
        }
      },
      "typeVersion": 4.2
    },
    {
      "name": "Process OCR Results",
      "type": "n8n-nodes-base.set",
      "position": [720, 360],
      "parameters": {
        "assignments": {
          "assignments": [
            {
              "id": "extracted_text",
              "name": "extracted_text",
              "type": "string",
              "value": "={{ $json.results[0]?.extracted_text || 'No text extracted' }}"
            },
            {
              "id": "file_count",
              "name": "processed_files",
              "type": "number", 
              "value": "={{ $json.processed_files }}"
            },
            {
              "id": "processing_time",
              "name": "processing_time",
              "type": "number",
              "value": "={{ $json.processing_time }}"
            },
            {
              "id": "success",
              "name": "success",
              "type": "boolean",
              "value": "={{ $json.success }}"
            }
          ]
        }
      },
      "typeVersion": 3.4
    }
  ],
  "connections": {
    "Manual Trigger": {
      "main": [
        [
          {
            "node": "HTTP Request - OCR Processing",
            "type": "main", 
            "index": 0
          }
        ]
      ]
    },
    "HTTP Request - OCR Processing": {
      "main": [
        [
          {
            "node": "Process OCR Results",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "settings": {
    "executionOrder": "v1"
  }
}

/*
===========================================
n8n OCR API Integration Instructions
===========================================

Endpoint: POST http://localhost:8000/ocr/batch-process

1. HTTP Request Node Configuration:
   - Method: POST
   - Content Type: multipart-form-data
   - Timeout: 300000ms (5 minutes)

2. Body Parameters:
   - config (string): JSON configuration object
   - files: Binary file data (use n8n binary data)

3. Configuration Options:
   {
     "ocr_engine": "google",           // "google", "azure", "aws"
     "google_processor": "OCR_PROCESSOR", // For Google Vision
     "llm_routing_enabled": false,     // Enable AI routing
     "post_processing": "v1",          // Processing version
     "preprocessing": "none",          // Preprocessing options
     "parallel_processing": false     // Process files in parallel
   }

4. File Upload in n8n:
   - Use "Binary File" input type
   - Reference binary data: {{ $binary.data }}
   - Multiple files supported

5. Response Structure:
   {
     "success": true,
     "processed_files": 2,
     "results": [
       {
         "filename": "document1.pdf",
         "extracted_text": "Document content...",
         "confidence": 0.95,
         "metadata": {...}
       }
     ],
     "processing_time": 12.5,
     "config_used": {...}
   }

6. Error Handling:
   - Check "success" field
   - Use "error_message" for debugging
   - Implement retry logic for timeouts

7. Integration with Email Classification:
   - Extract text from attachments using OCR
   - Pass extracted text to /api/classify-email
   - Enhance claim detection accuracy

Example curl command:
curl -X POST "http://localhost:8000/ocr/api/ocr/batch-process" \
  -F 'files=@"document.pdf"' \
  -F 'config={"ocr_engine": "google", "llm_routing_enabled": false}'
*/ 