# =============================================================================
# ZURICH CLAIMS EXPLAINABILITY DASHBOARD - PRODUCTION ENVIRONMENT
# =============================================================================

# =============================================================================
# OPENAI CONFIGURATION
# =============================================================================
OPENAI_API_KEY=your_openai_api_key_here

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
FASTAPI_ENV=production
LOG_LEVEL=INFO
SERVICE_NAME=zurich-explainable-claims

# =============================================================================
# BAML CONFIGURATION
# =============================================================================
BAML_ENVIRONMENT=production

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
SUPABASE_URL=https://tlduggpohclrgxbvuzhd.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Eud_SDfGulErh3yhqjaIMqM37eghuz-PVeRxknzxRfk
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.wDi5bG1O81a3HaXOujWshJZoGV6AhFgEuaoVcjam0SE
SUPABASE_BUCKET_NAME=claims-attachments

# =============================================================================
# AI EXPLAINABILITY CONFIGURATION
# =============================================================================
AI_EXPLAINABILITY_ENABLED=true
AI_MODEL_PROVIDER=openai
AI_MODEL_NAME=gpt-4o
AI_HIGHLIGHT_CONFIDENCE_THRESHOLD=0.7
AI_MAX_HIGHLIGHTS_PER_DOCUMENT=50

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
BACKEND_URL=http://backend:8000
API_BASE_URL=http://backend:8000/api
REAL_TIME_UPDATES_ENABLED=true
FRONTEND_ANALYTICS_ENABLED=false

# =============================================================================
# NETWORK CONFIGURATION
# =============================================================================
BACKEND_PORT=8000
FRONTEND_PORT=3000
PROXY_PORT=80
PROXY_SSL_PORT=443

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Secret (generate with: openssl rand -hex 32)
JWT_SECRET_KEY=your_jwt_secret_key_here_32_chars_minimum

# CORS Configuration
CORS_ORIGINS=*
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Origin,X-Requested-With,Content-Type,Accept,Authorization

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================
PROMETHEUS_ENABLED=false
GRAFANA_ENABLED=false
LOKI_ENABLED=false

# =============================================================================
# CACHING CONFIGURATION
# =============================================================================
REDIS_ENABLED=false
REDIS_URL=redis://cache:6379
CACHE_TTL=3600

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
# Backend Workers (for production scaling)
BACKEND_WORKERS=4
BACKEND_MAX_REQUESTS=1000
BACKEND_MAX_REQUESTS_JITTER=50

# Database Connection Pool
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# =============================================================================
# FEATURE FLAGS
# =============================================================================
FEATURE_AI_BATCH_PROCESSING=true
FEATURE_REAL_TIME_COLLABORATION=false
FEATURE_ADVANCED_ANALYTICS=true
FEATURE_DOCUMENT_ANNOTATIONS=true
FEATURE_MULTI_LANGUAGE_SUPPORT=false

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================
DOCKER_REGISTRY=
IMAGE_TAG=latest
DEPLOYMENT_ENVIRONMENT=production
HEALTH_CHECK_INTERVAL=30s
RESTART_POLICY=unless-stopped 