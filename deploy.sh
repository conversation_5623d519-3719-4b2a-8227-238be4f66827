#!/bin/bash

# =============================================================================
# ZURICH CLAIMS EXPLAINABILITY DASHBOARD - DEPLOYMENT SCRIPT
# =============================================================================

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="zurich-explainable-claims"
DEFAULT_ENV="development"

# Print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command_exists docker-compose; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi
    
    print_success "All prerequisites are met."
}

# Function to validate environment variables
validate_environment() {
    local env_file="$1"
    
    if [ ! -f "$env_file" ]; then
        print_error "Environment file $env_file not found."
        return 1
    fi
    
    # Check for required variables
    local required_vars=("OPENAI_API_KEY" "SUPABASE_URL" "SUPABASE_SERVICE_ROLE_KEY")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^${var}=" "$env_file" || grep -q "^${var}=your_" "$env_file"; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        print_error "Missing or placeholder values for required environment variables:"
        printf '%s\n' "${missing_vars[@]}" | sed 's/^/  - /'
        print_warning "Please update $env_file with actual values."
        return 1
    fi
    
    print_success "Environment validation passed."
}

# Function to build images
build_images() {
    print_status "Building Docker images..."
    
    # Build backend image
    print_status "Building backend image..."
    docker build -t "${PROJECT_NAME}-backend:latest" -f Dockerfile .
    
    # Build frontend image
    print_status "Building frontend image..."
    docker build -t "${PROJECT_NAME}-frontend:latest" -f frontend.Dockerfile .
    
    print_success "All images built successfully."
}

# Function to deploy services
deploy_services() {
    local env_file="$1"
    local profile="${2:-}"
    
    print_status "Deploying services with environment: $env_file"
    
    # Create logs directory if it doesn't exist
    mkdir -p logs
    
    # Copy environment file for docker-compose
    cp "$env_file" .env
    
    # Deploy based on profile
    if [ -n "$profile" ]; then
        print_status "Deploying with profile: $profile"
        docker-compose --profile "$profile" up -d
    else
        print_status "Deploying core services (backend + frontend)"
        docker-compose up -d backend frontend
    fi
    
    print_success "Services deployed successfully."
}

# Function to check service health
check_health() {
    print_status "Checking service health..."
    
    local max_attempts=30
    local attempt=1
    
    # Check backend health
    print_status "Waiting for backend to be ready..."
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8000/health >/dev/null 2>&1; then
            print_success "Backend is healthy."
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            print_error "Backend failed to become healthy after $max_attempts attempts."
            return 1
        fi
        
        print_status "Attempt $attempt/$max_attempts - Backend not ready yet, waiting 5 seconds..."
        sleep 5
        ((attempt++))
    done
    
    # Check frontend health
    attempt=1
    print_status "Waiting for frontend to be ready..."
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:3000/health >/dev/null 2>&1; then
            print_success "Frontend is healthy."
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            print_error "Frontend failed to become healthy after $max_attempts attempts."
            return 1
        fi
        
        print_status "Attempt $attempt/$max_attempts - Frontend not ready yet, waiting 5 seconds..."
        sleep 5
        ((attempt++))
    done
    
    # Test AI explainability endpoint
    print_status "Testing AI explainability endpoint..."
    if curl -f http://localhost:8000/api/ai-explainability/health >/dev/null 2>&1; then
        print_success "AI explainability service is healthy."
    else
        print_warning "AI explainability service health check failed."
    fi
    
    print_success "Health checks completed."
}

# Function to show service status
show_status() {
    print_status "Service Status:"
    echo
    docker-compose ps
    echo
    
    print_status "Service URLs:"
    echo "  🌐 Frontend Dashboard: http://localhost:3000"
    echo "  🔧 Backend API: http://localhost:8000"
    echo "  📚 API Documentation: http://localhost:8000/docs"
    echo "  🧠 AI Explainability: http://localhost:8000/api/ai-explainability/info"
    echo
    
    if docker-compose ps | grep -q "zurich-proxy"; then
        echo "  🔄 Reverse Proxy: http://localhost"
    fi
    
    if docker-compose ps | grep -q "zurich-monitoring"; then
        echo "  📊 Monitoring: http://localhost:9090"
    fi
}

# Function to show logs
show_logs() {
    local service="${1:-}"
    
    if [ -n "$service" ]; then
        print_status "Showing logs for service: $service"
        docker-compose logs -f "$service"
    else
        print_status "Showing logs for all services (press Ctrl+C to exit)"
        docker-compose logs -f
    fi
}

# Function to stop services
stop_services() {
    print_status "Stopping all services..."
    docker-compose down
    print_success "All services stopped."
}

# Function to clean up
cleanup() {
    print_status "Cleaning up..."
    
    # Stop services
    docker-compose down --volumes --remove-orphans
    
    # Remove images (optional)
    read -p "Remove Docker images? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker rmi "${PROJECT_NAME}-backend:latest" "${PROJECT_NAME}-frontend:latest" 2>/dev/null || true
        print_success "Images removed."
    fi
    
    # Clean up .env file
    rm -f .env
    
    print_success "Cleanup completed."
}

# Function to run tests
run_tests() {
    print_status "Running tests..."
    
    # Test backend endpoints
    print_status "Testing backend endpoints..."
    
    # Health check
    curl -f http://localhost:8000/health || {
        print_error "Backend health check failed"
        return 1
    }
    
    # AI explainability health
    curl -f http://localhost:8000/api/ai-explainability/health || {
        print_error "AI explainability health check failed"
        return 1
    }
    
    # Frontend health
    curl -f http://localhost:3000/health || {
        print_error "Frontend health check failed"
        return 1
    }
    
    print_success "All tests passed."
}

# Function to show usage
show_help() {
    echo "Zurich Claims Explainability Dashboard - Deployment Script"
    echo
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo
    echo "Commands:"
    echo "  deploy [ENV_FILE] [PROFILE]  Deploy the application"
    echo "  build                        Build Docker images"
    echo "  status                       Show service status"
    echo "  logs [SERVICE]               Show logs for all services or specific service"
    echo "  stop                         Stop all services"
    echo "  restart [ENV_FILE]           Restart services"
    echo "  test                         Run health checks and tests"
    echo "  cleanup                      Stop services and clean up"
    echo "  help                         Show this help message"
    echo
    echo "Environment Files:"
    echo "  production.env               Production configuration"
    echo "  development.env              Development configuration (if exists)"
    echo
    echo "Profiles:"
    echo "  production                   Full production stack with reverse proxy"
    echo "  monitoring                   Include monitoring services"
    echo "  development                  Include development tools"
    echo "  cache                        Include Redis caching"
    echo
    echo "Examples:"
    echo "  $0 deploy production.env                  # Deploy with production config"
    echo "  $0 deploy production.env production       # Deploy with reverse proxy"
    echo "  $0 logs backend                          # Show backend logs"
    echo "  $0 status                               # Show service status"
}

# Main script logic
main() {
    local command="${1:-help}"
    
    case $command in
        "deploy")
            local env_file="${2:-production.env}"
            local profile="$3"
            
            check_prerequisites
            validate_environment "$env_file" || exit 1
            build_images
            deploy_services "$env_file" "$profile"
            check_health
            show_status
            
            print_success "🎉 Deployment completed successfully!"
            print_status "Access your dashboard at: http://localhost:3000"
            ;;
            
        "build")
            check_prerequisites
            build_images
            ;;
            
        "status")
            show_status
            ;;
            
        "logs")
            show_logs "$2"
            ;;
            
        "stop")
            stop_services
            ;;
            
        "restart")
            local env_file="${2:-production.env}"
            stop_services
            sleep 2
            deploy_services "$env_file"
            check_health
            show_status
            ;;
            
        "test")
            run_tests
            ;;
            
        "cleanup")
            cleanup
            ;;
            
        "help"|*)
            show_help
            ;;
    esac
}

# Run main function with all arguments
main "$@" 