"""
Enhanced Level 2 Coverage Router with 12-Factor App & 12-Factor Agents Compliance
"""

import os
import time
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from functools import wraps

from fastapi import APIRouter, HTTPException, status, Depends
from pydantic import BaseModel, Field
import structlog

# 12-Factor Agent: Human oversight for high-stakes decisions
from humanlayer import HumanLayer, require_approval

# 12-Factor App: Config from environment
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY") 
HUMAN_APPROVAL_REQUIRED = os.getenv("HUMAN_APPROVAL_REQUIRED", "true").lower() == "true"
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")

# 12-Factor App: Structured logging as event streams
logger = structlog.get_logger()

# 12-Factor Agents: Initialize human oversight
hl = HumanLayer() if HUMAN_APPROVAL_REQUIRED else None

# 12-Factor App: Backing services as attached resources
class BackingServices:
    """Treat backing services as attached resources"""
    
    def __init__(self):
        self.supabase = None
        self.baml_client = None
        
    async def initialize(self):
        """Initialize backing services"""
        try:
            if SUPABASE_URL and SUPABASE_KEY:
                from supabase import create_client
                self.supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
                logger.info("supabase_connected", url=SUPABASE_URL[:20] + "...")
            
            from baml_client import b
            self.baml_client = b
            logger.info("baml_client_initialized")
            
        except Exception as e:
            logger.error("backing_service_init_failed", error=str(e))
            raise
    
    def health_check(self) -> Dict[str, str]:
        """Check backing service health"""
        status = {}
        
        if self.supabase:
            try:
                # Simple health check
                self.supabase.table('claims').select('count').limit(1).execute()
                status['supabase'] = 'healthy'
            except:
                status['supabase'] = 'unhealthy'
        else:
            status['supabase'] = 'disabled'
            
        status['baml'] = 'healthy' if self.baml_client else 'unhealthy'
        return status

# Global backing services instance
services = BackingServices()

class EnhancedLevel02Request(BaseModel):
    """Enhanced Level 2 request with 12-factor compliance"""
    
    claim_id: str = Field(..., description="Unique claim identifier")
    level01_analysis: Dict[str, Any] = Field(..., description="Complete Level 01 analysis")
    
    # 12-Factor App: Configuration
    enhancement_config: Optional[Dict[str, Any]] = Field(
        default_factory=lambda: {
            "supabase_fetch": SUPABASE_URL is not None,
            "human_approval": HUMAN_APPROVAL_REQUIRED,
            "log_level": LOG_LEVEL
        }
    )
    
    # Additional data sources
    policy_documents: Optional[List[str]] = Field(default_factory=list)
    additional_evidence: Optional[List[str]] = Field(default_factory=list)
    urgency_level: str = Field(default="NORMAL")

class CoverageDecisionApproval(BaseModel):
    """Human approval model for high-stakes coverage decisions"""
    
    claim_id: str
    coverage_decision: str
    confidence_score: float
    financial_impact: str
    risk_level: str
    justification: str
    evidence_summary: List[str]

# 12-Factor Agents: High-stakes decision approval
@require_approval(approver_emails=["<EMAIL>"])
async def approve_coverage_decision(
    decision: CoverageDecisionApproval
) -> Dict[str, Any]:
    """
    HIGH-STAKES: Coverage decision requiring human approval
    
    This function handles insurance coverage determinations that have:
    - Financial implications ($5M+ potential liability)
    - Legal ramifications (liability determinations) 
    - Customer relationship impact
    """
    
    approval_context = {
        "decision_type": "INSURANCE_COVERAGE_DETERMINATION",
        "claim_id": decision.claim_id,
        "coverage_decision": decision.coverage_decision,
        "confidence_score": decision.confidence_score,
        "financial_impact": decision.financial_impact,
        "risk_assessment": decision.risk_level,
        "ai_justification": decision.justification,
        "supporting_evidence": decision.evidence_summary,
        "requires_legal_review": decision.coverage_decision == "NOT_COVERED",
        "timestamp": datetime.utcnow().isoformat()
    }
    
    logger.info(
        "coverage_decision_pending_approval",
        claim_id=decision.claim_id,
        decision=decision.coverage_decision,
        confidence=decision.confidence_score
    )
    
    return approval_context

# 12-Factor App: Backing service data fetching
async def fetch_supabase_enrichment(claim_id: str) -> Optional[Dict[str, Any]]:
    """Fetch enrichment data from Supabase backing service"""
    
    if not services.supabase:
        logger.warning("supabase_not_available", claim_id=claim_id)
        return None
    
    try:
        # Fetch OCR documents
        attachments_response = services.supabase.table('attachments')\
            .select('original_filename, ocr_text')\
            .eq('claim_id', claim_id)\
            .execute()
        
        # Fetch email details
        claims_response = services.supabase.table('claims')\
            .select('email_subject, email_body, claim_reference')\
            .eq('claim_reference', claim_id)\
            .execute()
        
        enrichment_data = {
            'attachments': attachments_response.data,
            'claim_details': claims_response.data,
            'enrichment_timestamp': datetime.utcnow().isoformat(),
            'data_source': 'supabase'
        }
        
        logger.info(
            "supabase_enrichment_successful",
            claim_id=claim_id,
            attachments_count=len(attachments_response.data),
            claims_count=len(claims_response.data)
        )
        
        return enrichment_data
        
    except Exception as e:
        logger.error(
            "supabase_enrichment_failed",
            claim_id=claim_id,
            error=str(e)
        )
        return None

# Enhanced Level 2 analysis with 12-factor compliance
async def analyze_coverage_enhanced(request: EnhancedLevel02Request) -> Dict[str, Any]:
    """
    Enhanced Level 2 coverage analysis with:
    - 12-Factor App: Backing services, config, logging
    - 12-Factor Agents: Human oversight for high-stakes decisions
    """
    
    start_time = time.time()
    
    # 12-Factor App: Structured logging
    logger.info(
        "level02_analysis_started",
        claim_id=request.claim_id,
        urgency=request.urgency_level,
        supabase_enabled=request.enhancement_config.get("supabase_fetch", False)
    )
    
    try:
        # Step 1: Fetch Supabase enrichment (if available)
        enrichment_data = None
        if request.enhancement_config.get("supabase_fetch", False):
            enrichment_data = await fetch_supabase_enrichment(request.claim_id)
        
        # Step 2: Prepare enhanced BAML input
        baml_input = prepare_enhanced_baml_input(request, enrichment_data)
        
        # Step 3: Execute BAML analysis
        baml_result = services.baml_client.AnalyzeCoverageLevel02(baml_input)
        
        processing_time = int((time.time() - start_time) * 1000)
        
        logger.info(
            "baml_analysis_completed",
            claim_id=request.claim_id,
            decision=baml_result.coverageDecision.value,
            confidence=baml_result.confidenceScore,
            processing_time_ms=processing_time
        )
        
        # Step 4: 12-Factor Agents - High-stakes decision approval
        if request.enhancement_config.get("human_approval", False):
            if is_high_stakes_decision(baml_result):
                approval_request = CoverageDecisionApproval(
                    claim_id=request.claim_id,
                    coverage_decision=baml_result.coverageDecision.value,
                    confidence_score=baml_result.confidenceScore,
                    financial_impact=calculate_financial_impact(baml_result),
                    risk_level=assess_risk_level(baml_result),
                    justification=extract_justification(baml_result),
                    evidence_summary=extract_evidence_summary(baml_result)
                )
                
                # Human approval required for high-stakes decisions
                approval_result = await approve_coverage_decision(approval_request)
                
                logger.info(
                    "human_approval_requested",
                    claim_id=request.claim_id,
                    decision=baml_result.coverageDecision.value
                )
                
                return {
                    "analysis_result": baml_result,
                    "human_approval": approval_result,
                    "processing_time_ms": processing_time,
                    "enrichment_applied": enrichment_data is not None,
                    "status": "PENDING_HUMAN_APPROVAL"
                }
        
        return {
            "analysis_result": baml_result,
            "processing_time_ms": processing_time,
            "enrichment_applied": enrichment_data is not None,
            "status": "COMPLETED"
        }
        
    except Exception as e:
        logger.error(
            "level02_analysis_failed",
            claim_id=request.claim_id,
            error=str(e),
            processing_time_ms=int((time.time() - start_time) * 1000)
        )
        raise

# Helper functions for 12-factor agents
def is_high_stakes_decision(baml_result) -> bool:
    """Determine if decision requires human approval"""
    
    high_stakes_conditions = [
        baml_result.coverageDecision.value == "NOT_COVERED",  # Denial decisions
        baml_result.confidenceScore < 0.8,  # Low confidence
        baml_result.riskAssessment and baml_result.riskAssessment.financialRisk == "HIGH",
        len(baml_result.uncertaintyAreas or []) > 3  # Many uncertainties
    ]
    
    return any(high_stakes_conditions)

def calculate_financial_impact(baml_result) -> str:
    """Calculate financial impact of coverage decision"""
    # Implementation would analyze policy limits, damages, etc.
    return "HIGH"  # Simplified for example

def assess_risk_level(baml_result) -> str:
    """Assess overall risk level of decision"""
    return baml_result.riskAssessment.overallRisk.value if baml_result.riskAssessment else "MEDIUM"

def extract_justification(baml_result) -> str:
    """Extract AI justification for human review"""
    return baml_result.coverageJustification.primaryReason.value if baml_result.coverageJustification else ""

def extract_evidence_summary(baml_result) -> List[str]:
    """Extract key evidence for human review"""
    return baml_result.level01Data.keyFindings if baml_result.level01Data else []

def prepare_enhanced_baml_input(request: EnhancedLevel02Request, enrichment_data: Optional[Dict]) -> Any:
    """Prepare BAML input with potential Supabase enrichment"""
    
    # Start with base Level 1 data
    base_input = request.level01_analysis
    
    # Enhance with Supabase data if available
    if enrichment_data:
        # Add raw OCR documents
        if enrichment_data.get('attachments'):
            raw_documents = [
                f"FILENAME: {att['original_filename']}\nCONTENT: {att['ocr_text']}"
                for att in enrichment_data['attachments']
            ]
            request.policy_documents.extend(raw_documents)
        
        # Add full email context
        if enrichment_data.get('claim_details'):
            for claim in enrichment_data['claim_details']:
                request.additional_evidence.append(
                    f"EMAIL_SUBJECT: {claim.get('email_subject', '')}\n"
                    f"EMAIL_BODY: {claim.get('email_body', '')}"
                )
    
    # Convert to BAML input format
    from baml_client.types import Level02AnalysisInput, Level01Summary
    
    level01_summary = Level01Summary(
        claimId=base_input.get('claimId', request.claim_id),
        claimType=base_input.get('claimType', 'Unknown'),
        policyNumber=base_input.get('policyNumber', ''),
        incidentDate=base_input.get('incidentDate', ''),
        primaryCause=base_input.get('primaryCause', ''),
        level01Confidence=base_input.get('confidenceScore', 0.0),
        level01ExitPath=base_input.get('level01ExitPath', ''),
        keyFindings=base_input.get('keyFindings', [])
    )
    
    return Level02AnalysisInput(
        claimId=request.claim_id,
        level01Analysis=level01_summary,
        policyDocuments=request.policy_documents,
        additionalEvidence=request.additional_evidence,
        urgencyLevel=request.urgency_level,
        specialInstructions=[]
    )

# Initialize router with 12-factor app compliance
router = APIRouter(
    prefix="/api/level02-coverage",
    tags=["level02-coverage"]
)

@router.on_event("startup")
async def startup_event():
    """12-Factor App: Initialize backing services on startup"""
    await services.initialize()
    logger.info("level02_router_initialized", backing_services=services.health_check())

@router.post("/enhanced", response_model=Dict[str, Any])
async def analyze_coverage_level02_enhanced(request: EnhancedLevel02Request):
    """
    Enhanced Level 02 Coverage Analysis with 12-Factor Compliance
    
    Features:
    - 12-Factor App: Config from env, backing services, structured logging
    - 12-Factor Agents: Human approval for high-stakes decisions
    - Supabase enrichment for improved accuracy
    - Graceful degradation when services unavailable
    """
    
    return await analyze_coverage_enhanced(request)

@router.get("/health-enhanced")
async def enhanced_health_check():
    """Enhanced health check with backing service status"""
    
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "backing_services": services.health_check(),
        "human_approval": HUMAN_APPROVAL_REQUIRED,
        "supabase_enabled": SUPABASE_URL is not None,
        "config_source": "environment"
    } 