# 12-Factor App: Configuration in Environment Variables
# Level 2 Enhanced Coverage Analysis Configuration

# Backing Services (Factor IV)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-supabase-anon-key

# 12-Factor Agents: Human Oversight Configuration  
HUMAN_APPROVAL_REQUIRED=true
CLAIMS_MANAGER_EMAIL=<EMAIL>
HIGH_STAKES_THRESHOLD=0.8

# Application Configuration
LOG_LEVEL=INFO
ENVIRONMENT=production
APP_VERSION=2.0.0

# BAML Configuration
BAML_MODEL_VERSION=gpt-4o
BAML_CONFIDENCE_THRESHOLD=0.75

# Performance Configuration
SUPABASE_TIMEOUT_SECONDS=10
MAX_DOCUMENT_SIZE_MB=50
PROCESSING_TIMEOUT_SECONDS=300

# Security Configuration
API_RATE_LIMIT=100
CORS_ORIGINS=https://zurich-claims.app,https://admin.zurich.com

# Monitoring & Observability (Factor XI)
STRUCTURED_LOGGING=true
LOG_FORMAT=json
METRICS_ENABLED=true
TRACING_ENABLED=true

# Feature Flags
ENABLE_SUPABASE_ENRICHMENT=true
ENABLE_CANADIAN_LEGAL_ANALYSIS=true
ENABLE_CANLII_INTEGRATION=false 