# ZURICH INSURANCE - PRODUCTION NGINX CONFIGURATION
# Reverse proxy for Explainable Claims Dashboard
# Routes frontend and backend services with load balancing and security

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    # Basic Settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;
    
    # Security Headers
    server_tokens off;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; img-src 'self' data: https:; font-src 'self' https://cdnjs.cloudflare.com; connect-src 'self' https://tlduggpohclrgxbvuzhd.supabase.co" always;
    
    # MIME Types
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # Logging
    log_format detailed '$remote_addr - $remote_user [$time_local] '
                       '"$request" $status $body_bytes_sent '
                       '"$http_referer" "$http_user_agent" '
                       'rt=$request_time uct="$upstream_connect_time" '
                       'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    access_log /var/log/nginx/access.log detailed;
    error_log /var/log/nginx/error.log warn;
    
    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=30r/m;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    
    # Upstream Backend Services
    upstream backend_api {
        least_conn;
        server backend:8000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }
    
    upstream frontend_app {
        least_conn;
        server frontend:80 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }
    
    # ==========================================================================
    # MAIN APPLICATION SERVER
    # ==========================================================================
    
    server {
        listen 80;
        server_name localhost zurich-claims.local;
        
        # Security and performance settings
        client_header_timeout 10s;
        client_body_timeout 10s;
        send_timeout 10s;
        
        # Root location - serve frontend
        location / {
            proxy_pass http://frontend_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Frontend-specific headers
            proxy_set_header Cache-Control "no-cache, no-store, must-revalidate";
            proxy_set_header Pragma "no-cache";
            proxy_set_header Expires "0";
            
            # Timeout settings
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # Buffer settings
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
        }
        
        # API Routes - proxy to backend
        location /api/ {
            # Rate limiting for API endpoints
            limit_req zone=api burst=10 nodelay;
            
            proxy_pass http://backend_api/api/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # CORS headers for API
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization" always;
            
            # Handle preflight requests
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin "*";
                add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
                add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
                add_header Content-Length 0;
                add_header Content-Type text/plain;
                return 204;
            }
            
            # API-specific timeout settings
            proxy_connect_timeout 10s;
            proxy_send_timeout 120s;
            proxy_read_timeout 120s;
            
            # Buffer settings for larger API responses
            proxy_buffering on;
            proxy_buffer_size 8k;
            proxy_buffers 16 8k;
            proxy_busy_buffers_size 16k;
        }
        
        # AI Explainability endpoints (special handling for longer processing)
        location /api/ai-explainability/ {
            # More lenient rate limiting for AI processing
            limit_req zone=api burst=5 nodelay;
            
            proxy_pass http://backend_api/api/ai-explainability/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Extended timeouts for AI processing
            proxy_connect_timeout 15s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            
            # Larger buffers for AI responses
            proxy_buffering on;
            proxy_buffer_size 16k;
            proxy_buffers 32 16k;
            proxy_busy_buffers_size 32k;
            
            # CORS headers
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization" always;
        }
        
        # Health Check Endpoints
        location /health {
            access_log off;
            
            # Check both frontend and backend health
            location = /health {
                proxy_pass http://frontend_app/health;
                proxy_set_header Host $host;
            }
            
            location = /health/backend {
                proxy_pass http://backend_api/health;
                proxy_set_header Host $host;
            }
            
            location = /health/frontend {
                proxy_pass http://frontend_app/health;
                proxy_set_header Host $host;
            }
        }
        
        # Static Assets with Caching
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            proxy_pass http://frontend_app;
            proxy_set_header Host $host;
            
            # Cache static assets
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Access-Control-Allow-Origin "*";
            
            # Disable access log for static assets
            access_log off;
        }
        
        # WebSocket support (for future real-time features)
        location /ws/ {
            proxy_pass http://backend_api/ws/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket-specific timeouts
            proxy_connect_timeout 10s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
        }
        
        # Deny access to sensitive files
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }
        
        location ~ /(\.git|\.env|\.htaccess|\.htpasswd) {
            deny all;
            access_log off;
            log_not_found off;
        }
        
        # Custom error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
    }
    
    # ==========================================================================
    # HTTPS SERVER (for production with SSL)
    # ==========================================================================
    
    server {
        listen 443 ssl http2;
        server_name localhost zurich-claims.local;
        
        # SSL Configuration (use your own certificates)
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        
        # HSTS (HTTP Strict Transport Security)
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        
        # Include all the same location blocks as HTTP server
        include /etc/nginx/conf.d/locations.conf;
    }
    
    # ==========================================================================
    # MONITORING AND STATUS
    # ==========================================================================
    
    server {
        listen 8080;
        server_name localhost;
        
        # Nginx status endpoint
        location /nginx_status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            allow **********/16;  # Docker network
            deny all;
        }
        
        # Basic health check
        location /status {
            access_log off;
            return 200 "nginx healthy\n";
            add_header Content-Type text/plain;
        }
    }
} 