-- Zurich Claims Application - Supabase Schema Setup
-- Run this in your Supabase SQL Editor to create the required tables

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create claims table with all required columns
CREATE TABLE IF NOT EXISTS claims (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    claim_id UUID DEFAULT gen_random_uuid(),
    claim_reference TEXT UNIQUE NOT NULL,
    email_subject TEXT,
    email_body TEXT,
    "01_level_analysis" JSONB,
    "02_level_analysis" JSONB,
    "03_level_analysis" JSONB,
    "04_level_analysis" JSONB,
    workflow_status VARCHAR(50) DEFAULT 'NEW',
    priority_score INTEGER,
    estimated_value DECIMAL(12,2),
    incident_date DATE,
    assigned_agent VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create attachments table
CREATE TABLE IF NOT EXISTS attachments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    claim_reference TEXT REFERENCES claims(claim_reference) ON DELETE CASCADE,
    file_name TEXT NOT NULL,
    content_type VARCHAR(100),
    file_size INTEGER,
    ocr_text TEXT,
    storage_path VARCHAR(500),
    upload_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processing_status VARCHAR(50) DEFAULT 'pending',
    processed_at TIMESTAMP WITH TIME ZONE,
    processing_metadata JSONB
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_claims_reference ON claims(claim_reference);
CREATE INDEX IF NOT EXISTS idx_claims_status ON claims(workflow_status);
CREATE INDEX IF NOT EXISTS idx_attachments_claim_ref ON attachments(claim_reference);
CREATE INDEX IF NOT EXISTS idx_attachments_status ON attachments(processing_status);

-- Create storage bucket for file attachments
INSERT INTO storage.buckets (id, name, public) 
VALUES ('claims-attachments', 'claims-attachments', true)
ON CONFLICT (id) DO NOTHING;

-- Set up Row Level Security (RLS) policies
-- Note: For development, we'll disable RLS to allow full access
-- In production, you should enable RLS with proper policies

-- Disable RLS for development (allows full access with service role key)
ALTER TABLE claims DISABLE ROW LEVEL SECURITY;
ALTER TABLE attachments DISABLE ROW LEVEL SECURITY;

-- Insert sample data for testing
INSERT INTO claims (claim_reference, email_subject, email_body, workflow_status) 
VALUES 
    ('CLM-85228383', 'Test Claim - Vehicle Accident', 'This is a test claim for vehicle accident coverage analysis.', 'NEW'),
    ('CLM-12345678', 'Property Damage Claim', 'Property damage claim requiring coverage analysis.', 'IN_PROGRESS')
ON CONFLICT (claim_reference) DO NOTHING;

-- Insert sample attachments
INSERT INTO attachments (claim_reference, file_name, content_type, ocr_text) 
VALUES 
    ('CLM-85228383', 'accident_report.pdf', 'application/pdf', 'Sample OCR text from accident report'),
    ('CLM-85228383', 'police_report.pdf', 'application/pdf', 'Sample OCR text from police report'),
    ('CLM-12345678', 'damage_photos.jpg', 'image/jpeg', 'Sample OCR text from damage photos')
ON CONFLICT DO NOTHING;

-- Grant necessary permissions
GRANT ALL ON claims TO authenticated;
GRANT ALL ON attachments TO authenticated;
GRANT ALL ON claims TO anon;
GRANT ALL ON attachments TO anon;

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_claims_updated_at 
    BEFORE UPDATE ON claims 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Verify setup
SELECT 'Schema setup completed successfully' as status;
SELECT COUNT(*) as claims_count FROM claims;
SELECT COUNT(*) as attachments_count FROM attachments; 