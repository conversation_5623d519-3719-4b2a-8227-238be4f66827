#!/usr/bin/env python3
"""
Test script to directly examine the database structure and verify data flow
This will help us understand the actual data structure we're working with
"""

import os
import sys
import json
from typing import Dict, Any, List
from supabase import create_client, Client

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

def initialize_supabase() -> Client:
    """Initialize Supabase client"""
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY") or os.getenv("SUPABASE_ANON_KEY")
    
    if not supabase_url or not supabase_key:
        raise Exception("Missing Supabase configuration")
    
    return create_client(supabase_url, supabase_key)

def test_claims_table_structure(supabase: Client):
    """Test claims table structure and content"""
    print("="*60)
    print("TESTING CLAIMS TABLE STRUCTURE")
    print("="*60)
    
    try:
        # Get all claims to see what we have
        claims_response = supabase.table('claims').select('*').limit(1).execute()
        
        if claims_response.data:
            claim = claims_response.data[0]
            print(f"Sample claim keys: {list(claim.keys())}")
            
            # Check specific fields we care about
            important_fields = [
                'claim_reference', 'email_subject', 'email_body',
                '01_level_analysis', '02_level_analysis', 
                '03_level_analysis', '04_level_analysis'
            ]
            
            for field in important_fields:
                if field in claim:
                    value = claim[field]
                    if isinstance(value, str) and len(value) > 100:
                        print(f"{field}: {type(value).__name__} (length: {len(value)}) - {value[:100]}...")
                    else:
                        print(f"{field}: {type(value).__name__} - {value}")
                else:
                    print(f"{field}: NOT FOUND")
            
            print(f"\nFull claim structure for reference:")
            for key, value in claim.items():
                if isinstance(value, str) and len(value) > 50:
                    print(f"  {key}: {type(value).__name__} (length: {len(value)})")
                else:
                    print(f"  {key}: {type(value).__name__} - {value}")
                    
        else:
            print("No claims found in database")
            
    except Exception as e:
        print(f"Error testing claims table: {e}")

def test_attachments_table_structure(supabase: Client):
    """Test attachments table structure and content"""
    print("\n" + "="*60)
    print("TESTING ATTACHMENTS TABLE STRUCTURE")
    print("="*60)
    
    try:
        # Get all attachments to see what we have
        attachments_response = supabase.table('attachments').select('*').limit(3).execute()
        
        if attachments_response.data:
            print(f"Found {len(attachments_response.data)} sample attachments")
            
            for i, attachment in enumerate(attachments_response.data):
                print(f"\n--- Attachment {i+1} ---")
                print(f"Keys: {list(attachment.keys())}")
                
                # Check specific fields we care about
                important_fields = [
                    'id', 'claim_reference', 'file_name', 'content_type', 
                    'file_size', 'ocr_text', 'confidence'
                ]
                
                for field in important_fields:
                    if field in attachment:
                        value = attachment[field]
                        if field == 'ocr_text' and isinstance(value, str):
                            if len(value) > 100:
                                print(f"  {field}: {type(value).__name__} (length: {len(value)}) - {value[:100]}...")
                            else:
                                print(f"  {field}: {type(value).__name__} - {value}")
                        else:
                            print(f"  {field}: {type(value).__name__} - {value}")
                    else:
                        print(f"  {field}: NOT FOUND")
                        
        else:
            print("No attachments found in database")
            
    except Exception as e:
        print(f"Error testing attachments table: {e}")

def test_specific_claim_data(supabase: Client, claim_reference: str):
    """Test data for a specific claim reference"""
    print("\n" + "="*60)
    print(f"TESTING SPECIFIC CLAIM: {claim_reference}")
    print("="*60)
    
    try:
        # Get the specific claim
        claims_response = supabase.table('claims')\
            .select('*')\
            .eq('claim_reference', claim_reference)\
            .execute()
        
        if claims_response.data:
            claim = claims_response.data[0]
            print(f"Claim found: {claim['claim_reference']}")
            
            # Show analysis levels
            for level in ['01_level_analysis', '02_level_analysis', '03_level_analysis', '04_level_analysis']:
                analysis = claim.get(level)
                if analysis:
                    print(f"\n{level}:")
                    if isinstance(analysis, dict):
                        print(f"  Type: dict with keys: {list(analysis.keys())}")
                    elif isinstance(analysis, str):
                        try:
                            parsed = json.loads(analysis)
                            print(f"  Type: JSON string with keys: {list(parsed.keys())}")
                        except:
                            print(f"  Type: string (length: {len(analysis)})")
                    else:
                        print(f"  Type: {type(analysis).__name__} - {analysis}")
                else:
                    print(f"\n{level}: None or empty")
            
            # Get attachments for this claim
            attachments_response = supabase.table('attachments')\
                .select('*')\
                .eq('claim_reference', claim_reference)\
                .execute()
            
            attachments = attachments_response.data or []
            print(f"\nAttachments found: {len(attachments)}")
            
            for i, att in enumerate(attachments):
                print(f"\n--- Attachment {i+1}: {att.get('file_name', 'Unknown')} ---")
                ocr_text = att.get('ocr_text', '')
                if ocr_text:
                    print(f"  OCR text available: {len(ocr_text)} characters")
                    print(f"  Preview: {ocr_text[:200]}...")
                else:
                    print(f"  No OCR text available")
                print(f"  Content type: {att.get('content_type', 'Unknown')}")
                print(f"  File size: {att.get('file_size', 'Unknown')}")
                print(f"  Confidence: {att.get('confidence', 'Unknown')}")
                
        else:
            print(f"Claim {claim_reference} not found")
            
    except Exception as e:
        print(f"Error testing specific claim: {e}")

def main():
    """Main test function"""
    print("ZURICH DATABASE STRUCTURE TEST")
    print("=" * 60)
    
    try:
        # Initialize Supabase
        supabase = initialize_supabase()
        print("✅ Supabase connection established")
        
        # Test claims table structure
        test_claims_table_structure(supabase)
        
        # Test attachments table structure  
        test_attachments_table_structure(supabase)
        
        # Test specific claim if we can find one
        print("\n" + "="*60)
        print("FINDING A SAMPLE CLAIM TO TEST")
        print("="*60)
        
        claims_response = supabase.table('claims').select('claim_reference').limit(1).execute()
        if claims_response.data:
            sample_claim = claims_response.data[0]['claim_reference']
            test_specific_claim_data(supabase, sample_claim)
        else:
            print("No claims found to test with")
            
        print("\n" + "="*60)
        print("TEST COMPLETED")
        print("="*60)
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 