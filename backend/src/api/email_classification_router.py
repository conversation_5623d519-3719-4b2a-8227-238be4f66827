"""
ZURICH EMAIL CLASSIFICATION ROUTER
==================================

Purpose: FastAPI router for n8n to classify emails and determine claim-related processing
Integration: Receives email data from n8n, processes using BAML, returns classification decision
Models: Uses OpenAI GPT-4o (primary) and GPT-4o-mini (fallback)

Endpoints: 
- POST /classify-email
- POST /classify-email/simple  
- GET /health
- GET /info

Usage in n8n:
- HTTP Request node
- POST to {{base_url}}/api/classify-email
- Include email subject, body, sender info, and attachments
- Response determines workflow routing decision
"""

import asyncio
import logging
import uuid
import time
from datetime import datetime
from typing import List, Optional, Dict, Any
import traceback
import re

from fastapi import APIRouter, HTTPException, Request, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel, EmailStr, validator, Field

# Import structured logging
from .structured_logger import StructuredLogger

# Import BAML client (auto-generated)
try:
    from baml_models.baml_client import b
    from baml_models.baml_client.types import EmailForClassification, EmailAttachment
except ImportError as e:
    logging.error(f"BAML client import failed: {e}")
    logging.error("Make sure to run 'baml-cli generate' in the baml_models directory")
    raise

# ================================================================================================
# LOGGING CONFIGURATION FOR DEBUGGING
# ================================================================================================

logger = logging.getLogger("ZurichEmailClassification")
structured_logger = StructuredLogger("email_classification")

# ================================================================================================
# ROUTER INITIALIZATION 
# ================================================================================================

router = APIRouter(
    prefix="/api",
    tags=["email-classification"],
    responses={
        404: {"description": "Not found"},
        500: {"description": "Internal server error"}
    }
)

# ================================================================================================
# REQUEST/RESPONSE MODELS FOR N8N INTEGRATION
# ================================================================================================

class AttachmentRequest(BaseModel):
    """Model for attachment data from n8n"""
    filename: str = Field(..., description="Name of the attachment file")
    contentType: str = Field(..., description="MIME type of the attachment")
    size: Optional[int] = Field(None, description="File size in bytes")
    
    @validator('filename')
    def validate_filename(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("Filename cannot be empty")
        return v.strip()
    
    @validator('contentType')
    def validate_content_type(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("Content type cannot be empty")
        return v.strip()

class EmailClassificationRequest(BaseModel):
    """Model for email classification request from n8n"""
    subject: str = Field(..., description="Email subject line")
    body: str = Field(..., description="Email body content")
    senderEmail: EmailStr = Field(..., description="Sender's email address")
    senderName: Optional[str] = Field(None, description="Sender's display name")
    receivedDate: Optional[str] = Field(None, description="Email received timestamp")
    attachments: List[AttachmentRequest] = Field(default_factory=list, description="Email attachments")
    
    @validator('subject')
    def validate_subject(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("Email subject cannot be empty")
        return v.strip()
    
    @validator('body')
    def validate_body(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("Email body cannot be empty")
        return v.strip()

class WorkflowActionResponse(BaseModel):
    """Simplified response model for n8n workflow routing"""
    workflowAction: str = Field(..., description="Primary workflow decision")
    isClaimRelated: bool = Field(..., description="Whether email is claim-related")
    claimType: str = Field(..., description="Type of claim if applicable")
    urgencyLevel: str = Field(..., description="Processing urgency")
    confidenceScore: float = Field(..., description="Classification confidence 0.0-1.0")
    attachmentIssue: bool = Field(..., description="Whether there are attachment problems")
    requiresHumanReview: bool = Field(..., description="Whether human review is needed")
    reasoning: str = Field(..., description="Brief explanation of decision")

class DetailedClassificationResponse(BaseModel):
    """Detailed response model with full classification results"""
    # Core workflow decision
    workflowAction: str
    isClaimRelated: bool
    
    # Classification details
    claimType: str
    urgencyLevel: str
    confidenceScore: float
    canadianJurisdiction: str
    
    # Attachment analysis
    attachmentAnalysis: Dict[str, Any]
    
    # Risk and indicators (if claim-related)
    riskAssessment: Optional[Dict[str, Any]]
    claimIndicators: Optional[Dict[str, Any]]
    
    # Human-readable explanations
    classificationReasoning: str
    recommendedNextSteps: List[str]
    flagsForHumanReview: List[str]
    
    # Metadata
    processingNotes: List[str]
    canadianLegalConsiderations: List[str]
    
    # Processing metadata
    processingTime: float
    modelUsed: str
    timestamp: str

class ErrorResponse(BaseModel):
    """Error response model"""
    error: str
    message: str
    timestamp: str
    requestId: Optional[str] = None

# ================================================================================================
# UTILITY FUNCTIONS FOR EMAIL PROCESSING
# ================================================================================================

def detect_attachment_mentions(email_body: str) -> bool:
    """Detect if email mentions attachments or documents"""
    attachment_patterns = [
        r'\battach(?:ed|ment|ing)\b',
        r'\bdocument(?:s)?\b',
        r'\bfile(?:s)?\b',
        r'\bphoto(?:s)?\b',
        r'\bimage(?:s)?\b',
        r'\breport(?:s)?\b',
        r'\bplease\s+(?:see|find|check)\b',
        r'\bsee\s+(?:attached|below|above)\b',
        r'\bfind\s+(?:attached|enclosed)\b',
        r'\benclosed\b',
        r'\bpdf\b',
        r'\bjpeg?\b',
        r'\bpng\b'
    ]
    
    email_lower = email_body.lower()
    return any(re.search(pattern, email_lower, re.IGNORECASE) for pattern in attachment_patterns)

def sanitize_email_content(content: str) -> str:
    """Clean and sanitize email content for processing"""
    if not content:
        return ""
    
    # Remove excessive whitespace
    content = re.sub(r'\s+', ' ', content.strip())
    
    # Remove email signatures and disclaimers (basic patterns)
    signature_patterns = [
        r'--\s*\n.*',  # Lines after "--"
        r'this\s+email\s+is\s+confidential.*',
        r'please\s+consider\s+the\s+environment.*',
        r'sent\s+from\s+my\s+iphone.*',
        r'get\s+outlook\s+for.*'
    ]
    
    for pattern in signature_patterns:
        content = re.sub(pattern, '', content, flags=re.IGNORECASE | re.DOTALL)
    
    return content.strip()

def create_baml_email_object(request: EmailClassificationRequest) -> EmailForClassification:
    """Convert request to BAML EmailForClassification object"""
    
    # Convert attachments to BAML format
    baml_attachments = []
    for attachment in request.attachments:
        baml_attachment = EmailAttachment(
            filename=attachment.filename,
            contentType=attachment.contentType,
            size=attachment.size or 0
        )
        baml_attachments.append(baml_attachment)
    
    # Create BAML email object
    email_obj = EmailForClassification(
        subject=sanitize_email_content(request.subject),
        body=sanitize_email_content(request.body),
        senderEmail=str(request.senderEmail),
        senderName=request.senderName or str(request.senderEmail).split('@')[0],
        receivedDate=request.receivedDate or datetime.now().isoformat(),
        attachments=baml_attachments
    )
    
    return email_obj

# ================================================================================================
# API ENDPOINTS
# ================================================================================================

@router.post("/classify-email", 
             response_model=DetailedClassificationResponse,
             summary="Classify email for claims processing",
             description="Analyze email content and attachments to determine if claim-related")
async def classify_email(request: EmailClassificationRequest):
    """
    Comprehensive email classification for Canadian insurance claims processing
    
    Returns detailed analysis including:
    - Workflow routing decision
    - Claim type and urgency assessment  
    - Risk analysis and indicators
    - Canadian jurisdictional considerations
    - Attachment analysis
    - Human review requirements
    """
    request_id = str(uuid.uuid4())
    start_time = time.time()
    
    # Log request start
    structured_logger.log_request_start(
        request_id, 
        "/api/classify-email", 
        len(request.body.encode('utf-8'))
    )
    
    try:
        logger.info(f"📧 Processing email classification request from {request.senderEmail}")
        logger.info(f"Subject: {request.subject[:100]}...")
        
        # Log data validation
        structured_logger.log_data_validation(request_id, "subject", "valid", request.subject[:50])
        structured_logger.log_data_validation(request_id, "sender_email", "valid", request.senderEmail)
        structured_logger.log_data_validation(request_id, "attachments_count", "valid", len(request.attachments))
        
        # Create BAML email object
        email_obj = create_baml_email_object(request)
        
        # Call BAML classification function
        logger.info("🤖 Calling BAML email classification...")
        ai_start_time = time.time()
        classification_result = b.ClassifyZurichEmail(email_obj)
        ai_end_time = time.time()
        
        # Log AI decision
        structured_logger.log_ai_decision(
            request_id,
            "gpt-4o",
            "email_classification",
            (ai_end_time - ai_start_time) * 1000,
            classification_result.confidenceScore
        )
        
        processing_time = (time.time() - start_time)
        
        # Build detailed response
        response = DetailedClassificationResponse(
            workflowAction=classification_result.workflowAction,
            isClaimRelated=classification_result.isClaimRelated,
            claimType=classification_result.claimType,
            urgencyLevel=classification_result.urgencyLevel,
            confidenceScore=classification_result.confidenceScore,
            canadianJurisdiction=classification_result.canadianJurisdiction,
            attachmentAnalysis={
                "hasAttachments": classification_result.attachmentAnalysis.hasAttachments,
                "attachmentCount": classification_result.attachmentAnalysis.attachmentCount,
                "attachments": [
                    {
                        "filename": att.filename,
                        "fileType": att.fileType,
                        "isClaimRelated": att.isClaimRelated,
                        "documentType": att.documentType
                    } for att in classification_result.attachmentAnalysis.attachments
                ],
                "documentTypes": [att.documentType for att in classification_result.attachmentAnalysis.attachments],
                "mentionsAttachments": classification_result.attachmentAnalysis.mentionsAttachments,
                "attachmentMismatch": classification_result.attachmentAnalysis.attachmentMismatch,
                "suspiciousFilenames": classification_result.attachmentAnalysis.suspiciousFilenames,
                "attachmentMentioned": detect_attachment_mentions(request.body)
            },
            riskAssessment={
                "liabilityRisk": classification_result.riskAssessment.liabilityRisk,
                "financialRisk": classification_result.riskAssessment.financialRisk,
                "reputationalRisk": classification_result.riskAssessment.reputationalRisk,
                "regulatoryRisk": classification_result.riskAssessment.regulatoryRisk,
                "overallRiskScore": classification_result.riskAssessment.overallRiskScore
            } if classification_result.riskAssessment else None,
            claimIndicators={
                "incidentKeywords": classification_result.claimIndicators.incidentKeywords,
                "damageKeywords": classification_result.claimIndicators.damageKeywords,
                "legalKeywords": classification_result.claimIndicators.legalKeywords,
                "medicalKeywords": classification_result.claimIndicators.medicalKeywords,
                "timeIndicators": classification_result.claimIndicators.timeIndicators,
                "locationIndicators": classification_result.claimIndicators.locationIndicators,
                "partyIndicators": classification_result.claimIndicators.partyIndicators
            } if classification_result.claimIndicators else None,
            classificationReasoning=classification_result.classificationReasoning,
            recommendedNextSteps=classification_result.recommendedNextSteps,
            flagsForHumanReview=classification_result.flagsForHumanReview,
            processingNotes=classification_result.processingNotes,
            canadianLegalConsiderations=classification_result.canadianLegalConsiderations,
            processingTime=processing_time,
            modelUsed="gpt-4o",
            timestamp=datetime.now().isoformat()
        )
        
        logger.info(f"✅ Email classification completed in {processing_time:.2f}s")
        logger.info(f"Decision: {response.workflowAction} | Claim-related: {response.isClaimRelated}")
        
        # Log request completion
        structured_logger.log_request_complete(
            request_id,
            processing_time * 1000,
            True,
            f"{response.workflowAction}|claim:{response.isClaimRelated}|confidence:{response.confidenceScore:.2f}"
        )
        
        return response
        
    except Exception as e:
        error_msg = f"Email classification failed: {str(e)}"
        logger.error(f"❌ {error_msg}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        
        # Log error
        structured_logger.log_error(
            request_id,
            "classification_error",
            error_msg,
            "email_classification"
        )
        
        # Log failed request completion
        processing_time = (time.time() - start_time)
        structured_logger.log_request_complete(
            request_id,
            processing_time * 1000,
            False,
            f"ERROR: {error_msg[:100]}"
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "classification_failed",
                "message": error_msg,
                "timestamp": datetime.now().isoformat()
            }
        )

@router.post("/classify-email/simple",
             response_model=WorkflowActionResponse,
             summary="Simple email classification for n8n routing",
             description="Simplified endpoint returning only essential workflow routing information")
async def classify_email_simple(request: EmailClassificationRequest):
    """
    Simplified email classification for n8n workflow routing
    
    Returns only essential fields needed for workflow decision making:
    - Primary workflow action
    - Basic classification flags
    - Confidence and urgency
    - Brief reasoning
    """
    request_id = str(uuid.uuid4())
    start_time = time.time()
    
    structured_logger.log_request_start(request_id, "/api/classify-email/simple", len(request.body.encode('utf-8')))
    
    try:
        logger.info(f"📧 Processing simple email classification from {request.senderEmail}")
        
        # Create BAML email object
        email_obj = create_baml_email_object(request)
        
        # Call BAML classification function  
        ai_start_time = time.time()
        classification_result = b.ClassifyZurichEmail(email_obj)
        ai_end_time = time.time()
        
        structured_logger.log_ai_decision(
            request_id, "gpt-4o", "email_classification_simple",
            (ai_end_time - ai_start_time) * 1000, classification_result.confidenceScore
        )
        
        processing_time = (time.time() - start_time)
        
        # Build simplified response
        response = WorkflowActionResponse(
            workflowAction=classification_result.workflowAction,
            isClaimRelated=classification_result.isClaimRelated,
            claimType=classification_result.claimType,
            urgencyLevel=classification_result.urgencyLevel,
            confidenceScore=classification_result.confidenceScore,
            attachmentIssue=not classification_result.attachmentAnalysis.hasAttachments and detect_attachment_mentions(request.body),
            requiresHumanReview=len(classification_result.flagsForHumanReview) > 0,
            reasoning=classification_result.classificationReasoning[:200] + "..." if len(classification_result.classificationReasoning) > 200 else classification_result.classificationReasoning
        )
        
        logger.info(f"✅ Simple classification completed in {processing_time:.2f}s")
        
        structured_logger.log_request_complete(
            request_id, processing_time * 1000, True,
            f"{response.workflowAction}|confidence:{response.confidenceScore:.2f}"
        )
        
        return response
        
    except Exception as e:
        error_msg = f"Simple email classification failed: {str(e)}"
        logger.error(f"❌ {error_msg}")
        
        structured_logger.log_error(request_id, "classification_error", error_msg, "email_classification_simple")
        structured_logger.log_request_complete(request_id, (time.time() - start_time) * 1000, False, f"ERROR: {error_msg[:100]}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "classification_failed", 
                "message": error_msg,
                "timestamp": datetime.now().isoformat()
            }
        )

@router.get("/health",
            summary="Health check endpoint",
            description="Check if the API and BAML client are working properly")
async def health_check():
    """Health check for email classification service"""
    try:
        # Test BAML client availability
        test_email = EmailForClassification(
            subject="Test",
            body="Test email for health check",
            senderEmail="<EMAIL>",
            senderName="Test User",
            receivedDate=datetime.now().isoformat(),
            attachments=[]
        )
        
        # Quick test call (this should be fast)
        # Note: In production, you might want a lighter health check
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "baml_client": "available",
            "service": "email-classification",
            "version": "1.0.0"
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "service": "email-classification"
            }
        )

@router.get("/info",
            summary="API information",
            description="Get API information and supported workflow actions")
async def api_info():
    """Get comprehensive API information"""
    return {
        "service": "Zurich Email Classification API",
        "version": "1.0.0",
        "description": "AI-powered email classification for Canadian insurance claims processing",
        "endpoints": {
            "/api/classify-email": {
                "method": "POST",
                "description": "Comprehensive email classification with detailed analysis",
                "response": "DetailedClassificationResponse"
            },
            "/api/classify-email/simple": {
                "method": "POST", 
                "description": "Simplified email classification for workflow routing",
                "response": "WorkflowActionResponse"
            },
            "/api/health": {
                "method": "GET",
                "description": "Health check endpoint"
            },
            "/api/info": {
                "method": "GET",
                "description": "API information and capabilities"
            }
        },
        "supported_workflow_actions": [
            "PROCEED_TO_ZENDESK",
            "IGNORE_EMAIL", 
            "REQUEST_ATTACHMENTS",
            "HUMAN_REVIEW_REQUIRED"
        ],
        "supported_claim_types": [
            "LIABILITY",
            "PROPERTY", 
            "AUTO",
            "WORKERS_COMPENSATION",
            "GENERAL_LIABILITY",
            "PROFESSIONAL_LIABILITY",
            "NOT_CLAIM_RELATED"
        ],
        "canadian_jurisdictions": [
            "ALBERTA", "BRITISH_COLUMBIA", "MANITOBA", "NEW_BRUNSWICK",
            "NEWFOUNDLAND_AND_LABRADOR", "NORTHWEST_TERRITORIES", "NOVA_SCOTIA",
            "NUNAVUT", "ONTARIO", "PRINCE_EDWARD_ISLAND", "QUEBEC", 
            "SASKATCHEWAN", "YUKON"
        ],
        "urgency_levels": ["LOW", "MEDIUM", "HIGH", "CRITICAL"],
        "models_used": ["gpt-4o", "gpt-4o-mini"],
        "timestamp": datetime.now().isoformat()
    } 