"""
ZURICH LEVEL 04 QUANTUM CALCULATION - FASTAPI ROUTER
Complete Canadian quantum (damages) calculation using provincial rules
Integrates with Level 1, 2, and 3 analyses from Supabase
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any, Tuple
import logging
import json
import time
import uuid
from datetime import datetime
import os
import sys

# Import structured logging
from .structured_logger import StructuredLogger

# Canadian liability engine import

from .canadian_liability_engine import (
    CanadianMedicalCosts,
    Province
)
from supabase import create_client, Client

# Initialize router and logging first
router = APIRouter(prefix="/api/level04-quantum", tags=["Level 04 - Quantum Calculation"])
logger = logging.getLogger(__name__)
structured_logger = StructuredLogger("level04_quantum_calculation")

# Import BAML client for AI-powered extraction
try:
    # Try relative import first (works in Docker)
    import sys
    import os
    
    # Add the baml_models directory to Python path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
    baml_path = os.path.join(project_root, 'baml_models')
    if baml_path not in sys.path:
        sys.path.append(baml_path)
    
    from baml_client import b
    logger.info("BAML client imported successfully")
except ImportError as e:
    logger.error(f"Failed to import BAML client: {e}")
    b = None

# Supabase client
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY") or os.getenv("SUPABASE_ANON_KEY")
supabase: Optional[Client] = None

if supabase_url and supabase_key:
    supabase = create_client(supabase_url, supabase_key)
    logger.info("Supabase connected for Level 04")


# ==================== REQUEST/RESPONSE MODELS ====================

# Simplified request model for AI-powered extraction
class Level04SimpleRequest(BaseModel):
    """Simplified Level 04 Quantum Calculation Request - AI extracts all details"""
    claim_reference: str = Field(..., description="Unique claim identifier")
    province: Optional[str] = Field("Ontario", description="Province for quantum calculation")

# Legacy detailed request model (kept for backward compatibility)
class InjuryDetail(BaseModel):
    """Detailed injury information"""
    injury_type: str  # soft_tissue, fracture, head_injury, spinal
    severity: str  # minor, moderate, severe, catastrophic
    body_part: str
    treatment_status: str = "ongoing"
    permanent_impairment: bool = False
    disability_rating: Optional[float] = None  # 0-100%

class TreatmentPlan(BaseModel):
    """Treatment plan details"""
    physiotherapy_sessions: int = 0
    chiropractic_sessions: int = 0
    massage_therapy_sessions: int = 0
    psychological_sessions: int = 0
    surgery_required: bool = False
    hospitalization_days: int = 0
    medication_monthly_cost: float = 0
    medical_equipment_cost: float = 0

class IncomeInformation(BaseModel):
    """Income and employment information"""
    employment_status: str  # employed, self-employed, unemployed, retired
    annual_income: float = 0
    weekly_income: float = 0
    weeks_off_work: int = 0
    partial_disability_weeks: int = 0
    return_to_work_capacity: float = 1.0  # 0-1 (percentage of capacity)

class Level04Request(BaseModel):
    """Level 04 Quantum Calculation Request (Legacy detailed format)"""
    claim_reference: str = Field(..., description="Unique claim identifier")
    province: Optional[str] = Field("Ontario", description="Province for calculation")
    
    # Injury details
    injuries: List[InjuryDetail] = Field(..., description="List of injuries sustained")
    treatment_plan: TreatmentPlan = Field(..., description="Required treatment plan")
    income_info: Optional[IncomeInformation] = None
    
    # Fault from Level 3
    claimant_fault_percentage: Optional[int] = Field(
        None, 
        description="Claimant's fault percentage from Level 3 (0-100)"
    )
    
    # Optional override data
    level03_fault: Optional[Dict[str, Any]] = None
    
    # Special damages
    out_of_pocket_expenses: float = Field(0, description="Already incurred expenses")
    property_damage: float = Field(0, description="Property damage amount")
    
    # Future considerations
    life_expectancy_years: Optional[int] = Field(None, description="For future care calculations")
    pre_existing_conditions: List[str] = Field(default_factory=list)

class CostBreakdown(BaseModel):
    """Detailed cost breakdown"""
    medical_costs: float
    rehabilitation_costs: float
    medication_costs: float
    equipment_costs: float
    hospitalization_costs: float
    future_medical_costs: float
    total_medical: float

class IncomeBreakdown(BaseModel):
    """Income loss breakdown"""
    past_income_loss: float
    future_income_loss: float
    loss_of_earning_capacity: float
    pension_loss: float
    benefits_loss: float
    total_income_loss: float

class QuantumBreakdown(BaseModel):
    """Complete quantum breakdown"""
    # Special damages (pecuniary)
    cost_breakdown: CostBreakdown
    income_breakdown: IncomeBreakdown
    out_of_pocket: float
    property_damage: float
    total_special_damages: float
    
    # General damages (non-pecuniary)
    pain_and_suffering: float
    loss_of_amenities: float
    loss_of_expectation_of_life: float
    total_general_damages: float
    
    # Totals
    total_damages: float
    
    # Adjustments
    fault_reduction_percentage: float
    fault_reduction_amount: float
    pre_existing_reduction: float
    
    # Final amounts
    net_recoverable: float
    
    # Additional info
    calculation_notes: List[str]

class Level04Response(BaseModel):
    """Level 04 Quantum Calculation Response"""
    claim_reference: str
    processing_timestamp: datetime
    province: str
    
    # Quantum breakdown
    quantum_breakdown: QuantumBreakdown
    
    # Policy considerations
    within_policy_limits: bool
    policy_limit: float = 1000000  # Default CGL limit
    excess_amount: float = 0
    
    # Settlement recommendations
    settlement_range_low: float
    settlement_range_high: float
    recommended_settlement: float
    
    # Reserve recommendations
    initial_reserve: float
    recommended_reserve: float
    
    # Confidence and notes
    calculation_confidence: float = Field(..., ge=0, le=1)
    assumptions_made: List[str]
    data_gaps: List[str]
    
    # Next steps
    proceed_to_settlement: bool
    requires_medical_review: bool
    requires_actuarial_review: bool
    exit_path: str  # SETTLE, NEGOTIATE, MEDIATE, LITIGATE


# ==================== HELPER FUNCTIONS ====================

async def fetch_comprehensive_claim_data_l4(claim_reference: str) -> Dict[str, Any]:
    """Fetch comprehensive claim data from Supabase for Level 4 quantum calculation"""
    if not supabase:
        logger.warning(f"Supabase not available for claim {claim_reference}")
        return _create_minimal_fallback_l4(claim_reference)
    
    try:
        # Fetch claim details including all level analyses
        claims_response = None
        try:
            # First try with claim_id
            claims_response = supabase.table('claims')\
                .select('*, 01_level_analysis, 02_level_analysis, 03_level_analysis')\
                .eq('claim_id', claim_reference)\
                .execute()
        except Exception as e1:
            try:
                # Try with claim_reference
                claims_response = supabase.table('claims')\
                    .select('*, 01_level_analysis, 02_level_analysis, 03_level_analysis')\
                    .eq('claim_reference', claim_reference)\
                    .execute()
            except Exception as e2:
                try:
                    # Try with id
                    claims_response = supabase.table('claims')\
                        .select('*, 01_level_analysis, 02_level_analysis, 03_level_analysis')\
                        .eq('id', claim_reference)\
                        .execute()
                except Exception as e3:
                    # If all attempts fail, use minimal fallback
                    logger.warning(f"Database access not available. Using minimal fallback for claim {claim_reference}")
                    logger.debug(f"Errors: claim_id={e1}, claim_reference={e2}, id={e3}")
                    return _create_minimal_fallback_l4(claim_reference)
        
        # Fetch attachments and OCR text using claim_reference
        attachments_response = None
        try:
            attachments_response = supabase.table('attachments')\
                .select('*')\
                .eq('claim_reference', claim_reference)\
                .execute()
        except Exception as e:
            logger.warning(f"Could not fetch attachments for claim {claim_reference}: {str(e)}")
            attachments_response = None
        
        claim_data = claims_response.data[0] if claims_response and claims_response.data else {}
        attachments_data = attachments_response.data if attachments_response and attachments_response.data else []
        
        # Parse all level analyses
        level01_analysis = None
        level02_analysis = None
        level03_analysis = None
        
        if claim_data.get('01_level_analysis'):
            try:
                if isinstance(claim_data['01_level_analysis'], str):
                    level01_analysis = json.loads(claim_data['01_level_analysis'])
                else:
                    level01_analysis = claim_data['01_level_analysis']
                logger.info(f"Found Level 1 analysis for claim {claim_reference}")
            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"Failed to parse Level 1 analysis for claim {claim_reference}: {str(e)}")
        
        if claim_data.get('02_level_analysis'):
            try:
                if isinstance(claim_data['02_level_analysis'], str):
                    level02_analysis = json.loads(claim_data['02_level_analysis'])
                else:
                    level02_analysis = claim_data['02_level_analysis']
                logger.info(f"Found Level 2 analysis for claim {claim_reference}")
            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"Failed to parse Level 2 analysis for claim {claim_reference}: {str(e)}")
        
        if claim_data.get('03_level_analysis'):
            try:
                if isinstance(claim_data['03_level_analysis'], str):
                    level03_analysis = json.loads(claim_data['03_level_analysis'])
                else:
                    level03_analysis = claim_data['03_level_analysis']
                logger.info(f"Found Level 3 analysis for claim {claim_reference}")
            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"Failed to parse Level 3 analysis for claim {claim_reference}: {str(e)}")
        
        return {
            "claim_details": claim_data,
            "level01_analysis": level01_analysis,
            "level02_analysis": level02_analysis,
            "level03_analysis": level03_analysis,
            "attachments": attachments_data,
            "ocr_texts": [att.get('ocr_text', '') for att in attachments_data if att.get('ocr_text')],
            "documents": [att.get('file_name', '') for att in attachments_data],
            "fetched_from_supabase": True
        }
        
    except Exception as e:
        logger.error(f"Failed to fetch from Supabase: {str(e)}")
        return _create_minimal_fallback_l4(claim_reference)

def _create_minimal_fallback_l4(claim_reference: str) -> Dict[str, Any]:
    """Create minimal fallback when Supabase is unavailable - returns empty structure"""
    logger.warning(f"Supabase unavailable for claim {claim_reference} - returning empty structure")
    
    return {
        "claim_details": {"claim_reference": claim_reference},
        "level01_analysis": None,
        "level02_analysis": None,
        "level03_analysis": None,
        "attachments": [],
        "ocr_texts": [],
        "documents": [],
        "fetched_from_supabase": False,
        "fallback_data": True
    }

def extract_email_content_l4(supabase_data: Dict[str, Any], level01_analysis: Dict[str, Any] = None) -> List[str]:
    """Extract email content from various sources for Level 4 analysis"""
    email_content = []
    
    # Try to get email content from Level 1 analysis
    if level01_analysis:
        # Check for email content in various Level 1 fields
        claim_details = level01_analysis.get("claimDetails", {})
        if claim_details.get("emailBody"):
            email_content.append(claim_details["emailBody"])
        if claim_details.get("emailSubject"):
            email_content.append(f"Subject: {claim_details['emailSubject']}")
        
        # Check for email content in cause of loss
        cause_of_loss = level01_analysis.get("causeOfLoss", {})
        if cause_of_loss.get("circumstances"):
            email_content.append(cause_of_loss["circumstances"])
    
    # Try to get email content from claim details
    claim_details = supabase_data.get("claim_details", {})
    if claim_details.get("email_body"):
        email_content.append(claim_details["email_body"])
    if claim_details.get("email_subject"):
        email_content.append(f"Subject: {claim_details['email_subject']}")
    
    # Add any communication from attachments that might be emails
    for attachment in supabase_data.get("attachments", []):
        if attachment.get("file_name", "").lower().endswith(('.eml', '.msg')):
            if attachment.get("ocr_text"):
                email_content.append(f"Email file content: {attachment['ocr_text']}")
    
    return [content for content in email_content if content and len(content.strip()) > 10]

async def fetch_prior_analyses(claim_reference: str) -> Dict[str, Any]:
    """Fetch all prior level analyses from Supabase (legacy function for backward compatibility)"""
    if not supabase:
        logger.warning(f"Supabase not available for claim {claim_reference}")
        return {}
    
    try:
        response = supabase.table('claims')\
            .select('*, 01_level_analysis, 02_level_analysis, 03_level_analysis')\
            .eq('claim_reference', claim_reference)\
            .single()\
            .execute()
        
        return response.data if response.data else {}
    
    except Exception as e:
        logger.error(f"Error fetching analyses: {str(e)}")
        return {}


def extract_injuries_from_level01(level01: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Extract injury information from Level 1 analysis"""
    injuries = []
    
    # Parse from claim description and injury details
    injury_details = level01.get("injuryDetails", [])
    claim_desc = level01.get("claimDescription", "").lower()
    
    # If no structured injury details, infer from description
    if not injury_details:
        if "whiplash" in claim_desc or "neck" in claim_desc:
            injuries.append({
                "injury_type": "soft_tissue",
                "severity": "moderate",
                "body_part": "neck"
            })
        if "back" in claim_desc or "spine" in claim_desc:
            injuries.append({
                "injury_type": "soft_tissue",
                "severity": "moderate",
                "body_part": "back"
            })
        if "fracture" in claim_desc or "broken" in claim_desc:
            injuries.append({
                "injury_type": "fracture",
                "severity": "moderate",
                "body_part": "unknown"
            })
        if "concussion" in claim_desc or "head" in claim_desc:
            injuries.append({
                "injury_type": "head_injury",
                "severity": "moderate",
                "body_part": "head"
            })
    
    return injuries


def calculate_special_damages(
    injuries: List[InjuryDetail],
    treatment_plan: TreatmentPlan,
    income_info: Optional[IncomeInformation],
    province: Province,
    out_of_pocket: float,
    property_damage: float
) -> Tuple[CostBreakdown, IncomeBreakdown, float]:
    """Calculate all special (pecuniary) damages"""
    
    # Initialize medical cost calculator
    medical_calc = CanadianMedicalCosts()
    
    # Convert injuries to format expected by calculator
    injury_list = [
        {
            "type": inj.injury_type,
            "severity": inj.severity,
            "permanent_impairment": inj.permanent_impairment
        }
        for inj in injuries
    ]
    
    # Convert treatment plan to dictionary
    treatment_dict = {
        "physiotherapy": treatment_plan.physiotherapy_sessions,
        "chiropractic": treatment_plan.chiropractic_sessions,
        "massage_therapy": treatment_plan.massage_therapy_sessions,
        "psychological": treatment_plan.psychological_sessions
    }
    
    # Convert income info to dictionary
    income_dict = None
    if income_info:
        income_dict = {
            "weekly_income": income_info.weekly_income,
            "weeks_off_work": income_info.weeks_off_work
        }
    
    # Calculate medical costs
    cost_result = medical_calc.calculate_total_costs(
        injury_list,
        province,
        treatment_dict,
        income_dict
    )
    
    # Build cost breakdown
    cost_breakdown = CostBreakdown(
        medical_costs=cost_result["medical_costs"],
        rehabilitation_costs=cost_result["rehabilitation_costs"],
        medication_costs=treatment_plan.medication_monthly_cost * 12,  # Annual
        equipment_costs=treatment_plan.medical_equipment_cost,
        hospitalization_costs=treatment_plan.hospitalization_days * 1500,  # Daily rate
        future_medical_costs=cost_result["future_care_allowance"],
        total_medical=0  # Will calculate
    )
    
    cost_breakdown.total_medical = sum([
        cost_breakdown.medical_costs,
        cost_breakdown.rehabilitation_costs,
        cost_breakdown.medication_costs,
        cost_breakdown.equipment_costs,
        cost_breakdown.hospitalization_costs,
        cost_breakdown.future_medical_costs
    ])
    
    # Build income breakdown
    past_income_loss = cost_result.get("income_replacement", 0)
    
    # Calculate future income loss based on disability
    future_income_loss = 0
    if income_info and any(inj.permanent_impairment for inj in injuries):
        # Simplified calculation - would need actuarial tables
        disability_impact = max(inj.disability_rating or 0 for inj in injuries if inj.permanent_impairment) / 100
        years_to_retirement = max(0, 65 - (income_info.annual_income / income_info.weekly_income / 52))
        future_income_loss = income_info.annual_income * disability_impact * years_to_retirement * 0.7  # Present value factor
    
    income_breakdown = IncomeBreakdown(
        past_income_loss=past_income_loss,
        future_income_loss=future_income_loss,
        loss_of_earning_capacity=future_income_loss * 0.1,  # 10% additional
        pension_loss=future_income_loss * 0.15,  # CPP impact
        benefits_loss=income_info.weeks_off_work * 200 if income_info else 0,  # Estimated benefits
        total_income_loss=0  # Will calculate
    )
    
    income_breakdown.total_income_loss = sum([
        income_breakdown.past_income_loss,
        income_breakdown.future_income_loss,
        income_breakdown.loss_of_earning_capacity,
        income_breakdown.pension_loss,
        income_breakdown.benefits_loss
    ])
    
    # Total special damages
    total_special = (
        cost_breakdown.total_medical +
        income_breakdown.total_income_loss +
        out_of_pocket +
        property_damage
    )
    
    return cost_breakdown, income_breakdown, total_special


def calculate_general_damages(injuries: List[InjuryDetail]) -> Tuple[float, float, float, float]:
    """Calculate general (non-pecuniary) damages based on Canadian cap"""
    
    # 2024 Supreme Court cap approximately $400,000
    cap = 400000
    
    # Determine severity multiplier
    max_severity = "minor"
    for injury in injuries:
        if injury.severity == "catastrophic":
            max_severity = "catastrophic"
            break
        elif injury.severity == "severe":
            max_severity = "severe"
        elif injury.severity == "moderate" and max_severity != "severe":
            max_severity = "moderate"
    
    # Base pain and suffering calculation
    severity_multipliers = {
        "minor": 0.05,      # $20,000
        "moderate": 0.15,   # $60,000
        "severe": 0.40,     # $160,000
        "catastrophic": 1.0 # $400,000 (cap)
    }
    
    pain_suffering = cap * severity_multipliers.get(max_severity, 0.05)
    
    # Loss of amenities (reduced quality of life)
    amenities_multiplier = 0.2 if any(inj.permanent_impairment for inj in injuries) else 0.1
    loss_amenities = pain_suffering * amenities_multiplier
    
    # Loss of expectation of life (if applicable)
    loss_life_expectancy = 0
    if max_severity == "catastrophic":
        loss_life_expectancy = 50000  # Fixed amount for shortened life expectancy
    
    total_general = pain_suffering + loss_amenities + loss_life_expectancy
    
    return pain_suffering, loss_amenities, loss_life_expectancy, total_general


def apply_reductions(
    total_damages: float,
    fault_percentage: int,
    pre_existing: List[str]
) -> Tuple[float, float, float]:
    """Apply fault and pre-existing condition reductions"""
    
    # Fault reduction
    fault_reduction = total_damages * (fault_percentage / 100)
    
    # Pre-existing condition reduction (simplified)
    pre_existing_factor = min(len(pre_existing) * 0.05, 0.25)  # Max 25% reduction
    pre_existing_reduction = total_damages * pre_existing_factor
    
    # Net recoverable
    net_recoverable = total_damages - fault_reduction - pre_existing_reduction
    
    return fault_reduction, pre_existing_reduction, max(0, net_recoverable)


# ==================== API ENDPOINTS ====================

@router.post("/calculate-ai", response_model=Level04Response)
async def calculate_quantum_ai(request: Level04SimpleRequest):
    """
    AI-Powered Level 04 Quantum Calculation
    
    This endpoint:
    1. Fetches comprehensive data from Supabase (L1, L2, L3, OCR, emails)
    2. Uses AI to extract detailed damage information
    3. Calculates quantum using Canadian provincial rules
    4. Provides settlement recommendations and exit paths
    
    Simple request format - only requires claim_reference and province.
    """
    request_id = str(uuid.uuid4())
    start_time = time.time()
    
    # Log request start
    structured_logger.log_request_start(
        request_id, 
        "/api/level04-quantum/calculate-ai", 
        len(str(request.dict()).encode('utf-8'))
    )
    
    try:
        # Check if BAML client is available
        if b is None:
            raise HTTPException(
                status_code=500, 
                detail="AI service unavailable - BAML client not properly initialized"
            )
        
        logger.info(f"Processing AI-powered Level 04 quantum calculation for claim {request.claim_reference}")
        
        # Log data validation
        structured_logger.log_data_validation(request_id, "claim_reference", "valid", request.claim_reference)
        structured_logger.log_data_validation(request_id, "province", "valid", request.province)
        
        # Fetch comprehensive data from Supabase
        supabase_data = await fetch_comprehensive_claim_data_l4(request.claim_reference)
        
        # Extract data sources for AI analysis
        level01_analysis = supabase_data.get("level01_analysis", {})
        level02_analysis = supabase_data.get("level02_analysis", {})
        level03_analysis = supabase_data.get("level03_analysis", {})
        
        # Get email content
        email_content = extract_email_content_l4(supabase_data, level01_analysis)
        
        # Extract SparkNLP insights from Level 1
        spark_nlp_insights = ""
        if level01_analysis and level01_analysis.get("sparkNlpInsights"):
            spark_nlp_insights = json.dumps(level01_analysis["sparkNlpInsights"])
        
        # Get OCR texts and attachment details
        ocr_texts = supabase_data.get("ocr_texts", [])
        attachment_details = [f"{att.get('file_name', 'Unknown')}: {att.get('file_type', 'Unknown type')}" 
                             for att in supabase_data.get("attachments", [])]
        
        logger.info(f"Comprehensive data loaded - L1: {'✓' if level01_analysis else '✗'}, "
                   f"L2: {'✓' if level02_analysis else '✗'}, L3: {'✓' if level03_analysis else '✗'}, "
                   f"OCR: {len(ocr_texts)}, Email: {len(email_content)}")
        
        # Prepare BAML input for AI extraction
        try:
            from baml_client.types import Level04AnalysisInput
        except ImportError:
            raise HTTPException(
                status_code=500, 
                detail="AI service unavailable - BAML types not accessible"
            )
        
        ai_input = Level04AnalysisInput(
            claimReference=request.claim_reference,
            province=request.province,
            level01Analysis=json.dumps(level01_analysis) if level01_analysis else "No Level 1 analysis available",
            level02Coverage=json.dumps(level02_analysis) if level02_analysis else "No Level 2 coverage analysis available",
            level03Fault=json.dumps(level03_analysis) if level03_analysis else "No Level 3 fault determination available",
            emailContent=email_content if email_content else ["No email content available"],
            sparkNlpInsights=spark_nlp_insights if spark_nlp_insights else "No SparkNLP insights available",
            ocrTexts=ocr_texts if ocr_texts else ["No OCR text available"],
            attachmentDetails=attachment_details if attachment_details else ["No attachments available"]
        )
        
        # Execute AI extraction
        logger.info(f"Executing AI quantum damage extraction for claim {request.claim_reference}")
        
        try:
            ai_extraction = b.ExtractLevel04QuantumDetails(ai_input)
            logger.info(f"AI extraction completed for claim {request.claim_reference}")
        except Exception as ai_error:
            # Enhanced error capture for BAML validation errors
            error_details = {
                "error_type": type(ai_error).__name__,
                "error_message": str(ai_error),
                "error_args": getattr(ai_error, 'args', []),
                "claim_reference": request.claim_reference
            }
            
            # Special handling for BAML/Pydantic validation errors
            if hasattr(ai_error, 'errors'):
                error_details["validation_errors"] = ai_error.errors()
            
            logger.error(f"AI extraction failed for claim {request.claim_reference}: {error_details}")
            
            # Create a more descriptive error message
            if hasattr(ai_error, 'errors') and ai_error.errors():
                validation_summary = []
                for err in ai_error.errors()[:3]:  # Show first 3 errors
                    field = ".".join(str(loc) for loc in err.get('loc', []))
                    msg = err.get('msg', 'Unknown validation error')
                    validation_summary.append(f"{field}: {msg}")
                detail = f"AI validation failed - {'; '.join(validation_summary)}"
            else:
                detail = f"AI extraction failed: {error_details['error_type']} - {error_details['error_message'] or 'Unknown error'}"
                
            raise HTTPException(status_code=500, detail=detail)
        
        # Convert AI extraction to traditional quantum breakdown format
        try:
            quantum_breakdown = convert_ai_extraction_to_quantum(ai_extraction, request.province)
        except Exception as conversion_error:
            logger.error(f"AI extraction conversion failed for claim {request.claim_reference}: {str(conversion_error)}")
            raise HTTPException(
                status_code=500, 
                detail=f"Quantum calculation conversion failed: {str(conversion_error)}"
            )
        
        # RESILIENT: Get fault percentage from Level 3 or AI analysis with safe access
        fault_percentage = 0
        try:
            if level03_analysis and level03_analysis.get("faultAllocation"):
                fault_allocation = level03_analysis["faultAllocation"]
                fault_percentage = fault_allocation.get("claimant", 0)
            elif hasattr(ai_extraction, 'faultImpactAnalysis') and ai_extraction.faultImpactAnalysis:
                fault_percentage = getattr(ai_extraction.faultImpactAnalysis, 'claimantFaultPercentage', 0)
        except (AttributeError, KeyError, TypeError) as e:
            logger.warning(f"Could not extract fault percentage for claim {request.claim_reference}: {e}. Using default 0%")
            fault_percentage = 0
        
        # RESILIENT: Determine exit path with safe attribute access
        settlement_strategy = "negotiate"  # Safe default
        net_recoverable = 0  # Safe default
        litigation_risk = "moderate"  # Safe default
        
        try:
            if hasattr(ai_extraction, 'quantumGuidance') and ai_extraction.quantumGuidance:
                settlement_strategy = getattr(ai_extraction.quantumGuidance, 'settlementStrategy', 'negotiate')
                net_recoverable = getattr(ai_extraction.quantumGuidance, 'netDamagesTotal', 0)
                litigation_risk = getattr(ai_extraction.quantumGuidance, 'litigationRisk', 'moderate')
        except (AttributeError, TypeError) as e:
            logger.warning(f"Could not extract quantum guidance for claim {request.claim_reference}: {e}. Using safe defaults")
        
        if settlement_strategy == "settle_fast" or net_recoverable <= 25000:
            exit_path = "SETTLE"
            proceed_to_settlement = True
        elif settlement_strategy == "negotiate" or litigation_risk in ["low", "moderate"]:
            exit_path = "NEGOTIATE"
            proceed_to_settlement = True
        elif settlement_strategy == "mediate":
            exit_path = "MEDIATE"
            proceed_to_settlement = False
        else:  # litigation or high risk
            exit_path = "LITIGATE"
            proceed_to_settlement = False
        
        # RESILIENT: Medical/actuarial review flags with safe access
        requires_medical = False
        requires_actuarial = False
        
        try:
            if hasattr(ai_extraction, 'quantumGuidance') and ai_extraction.quantumGuidance:
                uncertainty_areas = getattr(ai_extraction.quantumGuidance, 'uncertaintyAreas', [])
                if uncertainty_areas:
                    requires_medical = any("medical" in str(area).lower() for area in uncertainty_areas)
                
                expert_reports = getattr(ai_extraction.quantumGuidance, 'recommendedExpertReports', [])
                if expert_reports:
                    requires_actuarial = "actuarial" in str(expert_reports).lower()
                
                # Also check net recoverable amount
                if net_recoverable > 500000:
                    requires_actuarial = True
        except (AttributeError, TypeError) as e:
            logger.warning(f"Could not extract review requirements for claim {request.claim_reference}: {e}. Using safe defaults")
        
        # Policy limit considerations
        policy_limit = 1000000  # Default CGL limit
        within_policy_limits = net_recoverable <= policy_limit
        excess_amount = max(0, net_recoverable - policy_limit)
        
        # RESILIENT: Settlement range parsing with fallbacks
        settlement_low = max(net_recoverable * 0.8, 1000)  # Minimum $1,000
        settlement_high = max(net_recoverable * 1.2, 5000)  # Minimum $5,000
        
        try:
            if hasattr(ai_extraction, 'quantumGuidance') and ai_extraction.quantumGuidance:
                settlement_range = getattr(ai_extraction.quantumGuidance, 'settlementRange', '')
                if settlement_range and isinstance(settlement_range, str):
                    # Parse settlement range (e.g., "$10,000 to $25,000")
                    settlement_parts = settlement_range.replace("$", "").replace(",", "").split(" to ")
                    if len(settlement_parts) == 2:
                        try:
                            settlement_low = float(settlement_parts[0])
                            settlement_high = float(settlement_parts[1])
                        except (ValueError, TypeError):
                            logger.warning(f"Could not parse settlement range '{settlement_range}' for claim {request.claim_reference}")
        except (AttributeError, TypeError) as e:
            logger.warning(f"Could not extract settlement range for claim {request.claim_reference}: {e}. Using calculated defaults")
        
        recommended_settlement = (settlement_low + settlement_high) / 2
        
        # Reserve recommendations
        initial_reserve = net_recoverable * 0.7  # Conservative initial reserve
        recommended_reserve = net_recoverable  # Full quantum as recommended reserve
        
        # RESILIENT: Store result in Supabase with safe serialization
        if supabase:
            try:
                # Safe extraction of confidence with fallback
                confidence = 0.5  # Default confidence
                try:
                    if hasattr(ai_extraction, 'quantumGuidance') and ai_extraction.quantumGuidance:
                        confidence = getattr(ai_extraction.quantumGuidance, 'quantumConfidence', 0.5)
                except (AttributeError, TypeError):
                    pass
                
                result_data = {
                    "claim_reference": request.claim_reference,
                    "04_level_analysis": {
                        "ai_extraction": ai_extraction.dict() if hasattr(ai_extraction, 'dict') else str(ai_extraction),
                        "quantum_breakdown": quantum_breakdown.dict() if hasattr(quantum_breakdown, 'dict') else str(quantum_breakdown),
                        "settlement_recommendation": recommended_settlement,
                        "exit_path": exit_path,
                        "confidence": confidence,
                        "timestamp": datetime.utcnow().isoformat()
                    }
                }
                
                supabase.table('claims').update(result_data).eq('claim_reference', request.claim_reference).execute()
                logger.info(f"AI-powered Level 04 analysis stored for claim {request.claim_reference}")
                
            except Exception as e:
                logger.error(f"Failed to store Level 04 analysis: {str(e)}")
        
        # Build response
        response = Level04Response(
            claim_reference=request.claim_reference,
            processing_timestamp=datetime.utcnow(),
            province=request.province,
            quantum_breakdown=quantum_breakdown,
            within_policy_limits=within_policy_limits,
            policy_limit=policy_limit,
            excess_amount=excess_amount,
            settlement_range_low=settlement_low,
            settlement_range_high=settlement_high,
            recommended_settlement=recommended_settlement,
            initial_reserve=initial_reserve,
            recommended_reserve=recommended_reserve,
            calculation_confidence=getattr(ai_extraction.quantumGuidance, 'quantumConfidence', 0.5) if hasattr(ai_extraction, 'quantumGuidance') and ai_extraction.quantumGuidance else 0.5,
            assumptions_made=getattr(ai_extraction.quantumGuidance, 'uncertaintyAreas', ["Incomplete data available"]) if hasattr(ai_extraction, 'quantumGuidance') and ai_extraction.quantumGuidance else ["Incomplete data available"],
            data_gaps=[area for area in (getattr(ai_extraction.quantumGuidance, 'uncertaintyAreas', []) if hasattr(ai_extraction, 'quantumGuidance') and ai_extraction.quantumGuidance else []) if "missing" in str(area).lower()],
            proceed_to_settlement=proceed_to_settlement,
            requires_medical_review=requires_medical,
            requires_actuarial_review=requires_actuarial,
            exit_path=exit_path
        )
        
        # Log successful completion
        processing_time = (time.time() - start_time) * 1000  # Convert to milliseconds
        structured_logger.log_request_complete(request_id, processing_time, True, f"Exit path: {exit_path}, Net recoverable: ${net_recoverable:,.2f}")
        
        logger.info(f"AI-powered Level 04 quantum calculation completed for claim {request.claim_reference} "
                   f"in {processing_time/1000:.2f}s - Exit path: {exit_path}, Net recoverable: ${net_recoverable:,.2f}")
        
        return response
        
    except Exception as e:
        # Enhanced error capture
        processing_time = (time.time() - start_time) * 1000  # Convert to milliseconds
        
        error_details = {
            "error_type": type(e).__name__,
            "error_message": str(e),
            "claim_reference": request.claim_reference,
            "processing_time_ms": processing_time
        }
        
        # Extract detailed error information
        if hasattr(e, 'errors'):
            error_details["validation_errors"] = e.errors()
        if hasattr(e, 'args'):
            error_details["error_args"] = e.args
            
        error_msg = f"Level 04 AI quantum calculation failed: {error_details['error_type']} - {error_details['error_message'] or 'Unknown error'}"
        logger.error(f"{error_msg} for claim {request.claim_reference}. Details: {error_details}")
        structured_logger.log_request_complete(request_id, processing_time, False, error_msg)
        
        raise HTTPException(status_code=500, detail=error_msg)

def validate_ontario_quantum_amounts(ai_extraction, province: str) -> Dict[str, Any]:
    """RESILIENT: Validate quantum amounts against Ontario benchmarks with safe access"""
    validation_results = {
        "overvaluation_risk": "low",
        "adjustments_needed": [],
        "warnings": [],
        "ontario_benchmark_comparison": {}
    }
    
    # RESILIENT: Pain and suffering validation for Ontario with safe access
    if province.lower() == "ontario":
        try:
            general_damages = getattr(ai_extraction, 'generalDamages', None)
            if not general_damages:
                validation_results["warnings"].append("No general damages data available for validation")
                return validation_results
                
            pain_suffering = getattr(general_damages, 'recommendedGeneralDamages', 0)
            if pain_suffering == 0:
                validation_results["warnings"].append("No pain and suffering amount available for validation")
                return validation_results
        except (AttributeError, TypeError) as e:
            validation_results["warnings"].append(f"Could not access general damages data: {e}")
            return validation_results
        
        # Ontario benchmark ranges (based on user analysis)
        ontario_benchmarks = {
            "minor": {"min": 5000, "max": 15000, "description": "Minor soft tissue injuries"},
            "moderate": {"min": 15000, "max": 45000, "description": "Moderate injuries with some impairment"},
            "severe": {"min": 45000, "max": 150000, "description": "Severe injuries with significant impairment"},
            "catastrophic": {"min": 150000, "max": 420000, "description": "Catastrophic injuries (Andrews cap)"},
            "slip_fall_typical": {"min": 10000, "max": 50000, "description": "Typical slip-fall moderate cases"}
        }
        
        # Determine appropriate benchmark category
        benchmark_category = getattr(general_damages, 'ontarioBenchmarkCategory', 'moderate')
        if benchmark_category not in ontario_benchmarks:
            benchmark_category = "moderate"  # Default to moderate
        
        benchmark = ontario_benchmarks[benchmark_category]
        validation_results["ontario_benchmark_comparison"] = {
            "category": benchmark_category,
            "ai_amount": pain_suffering,
            "benchmark_min": benchmark["min"],
            "benchmark_max": benchmark["max"],
            "benchmark_description": benchmark["description"]
        }
        
        # Check for overvaluation
        if pain_suffering > benchmark["max"]:
            overvaluation_factor = pain_suffering / benchmark["max"]
            validation_results["overvaluation_risk"] = "critical" if overvaluation_factor > 2.0 else "high"
            validation_results["adjustments_needed"].append(
                f"Pain & suffering ${pain_suffering:,.0f} exceeds Ontario {benchmark_category} range (${benchmark['min']:,.0f}-${benchmark['max']:,.0f}). "
                f"Recommended adjustment to ${benchmark['max']:,.0f} (reduction of ${pain_suffering - benchmark['max']:,.0f})"
            )
            validation_results["warnings"].append(
                f"AI calculated {overvaluation_factor:.1f}x the maximum Ontario benchmark for {benchmark_category} injuries"
            )
        elif pain_suffering < benchmark["min"]:
            validation_results["warnings"].append(
                f"Pain & suffering ${pain_suffering:,.0f} is below typical Ontario range"
            )
        
        # Special validation for slip-fall cases
        if "slip" in str(ai_extraction).lower() or "fall" in str(ai_extraction).lower():
            slip_fall_benchmark = ontario_benchmarks["slip_fall_typical"]
            if pain_suffering > slip_fall_benchmark["max"]:
                validation_results["adjustments_needed"].append(
                    f"For slip-fall cases, ${pain_suffering:,.0f} exceeds typical Ontario range (${slip_fall_benchmark['min']:,.0f}-${slip_fall_benchmark['max']:,.0f})"
                )
    
    # RESILIENT: Medical costs validation with safe access
    try:
        medical = getattr(ai_extraction, 'medicalDamages', None)
        if not medical:
            validation_results["warnings"].append("No medical damages data available for validation")
            return validation_results
            
        documented_costs = getattr(medical, 'documentedMedicalCosts', 0)
        future_medical = getattr(medical, 'estimatedFutureMedical', 0)
        total_medical = documented_costs + future_medical
        documented_only = documented_costs
    except (AttributeError, TypeError) as e:
        validation_results["warnings"].append(f"Could not access medical damages data: {e}")
        return validation_results
    
    # Ontario moderate injury medical cost benchmarks
    moderate_injury_max = 5000  # Based on user analysis: actual $1,610 vs. system $7,000
    
    if total_medical > moderate_injury_max:
        overvaluation_factor = total_medical / moderate_injury_max
        validation_results["overvaluation_risk"] = "high" if overvaluation_factor > 1.5 else "moderate"
        validation_results["adjustments_needed"].append(
            f"Medical costs ${total_medical:,.0f} exceed typical Ontario moderate injury range (${moderate_injury_max:,.0f} max). "
            f"Recommended adjustment based on documented evidence only: ${min(documented_only, moderate_injury_max):,.0f}"
        )
    
    # RESILIENT: Check if medical costs are reasonable per session
    try:
        treatment_sessions = getattr(medical, 'treatmentSessions', 0)
        if treatment_sessions > 0 and total_medical > 0:
            cost_per_session = total_medical / treatment_sessions
            if cost_per_session > 200:  # $200+ per session is high
                validation_results["warnings"].append(
                    f"Average treatment cost ${cost_per_session:.0f} per session appears high"
                )
    except (AttributeError, TypeError, ZeroDivisionError):
        pass  # Skip session validation if data unavailable
    
    # RESILIENT: Validate future medical projections
    try:
        if future_medical > documented_only:
            validation_results["warnings"].append(
                f"Future medical costs ${future_medical:,.0f} exceed documented costs ${documented_only:,.0f} - requires strong medical evidence"
            )
    except (TypeError, AttributeError):
        pass  # Skip if data unavailable
    
    return validation_results

def convert_ai_extraction_to_quantum(ai_extraction, province: str) -> QuantumBreakdown:
    """Convert AI extraction results to traditional quantum breakdown format with validation"""
    
    # Validate against Ontario benchmarks
    validation_results = validate_ontario_quantum_amounts(ai_extraction, province)
    
    # Medical costs
    medical = ai_extraction.medicalDamages
    cost_breakdown = CostBreakdown(
        medical_costs=medical.emergencyCareCosts + medical.ongoingTreatmentCosts,
        rehabilitation_costs=medical.ongoingTreatmentCosts,
        medication_costs=medical.medicationCosts,
        equipment_costs=medical.medicalEquipmentCosts,
        hospitalization_costs=medical.emergencyCareCosts,
        future_medical_costs=medical.estimatedFutureMedical,
        total_medical=medical.documentedMedicalCosts + medical.estimatedFutureMedical
    )
    
    # Income loss
    income = ai_extraction.incomeLossDamages
    income_breakdown = IncomeBreakdown(
        past_income_loss=income.documentedIncomeLoss,
        future_income_loss=income.projectedFutureIncomeLoss,
        loss_of_earning_capacity=income.projectedFutureIncomeLoss,
        pension_loss=0,  # Not specifically extracted by AI
        benefits_loss=0,  # Not specifically extracted by AI
        total_income_loss=income.documentedIncomeLoss + income.projectedFutureIncomeLoss
    )
    
    # Special damages total
    special_damages = ai_extraction.specialDamages
    total_special_damages = (
        cost_breakdown.total_medical +
        income_breakdown.total_income_loss +
        special_damages.specialDamagesTotal +
        ai_extraction.careAndAssistanceCosts.totalCareAssistanceCosts +
        ai_extraction.futureCareCosts.totalFutureCareCosts
    )
    
    # General damages with validation adjustments
    general = ai_extraction.generalDamages
    original_general_damages = general.recommendedGeneralDamages
    
    # Apply Ontario benchmark adjustments if overvaluation detected
    adjusted_general_damages = original_general_damages
    adjustment_notes = []
    
    if validation_results["overvaluation_risk"] in ["high", "critical"]:
        ontario_comparison = validation_results["ontario_benchmark_comparison"]
        if ontario_comparison and original_general_damages > ontario_comparison["benchmark_max"]:
            # Apply conservative adjustment to stay within Ontario benchmarks
            adjusted_general_damages = ontario_comparison["benchmark_max"]
            reduction_amount = original_general_damages - adjusted_general_damages
            adjustment_notes.append(
                f"⚠️ ADJUSTED: Pain & suffering reduced from ${original_general_damages:,.0f} to ${adjusted_general_damages:,.0f} "
                f"(${reduction_amount:,.0f} reduction) to align with Ontario {ontario_comparison['category']} injury benchmarks"
            )
    
    # Use adjusted general damages
    total_general_damages = adjusted_general_damages
    
    # Fault impact
    fault_impact = ai_extraction.faultImpactAnalysis
    fault_reduction_percentage = fault_impact.claimantFaultPercentage
    
    # Total damages
    total_damages = total_special_damages + total_general_damages
    fault_reduction_amount = total_damages * (fault_reduction_percentage / 100)
    net_recoverable = total_damages - fault_reduction_amount
    
    # Build calculation notes with validation results
    calculation_notes = [
        f"AI confidence: {ai_extraction.quantumGuidance.quantumConfidence:.2f}",
        f"Medical complexity: {medical.medicalComplexity}",
        f"Settlement strategy: {ai_extraction.quantumGuidance.settlementStrategy}",
        f"Litigation risk: {ai_extraction.quantumGuidance.litigationRisk}",
        f"Overvaluation risk: {validation_results['overvaluation_risk']}"
    ]
    
    # Add adjustment notes
    calculation_notes.extend(adjustment_notes)
    
    # Add validation warnings
    for warning in validation_results["warnings"]:
        calculation_notes.append(f"⚠️ {warning}")
    
    # Add benchmark comparison info
    if validation_results["ontario_benchmark_comparison"]:
        comparison = validation_results["ontario_benchmark_comparison"]
        calculation_notes.append(
            f"Ontario benchmark: {comparison['category']} (${comparison['benchmark_min']:,.0f}-${comparison['benchmark_max']:,.0f})"
        )
    
    return QuantumBreakdown(
        cost_breakdown=cost_breakdown,
        income_breakdown=income_breakdown,
        out_of_pocket=special_damages.specialDamagesTotal,
        property_damage=0,  # Not specifically extracted
        total_special_damages=total_special_damages,
        pain_and_suffering=total_general_damages,
        loss_of_amenities=0,  # Part of general damages
        loss_of_expectation_of_life=0,  # Part of general damages
        total_general_damages=total_general_damages,
        total_damages=total_damages,
        fault_reduction_percentage=fault_reduction_percentage,
        fault_reduction_amount=fault_reduction_amount,
        pre_existing_reduction=0,  # Not specifically calculated
        net_recoverable=net_recoverable,
        calculation_notes=calculation_notes
    )

@router.post("/calculate", response_model=Level04Response)
async def calculate_quantum(request: Level04Request):
    """
    Perform Level 04 Quantum Calculation using Canadian rules
    
    This endpoint:
    1. Fetches prior analyses from Supabase
    2. Calculates special damages (medical, income loss)
    3. Calculates general damages (pain & suffering)
    4. Applies reductions (fault, pre-existing)
    5. Provides settlement recommendations
    """
    request_id = str(uuid.uuid4())
    start_time = time.time()
    
    # Log request start
    structured_logger.log_request_start(
        request_id, 
        "/api/level04-quantum/calculate", 
        len(str(request.dict()).encode('utf-8'))
    )
    
    try:
        logger.info(f"Processing Level 04 quantum calculation for claim {request.claim_reference}")
        
        # Log data validation
        structured_logger.log_data_validation(request_id, "claim_reference", "valid", request.claim_reference)
        structured_logger.log_data_validation(request_id, "injuries_count", "valid", len(request.injuries))
        structured_logger.log_data_validation(request_id, "province", "valid", request.province)
        
        # Fetch prior analyses
        claim_data = await fetch_prior_analyses(request.claim_reference)
        
        # Get fault percentage from Level 3 or request
        if request.claimant_fault_percentage is not None:
            fault_percentage = request.claimant_fault_percentage
        elif request.level03_fault:
            fault_percentage = request.level03_fault.get("claimant_fault", 0)
        elif claim_data.get("03_level_analysis"):
            fault_allocation = claim_data["03_level_analysis"].get("fault_allocation", {})
            fault_percentage = fault_allocation.get("claimant", 0)
        else:
            fault_percentage = 0  # Assume no fault if not specified
        
        # Get province
        province = Province(request.province)
        
        # Calculate special damages
        special_calc_start = time.time()
        cost_breakdown, income_breakdown, total_special = calculate_special_damages(
            request.injuries,
            request.treatment_plan,
            request.income_info,
            province,
            request.out_of_pocket_expenses,
            request.property_damage
        )
        special_calc_end = time.time()
        
        # Log rule-based special damages calculation
        structured_logger.log_rule_decision(
            request_id, "special_damages_calculation", 
            f"{province.value}_medical_cost_tables",
            f"total_special:{total_special:.2f}_medical:{cost_breakdown.total_medical:.2f}_income:{income_breakdown.total_income_loss:.2f}",
            (special_calc_end - special_calc_start) * 1000
        )
        
        # Calculate general damages
        general_calc_start = time.time()
        pain_suffering, loss_amenities, loss_life_expectancy, total_general = calculate_general_damages(
            request.injuries
        )
        general_calc_end = time.time()
        
        # Log rule-based general damages calculation  
        structured_logger.log_rule_decision(
            request_id, "general_damages_calculation", "canadian_pain_suffering_cap_rules",
            f"total_general:{total_general:.2f}_pain_suffering:{pain_suffering:.2f}",
            (general_calc_end - general_calc_start) * 1000
        )
        
        # Total damages before reductions
        total_damages = total_special + total_general
        
        # Apply reductions
        reduction_calc_start = time.time()
        fault_reduction, pre_existing_reduction, net_recoverable = apply_reductions(
            total_damages,
            fault_percentage,
            request.pre_existing_conditions
        )
        reduction_calc_end = time.time()
        
        # Log rule-based reductions calculation
        structured_logger.log_rule_decision(
            request_id, "damages_reduction_calculation", "fault_and_preexisting_reduction_rules",
            f"net_recoverable:{net_recoverable:.2f}_fault_reduction:{fault_reduction:.2f}_fault_pct:{fault_percentage}",
            (reduction_calc_end - reduction_calc_start) * 1000
        )
        
        # Build quantum breakdown
        quantum_breakdown = QuantumBreakdown(
            cost_breakdown=cost_breakdown,
            income_breakdown=income_breakdown,
            out_of_pocket=request.out_of_pocket_expenses,
            property_damage=request.property_damage,
            total_special_damages=total_special,
            pain_and_suffering=pain_suffering,
            loss_of_amenities=loss_amenities,
            loss_of_expectation_of_life=loss_life_expectancy,
            total_general_damages=total_general,
            total_damages=total_damages,
            fault_reduction_percentage=fault_percentage,
            fault_reduction_amount=fault_reduction,
            pre_existing_reduction=pre_existing_reduction,
            net_recoverable=net_recoverable,
            calculation_notes=[]
        )
        
        # Add calculation notes
        if fault_percentage > 0:
            quantum_breakdown.calculation_notes.append(
                f"Reduced by {fault_percentage}% for contributory negligence"
            )
        if request.pre_existing_conditions:
            quantum_breakdown.calculation_notes.append(
                f"Adjusted for pre-existing conditions: {', '.join(request.pre_existing_conditions)}"
            )
        if province == Province.QC and any(inj.injury_type != "property" for inj in request.injuries):
            quantum_breakdown.calculation_notes.append(
                "Quebec SAAQ covers bodily injury - only property damage recoverable"
            )
        
        # Policy considerations
        policy_limit = claim_data.get("02_level_analysis", {}).get("policy_limits", {}).get("liability", 1000000)
        within_limits = net_recoverable <= policy_limit
        excess = max(0, net_recoverable - policy_limit)
        
        # Settlement recommendations
        settlement_calc_start = time.time()
        settlement_low = net_recoverable * 0.7  # 70% for quick settlement
        settlement_high = net_recoverable * 0.9  # 90% for negotiated settlement
        recommended = net_recoverable * 0.8  # 80% as fair settlement
        settlement_calc_end = time.time()
        
        # Log rule-based settlement calculation
        structured_logger.log_rule_decision(
            request_id, "settlement_calculation", "settlement_percentage_rules",
            f"recommended:{recommended:.2f}_range:{settlement_low:.2f}-{settlement_high:.2f}",
            (settlement_calc_end - settlement_calc_start) * 1000
        )
        
        # Reserve recommendations
        initial_reserve = total_special + (total_general * 0.5)  # Conservative initial
        recommended_reserve = net_recoverable * 1.1  # 10% buffer
        
        # Determine confidence and next steps
        confidence = 0.9  # High confidence with complete data
        assumptions = []
        data_gaps = []
        
        if not request.income_info:
            data_gaps.append("No income information provided")
            confidence -= 0.1
        
        if len(request.injuries) == 0:
            data_gaps.append("No specific injuries documented")
            confidence -= 0.2
        
        if not claim_data.get("03_level_analysis"):
            assumptions.append("Fault determination not completed")
            confidence -= 0.1
        
        # Determine exit path
        if net_recoverable <= 25000:
            exit_path = "SETTLE"  # Fast track settlement
            proceed_to_settlement = True
        elif net_recoverable <= 100000 and fault_percentage < 25:
            exit_path = "NEGOTIATE"  # Standard negotiation
            proceed_to_settlement = True
        elif net_recoverable > policy_limit:
            exit_path = "LITIGATE"  # Excess exposure
            proceed_to_settlement = False
        else:
            exit_path = "MEDIATE"  # Complex case
            proceed_to_settlement = False
        
        # Medical/actuarial review flags
        requires_medical = any(inj.severity in ["severe", "catastrophic"] for inj in request.injuries)
        requires_actuarial = (
            any(inj.permanent_impairment for inj in request.injuries) or
            total_damages > 500000
        )
        
        # Store result in Supabase
        if supabase:
            try:
                result_data = {
                    "claim_reference": request.claim_reference,
                    "04_level_analysis": {
                        "quantum_breakdown": quantum_breakdown.dict(),
                        "settlement_recommendation": recommended,
                        "confidence": confidence,
                        "timestamp": datetime.utcnow().isoformat()
                    }
                }
                
                supabase.table('claims').update(result_data).eq('claim_reference', request.claim_reference).execute()
                logger.info(f"Level 04 analysis stored for claim {request.claim_reference}")
                
            except Exception as e:
                logger.error(f"Failed to store Level 04 analysis: {str(e)}")
        
        # Build response
        response = Level04Response(
            claim_reference=request.claim_reference,
            processing_timestamp=datetime.utcnow(),
            province=province.value,
            quantum_breakdown=quantum_breakdown,
            within_policy_limits=within_limits,
            policy_limit=policy_limit,
            excess_amount=excess,
            settlement_range_low=settlement_low,
            settlement_range_high=settlement_high,
            recommended_settlement=recommended,
            initial_reserve=initial_reserve,
            recommended_reserve=recommended_reserve,
            calculation_confidence=confidence,
            assumptions_made=assumptions,
            data_gaps=data_gaps,
            proceed_to_settlement=proceed_to_settlement,
            requires_medical_review=requires_medical,
            requires_actuarial_review=requires_actuarial,
            exit_path=exit_path
        )
        
        logger.info(f"Level 04 quantum calculation completed for claim {request.claim_reference}")
        
        # Log request completion
        total_time = (time.time() - start_time) * 1000
        structured_logger.log_request_complete(
            request_id, total_time, True,
            f"net_recoverable:{net_recoverable:.2f}_exit_path:{exit_path}_confidence:{confidence:.2f}"
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error in Level 04 calculation: {str(e)}")
        
        # Log error
        structured_logger.log_error(
            request_id, "quantum_calculation_error", str(e), "level04_quantum_calculation"
        )
        
        # Log failed completion
        total_time = (time.time() - start_time) * 1000
        structured_logger.log_request_complete(
            request_id, total_time, False, f"ERROR: {str(e)[:100]}"
        )
        
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/injury-costs/{province}")
async def get_provincial_injury_costs(province: str, injury_type: str = "soft_tissue"):
    """
    Get injury cost guidelines for a specific province
    
    Returns the standard medical costs for different injury types and severities.
    """
    try:
        province_enum = Province(province)
        
        # Get injury costs from the medical calculator
        if injury_type in CanadianMedicalCosts.INJURY_COSTS:
            costs = {}
            for severity, provincial_costs in CanadianMedicalCosts.INJURY_COSTS[injury_type].items():
                if province_enum in provincial_costs:
                    costs[severity] = provincial_costs[province_enum]
                else:
                    # Use Ontario as default
                    costs[severity] = provincial_costs.get(Province.ON, {})
        else:
            costs = {}
        
        # Get rehabilitation costs
        rehab_costs = {}
        for therapy, provincial_costs in CanadianMedicalCosts.REHAB_COSTS.items():
            if province_enum in provincial_costs:
                rehab_costs[therapy] = provincial_costs[province_enum]
            else:
                rehab_costs[therapy] = provincial_costs.get(Province.ON, 0)
        
        # Get income replacement rules
        income_rules = CanadianMedicalCosts.INCOME_REPLACEMENT.get(
            province_enum,
            CanadianMedicalCosts.INCOME_REPLACEMENT.get(Province.ON, {})
        )
        
        return {
            "province": province,
            "injury_costs": costs,
            "rehabilitation_costs_per_session": rehab_costs,
            "income_replacement_rules": income_rules,
            "general_damages_cap": 400000,  # 2024 Supreme Court cap
            "available_injury_types": list(CanadianMedicalCosts.INJURY_COSTS.keys())
        }
        
    except ValueError:
        raise HTTPException(status_code=400, detail=f"Invalid province: {province}")


@router.get("/settlement-calculator")
async def settlement_calculator(
    total_damages: float,
    fault_percentage: int = 0,
    policy_limit: float = 1000000
):
    """
    Quick settlement calculation tool
    
    Provides settlement ranges based on total damages and fault percentage.
    """
    # Apply fault reduction
    fault_reduction = total_damages * (fault_percentage / 100)
    net_damages = total_damages - fault_reduction
    
    # Calculate settlement ranges
    quick_settlement = net_damages * 0.65  # 65% for immediate settlement
    fair_settlement = net_damages * 0.80   # 80% for negotiated settlement
    aggressive_settlement = net_damages * 0.90  # 90% for hard negotiation
    
    # Check policy limits
    if net_damages > policy_limit:
        excess = net_damages - policy_limit
        limited_settlement = policy_limit
    else:
        excess = 0
        limited_settlement = net_damages
    
    return {
        "total_damages": total_damages,
        "fault_percentage": fault_percentage,
        "fault_reduction": fault_reduction,
        "net_damages": net_damages,
        "settlement_ranges": {
            "quick_settlement": min(quick_settlement, limited_settlement),
            "fair_settlement": min(fair_settlement, limited_settlement),
            "aggressive_settlement": min(aggressive_settlement, limited_settlement),
            "maximum_recovery": limited_settlement
        },
        "policy_limit": policy_limit,
        "excess_exposure": excess,
        "within_limits": excess == 0
    }


@router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "Level 04 - Quantum Calculation",
        "timestamp": datetime.utcnow().isoformat(),
        "supabase_connected": supabase is not None
    } 