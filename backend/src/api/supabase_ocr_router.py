"""
ZURICH SUPABASE OCR ROUTER
==========================

Purpose: FastAPI router for processing claim files from Supabase storage through Zurich OCR API
Integration: Accepts claim ID, streams files from Supabase directly to OCR API without local downloads
Endpoints: POST /process-claim-files, GET /health, GET /info

Usage:
- Accept claim ID (e.g., "CLM-********")
- List all files in Supabase storage for that claim
- Stream files directly to Zurich OCR API using multipart form data
- Return OCR processing results

Architecture:
- No local file downloads
- Streaming from Supabase → OCR API
- Memory efficient processing
"""

import asyncio
import logging
import json
import os
from datetime import datetime
from typing import List, Optional, Dict, Any
import traceback
import io

from fastapi import APIRouter, HTTPException, Request, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field, validator
import httpx
import aiohttp

# ================================================================================================
# LOGGING CONFIGURATION
# ================================================================================================

logger = logging.getLogger("ZurichSupabaseOCR")

# ================================================================================================
# ROUTER INITIALIZATION 
# ================================================================================================

router = APIRouter(
    prefix="/supabase-ocr",
    tags=["supabase-ocr-processing"],
    responses={
        404: {"description": "Not found"},
        500: {"description": "Internal server error"}
    }
)

# ================================================================================================
# CONFIGURATION MODELS
# ================================================================================================

class OCRConfig(BaseModel):
    """OCR Configuration model - same as existing OCR wrapper"""
    ocr_engine: str = Field(default="google", description="OCR engine to use")
    google_processor: str = Field(default="OCR_PROCESSOR", description="Google processor type")
    llm_routing_enabled: bool = Field(default=False, description="Enable LLM routing")
    post_processing: str = Field(default="v1", description="Post processing version")
    preprocessing: str = Field(default="none", description="Preprocessing options")
    parallel_processing: bool = Field(default=False, description="Enable parallel processing")
    
    @validator('ocr_engine')
    def validate_ocr_engine(cls, v):
        allowed_engines = ["google", "azure", "aws"]
        if v not in allowed_engines:
            raise ValueError(f"OCR engine must be one of {allowed_engines}")
        return v

class ClaimOCRRequest(BaseModel):
    """Request model for claim file processing"""
    claimId: str = Field(..., description="Claim ID (e.g., CLM-********)")
    config: OCRConfig = Field(default_factory=OCRConfig, description="OCR configuration")
    
    @validator('claimId')
    def validate_claim_id(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("Claim ID cannot be empty")
        return v.strip()

class SupabaseFileInfo(BaseModel):
    """Model for Supabase file information"""
    name: str
    path: str
    size: Optional[int] = None
    content_type: Optional[str] = None
    public_url: str

class ClaimOCRResponse(BaseModel):
    """Response model for claim OCR processing"""
    success: bool = Field(..., description="Whether the operation was successful")
    claimId: str = Field(..., description="Processed claim ID")
    processed_files: int = Field(..., description="Number of files processed")
    file_list: List[str] = Field(..., description="List of processed file names")
    zurich_ocr_response: Dict[str, Any] = Field(..., description="Raw response from Zurich OCR API")
    processing_time: float = Field(..., description="Total processing time in seconds")
    config_used: Dict[str, Any] = Field(..., description="Configuration used for processing")
    error_message: Optional[str] = Field(None, description="Error message if any")

# ================================================================================================
# SUPABASE CLIENT
# ================================================================================================

class SupabaseFileService:
    """Service for interacting with Supabase storage via direct API calls"""
    
    def __init__(self):
        # Load configuration from environment variables
        self.supabase_url = os.getenv("SUPABASE_URL")
        self.supabase_anon_key = os.getenv("SUPABASE_ANON_KEY")
        self.supabase_service_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        self.bucket_name = os.getenv("SUPABASE_BUCKET_NAME", "claims-attachments")
        
        if not self.supabase_url or not self.supabase_service_key:
            raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables are required")
        
        # Remove trailing slash from URL
        self.supabase_url = self.supabase_url.rstrip('/')
        
        # Set up API endpoints
        self.storage_api_base = f"{self.supabase_url}/storage/v1"
        self.list_endpoint = f"{self.storage_api_base}/object/list/{self.bucket_name}"
        self.public_endpoint = f"{self.storage_api_base}/object/public/{self.bucket_name}"
        
        # Set up headers for authentication using service role key (has full permissions)
        self.headers = {
            "apikey": self.supabase_service_key,
            "Authorization": f"Bearer {self.supabase_service_key}",
            "Content-Type": "application/json"
        }
        
        logger.info(f"Initializing Supabase API client for URL: {self.supabase_url}")
        logger.info(f"Storage API base: {self.storage_api_base}")
        logger.info(f"Bucket: {self.bucket_name}")
        logger.info(f"Using service role key for full permissions")
        
    async def list_claim_files(self, claim_id: str) -> List[SupabaseFileInfo]:
        """
        List all files for a given claim ID from Supabase storage using direct API calls
        
        Args:
            claim_id: The claim ID (e.g., CLM-********)
            
        Returns:
            List of file information
        """
        try:
            logger.info(f"Listing files for claim: {claim_id}")
            
            async with httpx.AsyncClient() as client:
                # First, let's check what's in the root directory
                logger.info(f"Checking root directory: POST {self.list_endpoint}")
                
                root_response = await client.post(
                    self.list_endpoint,
                    headers=self.headers,
                    json={"prefix": "", "limit": 100}
                )
                
                logger.info(f"Root directory response status: {root_response.status_code}")
                
                if root_response.status_code == 200:
                    root_data = root_response.json()
                    root_names = [item.get('name', '') for item in root_data[:10]]
                    logger.info(f"Available in root directory: {root_names}")
                else:
                    logger.error(f"Failed to list root directory: {root_response.status_code} - {root_response.text}")
                
                # Now list files in the specific claim folder (under claim/ subdirectory)
                claim_prefix = f"claim/{claim_id}/"
                logger.info(f"Listing files with prefix: '{claim_prefix}'")
                
                list_response = await client.post(
                    self.list_endpoint,
                    headers=self.headers,
                    json={"prefix": claim_prefix, "limit": 100}
                )
                
                logger.info(f"Claim files response status: {list_response.status_code}")
                logger.info(f"Response text: {list_response.text[:200]}...")
                
                if list_response.status_code != 200:
                    error_msg = f"Failed to list files for claim {claim_id}: {list_response.status_code} - {list_response.text}"
                    logger.error(error_msg)
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail=error_msg
                    )
                
                files_data = list_response.json()
                logger.info(f"Found {len(files_data)} items for claim: {claim_id}")
                
                if not files_data:
                    logger.warning(f"No files found for claim: {claim_id}")
                    return []
                
                files = []
                for file_obj in files_data:
                    file_name = file_obj.get('name', '')
                    logger.info(f"Processing file object: {file_obj}")
                    
                    # Skip folders (they end with /)
                    if not file_name or file_name.endswith('/'):
                        logger.info(f"Skipping folder: {file_name}")
                        continue
                    
                    # Create public URL (files are already prefixed with claim/ in file_name)
                    public_url = f"{self.public_endpoint}/claim/{claim_id}/{file_name}"
                    
                    file_info = SupabaseFileInfo(
                        name=file_name.split('/')[-1],  # Get just the filename
                        path=file_name,
                        size=file_obj.get('metadata', {}).get('size'),
                        content_type=file_obj.get('metadata', {}).get('mimetype'),
                        public_url=public_url
                    )
                    files.append(file_info)
                    logger.info(f"Added file: {file_info.name} -> {file_info.public_url}")
                    
                logger.info(f"Processed {len(files)} files for claim: {claim_id}")
                return files
            
        except Exception as e:
            error_msg = f"Error listing files for claim {claim_id}: {str(e)}"
            logger.error(f"{error_msg}\n{traceback.format_exc()}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=error_msg
            )

# ================================================================================================
# ZURICH OCR CLIENT FOR STREAMING
# ================================================================================================

class ZurichOCRStreamingClient:
    """Client for streaming files to Zurich OCR API without local storage"""
    
    def __init__(self):
        self.base_url = "https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/batch-process"
        self.timeout = 300  # 5 minutes timeout for OCR processing
        
    async def process_files_stream(self, files: List[SupabaseFileInfo], config: OCRConfig) -> Dict[str, Any]:
        """
        Process files by streaming them directly from Supabase to Zurich OCR API
        
        Args:
            files: List of Supabase file information
            config: OCR configuration
            
        Returns:
            OCR processing results
        """
        start_time = datetime.now()
        
        try:
            logger.info(f"Starting streaming OCR processing for {len(files)} files")
            
            # Create multipart form data
            form_data = aiohttp.FormData()
            
            # Add each file as a stream
            for file_info in files:
                logger.debug(f"Adding file to form: {file_info.name}")
                
                # Stream file content from Supabase
                async with httpx.AsyncClient() as client:
                    file_response = await client.get(file_info.public_url)
                    file_response.raise_for_status()
                    
                    # Add file to form data
                    form_data.add_field(
                        'files',
                        file_response.content,
                        filename=file_info.name,
                        content_type=file_info.content_type or 'application/octet-stream'
                    )
            
            # Add config as JSON string
            config_json = config.json()
            form_data.add_field('config', config_json)
            
            logger.info("Sending request to Zurich OCR API...")
            
            # Make request to Zurich OCR API
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.base_url,
                    data=form_data,
                    timeout=aiohttp.ClientTimeout(total=self.timeout)
                ) as response:
                    
                    if response.status == 200:
                        ocr_results = await response.json()
                        processing_time = (datetime.now() - start_time).total_seconds()
                        
                        logger.info(f"✅ OCR processing completed successfully in {processing_time:.2f}s")
                        
                        return {
                            "success": True,
                            "processed_files": len(files),
                            "results": ocr_results.get("results", []),
                            "processing_time": processing_time,
                            "config_used": config.dict(),
                            "raw_response": ocr_results
                        }
                    else:
                        error_text = await response.text()
                        error_msg = f"OCR service returned status {response.status}: {error_text}"
                        logger.error(error_msg)
                        
                        return {
                            "success": False,
                            "processed_files": 0,
                            "results": [],
                            "processing_time": (datetime.now() - start_time).total_seconds(),
                            "config_used": config.dict(),
                            "error_message": error_msg
                        }
                        
        except Exception as e:
            error_msg = f"Error in streaming OCR processing: {str(e)}"
            logger.error(f"{error_msg}\n{traceback.format_exc()}")
            
            return {
                "success": False,
                "processed_files": 0,
                "results": [],
                "processing_time": (datetime.now() - start_time).total_seconds(),
                "config_used": config.dict(),
                "error_message": error_msg
            }

# ================================================================================================
# SERVICE INITIALIZATION (Lazy-loaded to avoid startup errors)
# ================================================================================================

_supabase_service = None
_ocr_client = None

def get_supabase_service() -> SupabaseFileService:
    """Get or create Supabase service instance"""
    global _supabase_service
    if _supabase_service is None:
        _supabase_service = SupabaseFileService()
    return _supabase_service

def get_ocr_client() -> ZurichOCRStreamingClient:
    """Get or create OCR client instance"""
    global _ocr_client
    if _ocr_client is None:
        _ocr_client = ZurichOCRStreamingClient()
    return _ocr_client

# ================================================================================================
# API ENDPOINTS
# ================================================================================================

@router.post("/process-claim-files",
             response_model=ClaimOCRResponse,
             summary="Process claim files from Supabase through OCR",
             description="Stream files from Supabase storage directly to Zurich OCR API without local downloads")
async def process_claim_files(request: ClaimOCRRequest):
    """
    Process all files for a claim ID through OCR without downloading files locally
    
    This endpoint:
    1. Lists all files in Supabase storage for the given claim ID
    2. Streams files directly to Zurich OCR API using multipart form data
    3. Returns OCR processing results
    
    No files are downloaded to local storage - everything is streamed in memory.
    """
    start_time = datetime.now()
    
    try:
        logger.info(f"🔄 Processing claim files for: {request.claimId}")
        
        # 1. List files from Supabase for the claim
        supabase_service = get_supabase_service()
        files = await supabase_service.list_claim_files(request.claimId)
        
        if not files:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No files found for claim ID: {request.claimId}"
            )
        
        logger.info(f"Found {len(files)} files for claim {request.claimId}")
        
        # 2. Process files through OCR API via streaming
        ocr_client = get_ocr_client()
        ocr_result = await ocr_client.process_files_stream(files, request.config)
        
        # 3. Build response
        processing_time = (datetime.now() - start_time).total_seconds()
        
        response = ClaimOCRResponse(
            success=ocr_result["success"],
            claimId=request.claimId,
            processed_files=len(files),
            file_list=[f.name for f in files],
            zurich_ocr_response=ocr_result.get("raw_response", {}),
            processing_time=processing_time,
            config_used=request.config.dict(),
            error_message=ocr_result.get("error_message")
        )
        
        if response.success:
            logger.info(f"✅ Successfully processed {response.processed_files} files for claim {request.claimId}")
        else:
            logger.error(f"❌ Failed to process files for claim {request.claimId}: {response.error_message}")
        
        return response
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        error_msg = f"Unexpected error processing claim {request.claimId}: {str(e)}"
        logger.error(f"❌ {error_msg}\n{traceback.format_exc()}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "claim_processing_failed",
                "message": error_msg,
                "timestamp": datetime.now().isoformat()
            }
        )

@router.get("/health",
            summary="Health check",
            description="Check if the Supabase OCR service is healthy")
async def health_check():
    """Health check for Supabase OCR service"""
    try:
        # Test Supabase connection by making API call
        supabase_service = get_supabase_service()
        
        async with httpx.AsyncClient() as client:
            # Test Supabase API
            supabase_response = await client.post(
                supabase_service.list_endpoint,
                headers=supabase_service.headers,
                json={"prefix": "", "limit": 1},
                timeout=5.0
            )
            buckets_accessible = 1 if supabase_response.status_code == 200 else 0
        
        # Test OCR API connectivity
        ocr_api_status = "unknown"
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get("https://zurich-ocr.dev-scc-demo.rozie.ai/")
                ocr_api_status = "reachable" if response.status_code in [200, 404] else "unreachable"
        except:
            ocr_api_status = "unreachable"
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "supabase_client": "connected" if buckets_accessible > 0 else "disconnected",
            "supabase_bucket": supabase_service.bucket_name,
            "buckets_accessible": buckets_accessible,
            "zurich_ocr_api": ocr_api_status,
            "service": "supabase-ocr-processing",
            "version": "1.0.0"
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "service": "supabase-ocr-processing"
            }
        )

@router.get("/list-claims",
            summary="List available claim folders",
            description="List all claim folders available in Supabase storage")
async def list_available_claims():
    """List all claim folders in Supabase storage"""
    try:
        supabase_service = get_supabase_service()
        
        # List all folders in the root directory using API
        async with httpx.AsyncClient() as client:
            response = await client.post(
                supabase_service.list_endpoint,
                headers=supabase_service.headers,
                json={"prefix": "", "limit": 100}
            )
            
            if response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to list claims: {response.status_code} - {response.text}"
                )
            
            root_items = response.json()
            
            claim_folders = []
            for item in root_items:
                item_name = item.get('name', '')
                if item_name and item_name.startswith('CLM-') and not item_name.endswith('/'):
                    # This is a claim folder, get file count
                    files = await supabase_service.list_claim_files(item_name)
                    claim_folders.append({
                        "claimId": item_name,
                        "fileCount": len(files),
                        "lastModified": item.get('updated_at'),
                        "hasFiles": len(files) > 0
                    })
        
        return {
            "success": True,
            "available_claims": claim_folders,
            "total_claims": len(claim_folders),
            "claims_with_files": len([c for c in claim_folders if c["hasFiles"]]),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error listing claims: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error listing available claims: {str(e)}"
        )

@router.get("/info",
            summary="API information",
            description="Get API information for Supabase OCR processing")
async def api_info():
    """Get comprehensive API information"""
    return {
        "service": "Zurich Supabase OCR Processing API",
        "version": "1.0.0",
        "description": "Stream claim files from Supabase storage directly to Zurich OCR API",
        "features": [
            "No local file downloads",
            "Memory efficient streaming",
            "Concurrent file processing",
            "Supabase storage integration"
        ],
        "endpoints": {
            "/supabase-ocr/process-claim-files": {
                "method": "POST",
                "description": "Process all files for a claim ID through OCR",
                "input": "ClaimOCRRequest",
                "output": "ClaimOCRResponse"
            },
            "/supabase-ocr/health": {
                "method": "GET",
                "description": "Health check endpoint"
            },
            "/supabase-ocr/info": {
                "method": "GET",
                "description": "API information and capabilities"
            }
        },
        "supported_ocr_engines": ["google", "azure", "aws"],
        "supported_file_types": ["PDF", "DOCX", "TXT", "JPG", "PNG"],
        "configuration_options": {
            "ocr_engine": "google",
            "google_processor": "OCR_PROCESSOR",
            "llm_routing_enabled": False,
            "post_processing": "v1",
            "preprocessing": "none",
            "parallel_processing": False
        },
        "limits": {
            "max_files_per_claim": 100,
            "timeout_seconds": 300,
            "max_file_size_mb": 50
        },
        "timestamp": datetime.now().isoformat()
    } 