"""
===================================================================================
ZURICH LEVEL 02 COVERAGE ANALYSIS - FASTAPI ROUTER (SUPABASE ENHANCED)
===================================================================================
Purpose: Coverage determination with intelligent exit path routing
Integration: Auto-fetches data from Supabase (claims, attachments, OCR text)
Enhancement: AI-driven risk assessment and data completeness scoring
===================================================================================
"""

import logging
import json
import time
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, status, Depends
from pydantic import BaseModel, Field, validator
from supabase import create_client, Client
from baml_client import b
from baml_client.types import (
    Level02CoverageAnalysis, 
    Level02AnalysisInput, 
    Level01Summary,
    CoverageDecision,
    DataCompletenessAssessment,
    PriorityRiskAssessment
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize router
router = APIRouter(
    prefix="/api/level02-coverage",
    tags=["level02-coverage"],
    responses={
        404: {"description": "Analysis not found"},
        500: {"description": "Analysis processing error"}
    }
)

# =====================================================================================
# SUPABASE INTEGRATION
# =====================================================================================

class SupabaseService:
    """Service for fetching claim data from Supabase"""
    
    def __init__(self):
        self.supabase_url = os.getenv("SUPABASE_URL")
        self.supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        
        if self.supabase_url and self.supabase_key:
            self.supabase: Client = create_client(self.supabase_url, self.supabase_key)
            self.enabled = True
            logger.info(f"Supabase connected: {self.supabase_url[:20]}...")
        else:
            self.supabase = None
            self.enabled = False
            logger.warning("Supabase not configured - will use fallback mode")
    
    async def fetch_claim_data(self, claim_id: str) -> Dict[str, Any]:
        """Fetch complete claim data from Supabase including Level 1 analysis"""
        if not self.enabled:
            logger.warning(f"Supabase not available for claim {claim_id}")
            return self._create_minimal_fallback(claim_id)
        
        try:
            # Fetch claim details including Level 1 analysis
            # Try different possible column names for the claim identifier
            claims_response = None
            try:
                # First try with claim_id
                claims_response = self.supabase.table('claims')\
                    .select('*, 01_level_analysis')\
                    .eq('claim_id', claim_id)\
                    .execute()
            except Exception as e1:
                try:
                    # Try with claim_reference
                    claims_response = self.supabase.table('claims')\
                        .select('*, 01_level_analysis')\
                        .eq('claim_reference', claim_id)\
                        .execute()
                except Exception as e2:
                    try:
                        # Try with id
                        claims_response = self.supabase.table('claims')\
                            .select('*, 01_level_analysis')\
                            .eq('id', claim_id)\
                            .execute()
                    except Exception as e3:
                        # If all attempts fail, use minimal fallback
                        logger.warning(f"Database access not available. Using minimal fallback for claim {claim_id}")
                        logger.debug(f"Errors: claim_id={e1}, claim_reference={e2}, id={e3}")
                        return self._create_minimal_fallback(claim_id)
            
            # Fetch attachments and OCR text using claim_reference instead of claim_id
            attachments_response = None
            try:
                attachments_response = self.supabase.table('attachments')\
                    .select('*')\
                    .eq('claim_reference', claim_id)\
                    .execute()
            except Exception as e:
                logger.warning(f"Could not fetch attachments for claim {claim_id}: {str(e)}")
                attachments_response = None
            
            claim_data = claims_response.data[0] if claims_response and claims_response.data else {}
            attachments_data = attachments_response.data if attachments_response and attachments_response.data else []
            
            # Extract Level 1 analysis from claim data
            level01_analysis = None
            if claim_data.get('01_level_analysis'):
                try:
                    # Parse JSON if it's a string, otherwise use directly
                    if isinstance(claim_data['01_level_analysis'], str):
                        level01_analysis = json.loads(claim_data['01_level_analysis'])
                    else:
                        level01_analysis = claim_data['01_level_analysis']
                    logger.info(f"Found Level 1 analysis for claim {claim_id}")
                except (json.JSONDecodeError, TypeError) as e:
                    logger.warning(f"Failed to parse Level 1 analysis for claim {claim_id}: {str(e)}")
                    level01_analysis = None
            
            return {
                "claim_details": claim_data,
                "level01_analysis": level01_analysis,
                "attachments": attachments_data,
                "ocr_texts": [att.get('ocr_text', '') for att in attachments_data if att.get('ocr_text')],
                "documents": [att.get('file_name', '') for att in attachments_data],
                "fetched_from_supabase": True
            }
            
        except Exception as e:
            logger.error(f"Failed to fetch from Supabase: {str(e)}")
            return self._create_minimal_fallback(claim_id)
    
    def _create_minimal_fallback(self, claim_id: str) -> Dict[str, Any]:
        """Create minimal fallback when Supabase is unavailable - returns empty structure"""
        logger.warning(f"Supabase unavailable for claim {claim_id} - returning empty structure")
        
        return {
            "claim_details": {"claim_id": claim_id},
            "level01_analysis": None,
            "attachments": [],
            "ocr_texts": [],
            "documents": [],
            "fetched_from_supabase": False,
            "fallback_data": True
        }

# Initialize Supabase service
supabase_service = SupabaseService()

# =====================================================================================
# SIMPLIFIED REQUEST/RESPONSE MODELS  
# =====================================================================================

class Level02CoverageRequest(BaseModel):
    """Simplified Level 02 Coverage Analysis Request - Data fetched from Supabase"""
    
    # Core Identifiers (Required)
    claim_id: str = Field(..., description="Unique claim identifier")
    
    # Processing Controls (Optional)
    urgency_level: str = Field(
        default="NORMAL", 
        description="Processing urgency: IMMEDIATE, HIGH, NORMAL, LOW"
    )
    
    # Optional overrides (if Supabase data unavailable)
    fallback_level01_analysis: Optional[Dict[str, Any]] = Field(
        None, 
        description="Level 01 analysis (only if not available in Supabase)"
    )
    fallback_policy_documents: Optional[List[str]] = Field(
        None, 
        description="Policy documents (only if not available in Supabase)"
    )
    
    # Special processing options
    force_human_review: bool = Field(
        default=False, 
        description="Force human expert review regardless of confidence"
    )
    include_ai_insights: bool = Field(
        default=True, 
        description="Include AI-driven analysis insights"
    )
    special_instructions: Optional[List[str]] = Field(
        default_factory=list, 
        description="Special handling instructions"
    )
    
    @validator('urgency_level')
    def validate_urgency_level(cls, v):
        valid_levels = ['IMMEDIATE', 'HIGH', 'NORMAL', 'LOW']
        if v not in valid_levels:
            raise ValueError(f'urgency_level must be one of {valid_levels}')
        return v

class Level02CoverageResponse(BaseModel):
    """Comprehensive Level 02 Coverage Analysis Response"""
    
    # Request Processing
    success: bool = Field(..., description="Whether analysis was successful")
    claim_id: str = Field(..., description="Claim identifier from request")
    processing_time_ms: int = Field(..., description="Processing time in milliseconds")
    timestamp: str = Field(..., description="Analysis timestamp")
    data_source: str = Field(..., description="Source of claim data (supabase/fallback)")
    
    # Coverage Decision Results
    coverage_decision: str = Field(..., description="Coverage determination: NOT_COVERED, COVERED, INFORMATION_REQUIRED")
    confidence_score: float = Field(..., description="Overall analysis confidence (0.0-1.0)")
    
    # Detailed Analysis
    analysis_results: Optional[Dict[str, Any]] = Field(None, description="Complete BAML analysis results")
    
    # Exit Path Specific Information
    coverage_justification: Optional[Dict[str, Any]] = Field(None, description="Detailed coverage justification")
    information_requests: Optional[List[Dict[str, Any]]] = Field(None, description="Information required (if applicable)")
    next_steps: Optional[Dict[str, Any]] = Field(None, description="Required next steps")
    risk_assessment: Optional[Dict[str, Any]] = Field(None, description="Risk assessment details")
    
    # Supabase Data Quality
    supabase_data: Optional[Dict[str, Any]] = Field(None, description="Data fetched from Supabase")
    data_completeness: Optional[float] = Field(None, description="Completeness of available data (0.0-1.0)")
    missing_data_items: Optional[List[str]] = Field(None, description="Items missing from Supabase")
    
    # Quality and Metadata
    analysis_quality: Optional[Dict[str, Any]] = Field(None, description="Analysis quality metrics")
    uncertainty_areas: Optional[List[Dict[str, Any]]] = Field(None, description="Areas requiring clarification")
    human_review_required: Optional[bool] = Field(None, description="Whether human expert review is required")
    legal_counsel_required: Optional[bool] = Field(None, description="Whether legal counsel review is required")
    
    # Error Information
    error_message: Optional[str] = Field(None, description="Error message if analysis failed")
    warnings: Optional[List[str]] = Field(None, description="Analysis warnings")

class Level02SimpleResponse(BaseModel):
    """Simplified Level 02 Response for n8n Workflow Integration"""
    
    success: bool = Field(..., description="Whether analysis was successful")
    claim_id: str = Field(..., description="Claim identifier")
    coverage_decision: str = Field(..., description="NOT_COVERED, COVERED, INFORMATION_REQUIRED")
    confidence_score: float = Field(..., description="Analysis confidence (0.0-1.0)")
    data_source: str = Field(..., description="Source of data (supabase/fallback)")
    
    # Decision-specific routing information
    requires_human_review: bool = Field(..., description="Whether human expert review is required")
    requires_legal_counsel: bool = Field(..., description="Whether legal counsel review is required")
    priority_level: str = Field(..., description="Priority level: LOW, MEDIUM, HIGH, CRITICAL")
    
    # Quick access to key information
    primary_reason: Optional[str] = Field(None, description="Primary reason for coverage decision")
    information_needed_count: Optional[int] = Field(None, description="Number of information requests (if applicable)")
    estimated_resolution_timeline: Optional[str] = Field(None, description="Expected timeline for resolution")
    data_completeness: Optional[float] = Field(None, description="Completeness of Supabase data (0.0-1.0)")
    
    # Error handling
    error_message: Optional[str] = Field(None, description="Error message if analysis failed")

class HealthCheckResponse(BaseModel):
    """Health check response model"""
    status: str = Field(..., description="Service status")
    timestamp: str = Field(..., description="Check timestamp")
    dependencies: Dict[str, str] = Field(..., description="Dependency status")

class ServiceInfoResponse(BaseModel):
    """Service information response model"""
    service_name: str = Field(..., description="Service name")
    version: str = Field(..., description="Service version")
    description: str = Field(..., description="Service description")
    capabilities: List[str] = Field(..., description="Service capabilities")
    exit_paths: List[str] = Field(..., description="Available exit paths")
    dependencies: List[str] = Field(..., description="Service dependencies")

# =====================================================================================
# HELPER FUNCTIONS
# =====================================================================================

async def assess_priority_and_risk_ai(
    claim_id: str, 
    coverage_decision: str, 
    coverage_confidence: float,
    supabase_data: Dict[str, Any], 
    data_completeness: float
) -> PriorityRiskAssessment:
    """Use AI to assess priority and risk instead of hardcoded rules"""
    try:
        # Extract data for AI assessment
        level01_analysis = json.dumps(supabase_data.get("level01_analysis", {})) if supabase_data.get("level01_analysis") else "No Level 01 analysis available"
        claim_details = json.dumps(supabase_data.get("claim_details", {})) if supabase_data.get("claim_details") else "No claim details available"
        
        # Determine claim type from available data
        claim_type = "UNKNOWN"
        if supabase_data.get("level01_analysis"):
            level01 = supabase_data["level01_analysis"]
            if isinstance(level01, dict):
                claim_type = level01.get("claimDetails", {}).get("claimType", 
                           level01.get("claimType", "UNKNOWN"))
        
        # Extract policy information
        policy_info = "No policy information available"
        if supabase_data.get("level01_analysis"):
            level01 = supabase_data["level01_analysis"]
            if isinstance(level01, dict) and level01.get("policyDetails"):
                policy_info = json.dumps(level01["policyDetails"])
        
        # Additional context
        additional_context = f"""
        Data source: {'Supabase' if supabase_data.get('fetched_from_supabase') else 'Fallback'}
        Data completeness score: {data_completeness:.2f}
        Coverage decision confidence: {coverage_confidence:.2f}
        Available OCR texts: {len(supabase_data.get('ocr_texts', []))}
        Available attachments: {len(supabase_data.get('attachments', []))}
        """
        
        # Call AI assessment
        assessment = b.AssessPriorityAndRisk(
            claimId=claim_id,
            claimType=claim_type,
            coverageDecision=coverage_decision,
            coverageConfidence=coverage_confidence,
            level01Analysis=level01_analysis,
            claimDetails=claim_details,
            policyInformation=policy_info,
            dataCompleteness=data_completeness,
            additionalContext=additional_context
        )
        
        logger.info(f"AI priority/risk assessment for claim {claim_id}: {assessment.overallPriorityLevel.value} priority, {assessment.overallRiskScore:.2f} risk score")
        return assessment
        
    except Exception as e:
        logger.error(f"AI priority/risk assessment failed for claim {claim_id}: {str(e)}")
        # Fallback to basic assessment
        from baml_client.types import PriorityLevel
        
        return PriorityRiskAssessment(
            overallPriorityLevel=PriorityLevel.MEDIUM,
            overallRiskScore=0.5,
            priorityDrivers=["AI assessment failed - using fallback"],
            timelineSensitivity="Standard processing timeline",
            stakeholderImpact="Moderate impact if delayed",
            identifiedRisks=[],
            highestRiskCategory="OPERATIONAL",
            riskMitigationPriority=["Review AI assessment failure"],
            requiresHumanReview=True,
            requiresLegalCounsel=False,
            requiresSpecialistReview=False,
            reviewReason="AI assessment system unavailable",
            recommendedProcessingTimeline="Standard 5-10 business days",
            escalationTriggers=["No response within timeline"],
            urgencyJustification="Default priority due to system limitation",
            estimatedFinancialExposure="Unable to assess",
            costOfDelay="Unknown",
            assessmentConfidence=0.3,
            uncertaintyFactors=["AI priority assessment failed"],
            immediateActions=["Manual priority assessment required"],
            processOptimizations=["Investigate AI assessment failure"],
            preventiveRecommendations=["Improve system reliability"]
        )

async def assess_data_completeness_ai(claim_id: str, supabase_data: Dict[str, Any]) -> DataCompletenessAssessment:
    """Use AI to assess data completeness instead of hardcoded weights"""
    try:
        # Extract data for AI assessment
        level01_analysis = json.dumps(supabase_data.get("level01_analysis", {})) if supabase_data.get("level01_analysis") else "No Level 01 analysis available"
        claim_details = json.dumps(supabase_data.get("claim_details", {})) if supabase_data.get("claim_details") else "No claim details available"
        ocr_texts = supabase_data.get("ocr_texts", [])
        attachments = [att.get('file_name', 'Unknown file') for att in supabase_data.get("attachments", [])]
        
        # Determine claim type from available data
        claim_type = "UNKNOWN"
        if supabase_data.get("level01_analysis"):
            level01 = supabase_data["level01_analysis"]
            if isinstance(level01, dict):
                claim_type = level01.get("claimDetails", {}).get("claimType", 
                           level01.get("claimType", "UNKNOWN"))
        
        # Additional context
        additional_context = f"Data source: {'Supabase' if supabase_data.get('fetched_from_supabase') else 'Fallback'}"
        
        # Call AI assessment
        assessment = b.AssessDataCompleteness(
            claimId=claim_id,
            claimType=claim_type,
            level01Analysis=level01_analysis,
            claimDetails=claim_details,
            ocrTexts=ocr_texts,
            attachmentsList=attachments,
            additionalContext=additional_context
        )
        
        logger.info(f"AI data completeness assessment for claim {claim_id}: {assessment.overallCompletenessScore:.2f} (readiness: {assessment.analysisReadiness})")
        return assessment
        
    except Exception as e:
        logger.error(f"AI data completeness assessment failed for claim {claim_id}: {str(e)}")
        # Fallback to simple scoring
        score = 0.0
        if supabase_data.get("level01_analysis"): score += 0.5
        if supabase_data.get("claim_details"): score += 0.2
        if supabase_data.get("ocr_texts"): score += 0.2
        if supabase_data.get("attachments"): score += 0.1
        
        # Create minimal assessment
        return DataCompletenessAssessment(
            overallCompletenessScore=score,
            level01AnalysisCompleteness=1.0 if supabase_data.get("level01_analysis") else 0.0,
            claimDetailsCompleteness=1.0 if supabase_data.get("claim_details") else 0.0,
            ocrDocumentsCompleteness=1.0 if supabase_data.get("ocr_texts") else 0.0,
            attachmentsCompleteness=1.0 if supabase_data.get("attachments") else 0.0,
            level01AnalysisImportance=0.5,
            claimDetailsImportance=0.2,
            ocrDocumentsImportance=0.2,
            attachmentsImportance=0.1,
            criticalMissingItems=[],
            minorMissingItems=[],
            dataQualityScore=score,
            analysisReadiness="NEEDS_CLARIFICATION" if score < 0.7 else "READY",
            improvementRecommendations=[],
            alternativeDataSources=[],
            claimTypeSpecificNeeds="Unable to determine due to AI assessment failure",
            riskFactorsFromGaps=[],
            assessmentConfidence=0.5,
            uncertaintyAreas=["AI assessment failed - using fallback scoring"]
        )

def validate_level01_data(level01_data: Dict[str, Any]) -> bool:
    """Validate Level 01 analysis data structure"""
    if not level01_data:
        return False
        
    required_fields = ['claimId', 'claimType', 'primaryCause']
    
    for field in required_fields:
        if field not in level01_data:
            logger.warning(f"Missing required Level 01 field: {field}")
            return False
    
    return True

def extract_level01_from_supabase(supabase_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Extract Level 01 analysis from Supabase data (comprehensive)"""
    
    # First, try to get the complete Level 1 analysis from the level01_analysis column
    if supabase_data.get('level01_analysis'):
        level01_analysis = supabase_data['level01_analysis']
        logger.info("Using comprehensive Level 1 analysis from Supabase level01_analysis column")
        
        # Extract key fields for Level 02 processing
        level01_data = {
            "claimId": level01_analysis.get('claimId', ''),
            "claimType": level01_analysis.get('claimDetails', {}).get('claimType', ''),
            "primaryCause": level01_analysis.get('causeOfLoss', {}).get('primaryCause', ''),
            "incidentDescription": level01_analysis.get('claimDetails', {}).get('damageDescription', ''),
            "policyNumber": level01_analysis.get('policyDetails', {}).get('policyNumber', ''),
            "claimant": level01_analysis.get('claimDetails', {}).get('claimantName', ''),
            "incidentDate": level01_analysis.get('claimDetails', {}).get('incidentDate', ''),
            "incidentLocation": level01_analysis.get('claimDetails', {}).get('incidentLocation', ''),
            "estimatedLoss": level01_analysis.get('claimDetails', {}).get('estimatedAmount', ''),
            "level01ExitPath": level01_analysis.get('exitPath', 'PROCEED_TO_LEVEL02'),
            "confidenceScore": level01_analysis.get('confidenceScore', 0.8),
            "keyFindings": level01_analysis.get('exitAnalysis', {}).get('analysisProvided', {}).get('identifiedGaps', []),
            
            # Additional rich data from Level 1
            "policyDetails": level01_analysis.get('policyDetails', {}),
            "causeOfLoss": level01_analysis.get('causeOfLoss', {}),
            "contactDetails": level01_analysis.get('contactDetails', []),
            "canadianJurisdiction": level01_analysis.get('canadianJurisdiction', ''),
            "legalConsiderations": level01_analysis.get('legalConsiderations', []),
            "sparkNlpInsights": level01_analysis.get('sparkNlpInsights', {}),
            "dataQualityScore": level01_analysis.get('dataQualityScore', 0.8),
            
            # Complete Level 1 analysis for reference
            "_complete_level01_analysis": level01_analysis
        }
        
        # Remove empty fields
        level01_data = {k: v for k, v in level01_data.items() if v or k.startswith('_')}
        
        return level01_data if validate_level01_data(level01_data) else None
    
    # Fallback: try to extract from basic claim details if no Level 1 analysis available
    claim_data = supabase_data.get('claim_details', {})
    if not claim_data:
        return None
    
    logger.info("No Level 1 analysis found, attempting to extract from basic claim fields")
    
    # Basic extraction from claim table fields
    level01_data = {
        "claimId": claim_data.get('claim_id', ''),
        "claimType": claim_data.get('claim_type', ''),
        "primaryCause": claim_data.get('primary_cause', ''),
        "incidentDescription": claim_data.get('incident_description', ''),
        "policyNumber": claim_data.get('policy_number', ''),
        "claimant": claim_data.get('claimant_name', ''),
        "incidentDate": claim_data.get('incident_date', ''),
        "reportDate": claim_data.get('report_date', ''),
        "estimatedLoss": claim_data.get('estimated_loss', ''),
        "level01ExitPath": "PROCEED_TO_LEVEL02"  # Assume we're at Level 02
    }
    
    # Remove empty fields
    level01_data = {k: v for k, v in level01_data.items() if v}
    
    return level01_data if validate_level01_data(level01_data) else None

def sanitize_text_input(text: str) -> str:
    """Sanitize text input for analysis"""
    if not text or not isinstance(text, str):
        return ""
    
    # Remove potentially problematic characters while preserving content
    sanitized = text.strip()
    
    # Remove null bytes and control characters except newlines and tabs
    sanitized = ''.join(char for char in sanitized if ord(char) >= 32 or char in '\n\t')
    
    # Limit length to prevent excessively long inputs
    max_length = 50000  # Reasonable limit for insurance claim text
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length] + "... [TRUNCATED]"
        logger.warning(f"Input text truncated to {max_length} characters")
    
    return sanitized

# =====================================================================================
# FACT-CHECKING AND VALIDATION FUNCTIONS (ANTI-HALLUCINATION)
# =====================================================================================

def validate_ai_response_for_hallucination(baml_result: Level02CoverageAnalysis, supabase_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate AI response for potential hallucination and flag suspicious content
    """
    validation_results = {
        "is_valid": True,
        "warnings": [],
        "critical_issues": [],
        "confidence_adjustment": 0.0
    }
    
    try:
        # Check for fabricated exclusions
        if hasattr(baml_result.exclusionAnalysis, 'applicableExclusions'):
            for exclusion in baml_result.exclusionAnalysis.applicableExclusions:
                if hasattr(exclusion, 'exclusionType'):
                    # Flag common hallucinated exclusions
                    suspicious_exclusions = [
                        "Water Intrusion Exclusion",
                        "Water Damage Exclusion", 
                        "Flood Exclusion",
                        "Maintenance Exclusion"
                    ]
                    if exclusion.exclusionType in suspicious_exclusions:
                        validation_results["warnings"].append(
                            f"Suspicious exclusion cited: {exclusion.exclusionType}. Requires policy document verification."
                        )
                        validation_results["confidence_adjustment"] -= 0.2
        
        # Check for fabricated legal precedents
        if hasattr(baml_result.canadianLegalAnalysis, 'legalPrecedents'):
            for precedent in baml_result.canadianLegalAnalysis.legalPrecedents:
                if hasattr(precedent, 'caseName'):
                    # Flag obviously fabricated case names
                    suspicious_patterns = ["XYZ", "ABC", "Sample", "Example", "Test"]
                    if any(pattern in precedent.caseName for pattern in suspicious_patterns):
                        validation_results["critical_issues"].append(
                            f"Fabricated legal precedent detected: {precedent.caseName}. This appears to be AI hallucination."
                        )
                        validation_results["is_valid"] = False
                        validation_results["confidence_adjustment"] -= 0.4
        
        # Check for impossible confidence levels
        if baml_result.confidenceScore > 0.95 and not supabase_data.get("fetched_from_supabase", False):
            validation_results["warnings"].append(
                "High confidence score without complete data source. Confidence may be inflated."
            )
            validation_results["confidence_adjustment"] -= 0.1
        
        # Check for policy language citations without policy documents
        policy_docs_available = bool(supabase_data.get("claim_details", {}).get("policy_documents"))
        if hasattr(baml_result.coverageJustification, 'policyBasis') and not policy_docs_available:
            validation_results["warnings"].append(
                "Policy language cited without available policy documents. Requires verification."
            )
            validation_results["confidence_adjustment"] -= 0.15
            
    except Exception as e:
        logger.error(f"Error in hallucination validation: {str(e)}")
        validation_results["warnings"].append("Validation check failed - manual review recommended")
    
    return validation_results

def apply_fact_checking_constraints(baml_result: Level02CoverageAnalysis, validation_results: Dict[str, Any]) -> Level02CoverageAnalysis:
    """
    Apply fact-checking constraints and adjust confidence based on validation results
    """
    try:
        # Adjust confidence score based on validation
        confidence_adjustment = validation_results.get("confidence_adjustment", 0.0)
        if confidence_adjustment != 0.0:
            original_confidence = baml_result.confidenceScore
            adjusted_confidence = max(0.1, min(1.0, original_confidence + confidence_adjustment))
            baml_result.confidenceScore = adjusted_confidence
            logger.info(f"Confidence adjusted from {original_confidence:.2f} to {adjusted_confidence:.2f} due to validation findings")
        
        # Add validation warnings to analysis quality
        if hasattr(baml_result, 'analysisQuality') and baml_result.analysisQuality:
            existing_notes = baml_result.analysisQuality.qualityNotes or []
            validation_warnings = validation_results.get("warnings", [])
            baml_result.analysisQuality.qualityNotes = existing_notes + validation_warnings
        
        # Force human review if critical issues found
        if validation_results.get("critical_issues"):
            if hasattr(baml_result.exitAnalysis, 'humanReviewRequired'):
                baml_result.exitAnalysis.humanReviewRequired = True
            logger.warning(f"Critical validation issues found - forcing human review: {validation_results['critical_issues']}")
            
    except Exception as e:
        logger.error(f"Error applying fact-checking constraints: {str(e)}")
    
    return baml_result

def check_response_consistency(baml_result: Level02CoverageAnalysis) -> Dict[str, Any]:
    """
    Check for internal consistency in the AI response
    """
    consistency_check = {
        "is_consistent": True,
        "inconsistencies": []
    }
    
    try:
        # Check coverage decision consistency
        coverage_decision = baml_result.coverageDecision
        exit_path = baml_result.exitAnalysis.exitPath if hasattr(baml_result.exitAnalysis, 'exitPath') else None
        
        if coverage_decision and exit_path:
            if coverage_decision != exit_path:
                consistency_check["is_consistent"] = False
                consistency_check["inconsistencies"].append(
                    f"Coverage decision ({coverage_decision}) doesn't match exit path ({exit_path})"
                )
        
        # Check confidence vs risk assessment alignment
        confidence = baml_result.confidenceScore
        if hasattr(baml_result.riskAssessment, 'overallRisk'):
            risk_level = baml_result.riskAssessment.overallRisk
            if confidence > 0.9 and risk_level in ["HIGH", "CRITICAL"]:
                consistency_check["inconsistencies"].append(
                    f"High confidence ({confidence:.2f}) with high risk level ({risk_level}) seems inconsistent"
                )
                
    except Exception as e:
        logger.error(f"Error in consistency check: {str(e)}")
        consistency_check["inconsistencies"].append("Consistency check failed")
    
    return consistency_check

def prepare_baml_input(request: Level02CoverageRequest, supabase_data: Dict[str, Any]) -> Level02AnalysisInput:
    """Prepare and validate input for BAML analysis using comprehensive Supabase data"""
    
    # Extract Level 01 data from Supabase (prioritizing level01_analysis column)
    level01_data = extract_level01_from_supabase(supabase_data)
    
    # Use fallback if Supabase data unavailable
    if not level01_data and request.fallback_level01_analysis:
        level01_data = request.fallback_level01_analysis
        if not validate_level01_data(level01_data):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid fallback Level 01 analysis data structure"
            )
    
    if not level01_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No valid Level 01 analysis data available from Supabase or fallback"
        )
    
    # Prepare policy documents from multiple sources
    policy_documents = []
    
    # From Level 1 analysis policy details
    if level01_data.get("policyDetails"):
        policy_details = level01_data["policyDetails"]
        policy_info = f"""
        Policy Number: {policy_details.get('policyNumber', '')}
        Policy Holder: {policy_details.get('policyHolder', '')}
        Coverage Types: {', '.join(policy_details.get('coverageTypes', []))}
        Policy Limits: {policy_details.get('policyLimits', '')}
        Deductible: {policy_details.get('deductibleAmount', '')}
        Effective: {policy_details.get('effectiveDate', '')} to {policy_details.get('expiryDate', '')}
        Insurance Company: {policy_details.get('insuranceCompany', '')}
        Agent: {policy_details.get('agentDetails', '')}
        """
        policy_documents.append(policy_info.strip())
    
    # From OCR texts
    if supabase_data.get("ocr_texts"):
        policy_documents.extend(supabase_data["ocr_texts"])
    
    # From fallback
    if request.fallback_policy_documents:
        policy_documents.extend(request.fallback_policy_documents)
    
    # Sanitize all text inputs
    sanitized_policy_docs = [
        sanitize_text_input(doc) for doc in policy_documents if doc
    ]
    
    # Prepare additional evidence from multiple sources
    additional_evidence = []
    
    # From Level 1 analysis
    if level01_data.get("causeOfLoss"):
        cause_info = level01_data["causeOfLoss"]
        cause_evidence = f"""
        Primary Cause: {cause_info.get('primaryCause', '')}
        Contributing Factors: {', '.join(cause_info.get('contributingFactors', []))}
        Incident Type: {cause_info.get('incidentType', '')}
        At-Fault Parties: {', '.join(cause_info.get('atFaultParties', []))}
        Circumstances: {cause_info.get('circumstances', '')}
        Negligence Factors: {', '.join(cause_info.get('negligenceFactors', []))}
        Causation Chain: {' → '.join(cause_info.get('causationChain', []))}
        """
        additional_evidence.append(cause_evidence.strip())
    
    # From Canadian jurisdiction and legal considerations
    if level01_data.get("canadianJurisdiction"):
        legal_evidence = f"""
        Canadian Jurisdiction: {level01_data['canadianJurisdiction']}
        Legal Considerations: {', '.join(level01_data.get('legalConsiderations', []))}
        """
        additional_evidence.append(legal_evidence.strip())
    
    # From SparkNLP insights if available
    if level01_data.get("sparkNlpInsights"):
        nlp_insights = level01_data["sparkNlpInsights"]
        if nlp_insights.get("enhancedEntities"):
            entities_info = "Enhanced NLP Entities: " + "; ".join([
                f"{entity.get('text', '')} ({entity.get('entityType', '')})"
                for entity in nlp_insights["enhancedEntities"]
            ])
            additional_evidence.append(entities_info)
    
    # From attachments
    if supabase_data.get("attachments"):
        for attachment in supabase_data["attachments"]:
            if attachment.get("file_name"):
                additional_evidence.append(f"Document: {attachment['file_name']}")
            if attachment.get("ocr_text"):
                additional_evidence.append(f"Content: {attachment['ocr_text'][:500]}...")
    
    sanitized_evidence = [
        sanitize_text_input(evidence) for evidence in additional_evidence if evidence
    ]
    
    # Create Level01Summary with comprehensive data
    level01_summary = Level01Summary(
        claimId=level01_data.get('claimId', request.claim_id),
        claimType=level01_data.get('claimType', 'Unknown'),
        policyNumber=level01_data.get('policyNumber', ''),
        incidentDate=level01_data.get('incidentDate', ''),
        primaryCause=level01_data.get('primaryCause', ''),
        level01Confidence=level01_data.get('confidenceScore', 0.8),
        level01ExitPath=level01_data.get('level01ExitPath', 'PROCEED_TO_LEVEL02'),
        keyFindings=level01_data.get('keyFindings', [])
    )
    
    # Prepare processing notes with data source information
    processing_notes = [
        f"Data source: {'Supabase' if supabase_data.get('fetched_from_supabase') else 'Fallback'}",
        f"Level 1 analysis available: {'Yes' if supabase_data.get('level01_analysis') else 'No'}",
        f"Data quality score: {level01_data.get('dataQualityScore', 'Unknown')}"
    ]
    
    if level01_data.get("_complete_level01_analysis"):
        processing_notes.append("Complete Level 1 analysis data available")
    
    # Create the full BAML input
    baml_input = Level02AnalysisInput(
        claimId=request.claim_id,
        level01Analysis=level01_summary,
        policyDocuments=sanitized_policy_docs,
        additionalEvidence=sanitized_evidence,
        humanInputs=[],  # Will be populated from Supabase in future enhancement
        processingNotes=processing_notes,
        urgencyLevel=request.urgency_level,
        specialInstructions=request.special_instructions or []
    )
    
    return baml_input

def process_baml_result(baml_result: Level02CoverageAnalysis, processing_time_ms: int) -> Level02CoverageResponse:
    """Process BAML analysis result into API response"""
    
    try:
        # Convert BAML result to dictionary for JSON serialization
        analysis_dict = {
            "coverageDecision": baml_result.coverageDecision.value,
            "confidenceScore": baml_result.confidenceScore,
            "policyAnalysis": baml_result.policyAnalysis.__dict__ if baml_result.policyAnalysis else None,
            "exclusionAnalysis": baml_result.exclusionAnalysis.__dict__ if baml_result.exclusionAnalysis else None,
            "coverageMapping": baml_result.coverageMapping.__dict__ if baml_result.coverageMapping else None,
            "causeMapping": baml_result.causeMapping.__dict__ if baml_result.causeMapping else None,
            "coverageJustification": baml_result.coverageJustification.__dict__ if baml_result.coverageJustification else None,
            "decisionSupport": baml_result.decisionSupport.__dict__ if baml_result.decisionSupport else None,
            "canadianLegalAnalysis": baml_result.canadianLegalAnalysis.__dict__ if baml_result.canadianLegalAnalysis else None,
            "exitAnalysis": baml_result.exitAnalysis.__dict__ if baml_result.exitAnalysis else None,
            "analysisQuality": baml_result.analysisQuality.__dict__ if baml_result.analysisQuality else None,
            "riskAssessment": baml_result.riskAssessment.__dict__ if baml_result.riskAssessment else None,
            "uncertaintyAreas": [area.__dict__ for area in baml_result.uncertaintyAreas] if baml_result.uncertaintyAreas else [],
            "level01Data": baml_result.level01Data.__dict__ if baml_result.level01Data else None,
            "analysisTimestamp": baml_result.analysisTimestamp,
            "processingTimeMs": baml_result.processingTimeMs,
            "modelVersion": baml_result.modelVersion,
            "analystId": baml_result.analystId
        }
        
        # Extract information requests if applicable
        information_requests = []
        if baml_result.coverageDecision == CoverageDecision.INFORMATION_REQUIRED:
            if baml_result.allInformationRequests:
                information_requests = [req.__dict__ for req in baml_result.allInformationRequests]
        
        return Level02CoverageResponse(
            success=True,
            claim_id=baml_result.level01Data.claimId if baml_result.level01Data else "unknown",
            processing_time_ms=processing_time_ms,
            timestamp=datetime.utcnow().isoformat(),
            data_source="pending",  # Will be set by caller
            coverage_decision=baml_result.coverageDecision.value,
            confidence_score=baml_result.confidenceScore,
            analysis_results=analysis_dict,
            coverage_justification=baml_result.coverageJustification.__dict__ if baml_result.coverageJustification else None,
            information_requests=information_requests,
            next_steps=baml_result.exitAnalysis.nextStepsRequired.__dict__ if baml_result.exitAnalysis and baml_result.exitAnalysis.nextStepsRequired else None,
            risk_assessment=baml_result.riskAssessment.__dict__ if baml_result.riskAssessment else None,
            analysis_quality=baml_result.analysisQuality.__dict__ if baml_result.analysisQuality else None,
            uncertainty_areas=[area.__dict__ for area in baml_result.uncertaintyAreas] if baml_result.uncertaintyAreas else [],
            human_review_required=baml_result.exitAnalysis.humanReviewRequired if baml_result.exitAnalysis else False,
            legal_counsel_required=baml_result.exitAnalysis.legalCounselRequired if baml_result.exitAnalysis else False,
            warnings=[]
        )
        
    except Exception as e:
        logger.error(f"Error processing BAML result: {str(e)}")
        raise

def create_simple_response(baml_result: Level02CoverageAnalysis, claim_id: str) -> Level02SimpleResponse:
    """Create simplified response for n8n workflow integration"""
    
    try:
        return Level02SimpleResponse(
            success=True,
            claim_id=claim_id,
            coverage_decision=baml_result.coverageDecision.value,
            confidence_score=baml_result.confidenceScore,
            data_source="pending",  # Will be set by caller
            requires_human_review=baml_result.exitAnalysis.humanReviewRequired if baml_result.exitAnalysis else False,
            requires_legal_counsel=baml_result.exitAnalysis.legalCounselRequired if baml_result.exitAnalysis else False,
            priority_level=baml_result.exitAnalysis.priorityLevel.value if baml_result.exitAnalysis and baml_result.exitAnalysis.priorityLevel else "NORMAL",
            primary_reason=baml_result.coverageJustification.primaryReason.value if baml_result.coverageJustification and baml_result.coverageJustification.primaryReason else None,
            information_needed_count=len(baml_result.allInformationRequests) if baml_result.allInformationRequests else 0,
            estimated_resolution_timeline=baml_result.exitAnalysis.timelineForResolution if baml_result.exitAnalysis else None
        )
        
    except Exception as e:
        logger.error(f"Error creating simple response: {str(e)}")
        return Level02SimpleResponse(
            success=False,
            claim_id=claim_id,
            coverage_decision="ERROR",
            confidence_score=0.0,
            data_source="error",
            requires_human_review=True,
            requires_legal_counsel=True,
            priority_level="CRITICAL",
            error_message=f"Analysis processing error: {str(e)}"
        )

# =====================================================================================
# API ENDPOINTS
# =====================================================================================

@router.post("/", response_model=Level02CoverageResponse)
async def analyze_coverage_level02(request: Level02CoverageRequest):
    """
    Comprehensive Level 02 Coverage Analysis
    
    Determines coverage for insurance claims using:
    - Comprehensive policy analysis 
            - AI-driven risk and priority assessment
    - Three intelligent exit paths: NOT_COVERED, COVERED, INFORMATION_REQUIRED
    - Detailed justification for all decisions
    
    Returns complete analysis with risk assessment and next steps.
    """
    
    start_time = time.time()
    
    try:
        logger.info(f"Starting Level 02 coverage analysis for claim: {request.claim_id}")
        
        # Fetch claim data from Supabase
        supabase_data = await supabase_service.fetch_claim_data(request.claim_id)
        data_source = "supabase" if supabase_data.get("fetched_from_supabase") else "fallback"
        
        logger.info(f"Fetched data from {data_source} for claim {request.claim_id}")
        
        # Use AI-driven data completeness assessment
        completeness_assessment = await assess_data_completeness_ai(request.claim_id, supabase_data)
        
        logger.info(f"AI-driven data completeness for claim {request.claim_id}: {completeness_assessment.overallCompletenessScore:.2f} (readiness: {completeness_assessment.analysisReadiness})")
        
        # Prepare BAML input with Supabase data
        baml_input = prepare_baml_input(request, supabase_data)
        
        logger.info(f"Prepared BAML input for claim {request.claim_id}")
        
        # Execute BAML analysis (synchronous, not async)
        # Extract individual parameters from baml_input for the new function signature
        level01 = baml_input.level01Analysis
        policy_docs_text = "\n\n".join(baml_input.policyDocuments) if baml_input.policyDocuments else "No policy documents available"
        additional_evidence_text = "\n\n".join(baml_input.additionalEvidence) if baml_input.additionalEvidence else "No additional evidence available"
        canadian_jurisdiction = "Unknown"
        
        # Try to extract Canadian jurisdiction from the evidence
        for evidence in baml_input.additionalEvidence:
            if "Canadian Jurisdiction:" in evidence:
                canadian_jurisdiction = evidence.split("Canadian Jurisdiction:")[1].split("\n")[0].strip()
                break
        
        baml_result = b.AnalyzeCoverageLevel02(
            claimId=level01.claimId,
            claimType=level01.claimType,
            policyNumber=level01.policyNumber,
            incidentDate=level01.incidentDate,
            primaryCause=level01.primaryCause,
            level01Confidence=level01.level01Confidence,
            level01ExitPath=level01.level01ExitPath,
            keyFindings=level01.keyFindings,
            policyDocuments=policy_docs_text,
            additionalEvidence=additional_evidence_text,
            canadianJurisdiction=canadian_jurisdiction
        )
        
        # Apply fact-checking and validation to prevent hallucination
        validation_results = validate_ai_response_for_hallucination(baml_result, supabase_data)
        consistency_check = check_response_consistency(baml_result)
        
        # Apply constraints and adjustments based on validation
        baml_result = apply_fact_checking_constraints(baml_result, validation_results)
        
        # Log validation results
        if not validation_results["is_valid"]:
            logger.error(f"AI hallucination detected for claim {request.claim_id}: {validation_results['critical_issues']}")
        if validation_results["warnings"]:
            logger.warning(f"AI validation warnings for claim {request.claim_id}: {validation_results['warnings']}")
        if not consistency_check["is_consistent"]:
            logger.warning(f"AI response inconsistencies for claim {request.claim_id}: {consistency_check['inconsistencies']}")
        
        # Use AI-driven priority and risk assessment
        priority_risk_assessment = await assess_priority_and_risk_ai(
            request.claim_id,
            baml_result.coverageDecision.value,
            baml_result.confidenceScore,
            supabase_data,
            completeness_assessment.overallCompletenessScore
        )
        
        # Calculate processing time
        processing_time_ms = int((time.time() - start_time) * 1000)
        
        logger.info(f"Completed Level 02 analysis for claim {request.claim_id} in {processing_time_ms}ms")
        logger.info(f"Coverage decision: {baml_result.coverageDecision.value}, Confidence: {baml_result.confidenceScore}")
        logger.info(f"AI Priority assessment: {priority_risk_assessment.overallPriorityLevel.value}, Risk: {priority_risk_assessment.overallRiskScore:.2f}")
        
        # Process result into API response
        response = process_baml_result(baml_result, processing_time_ms)
        
        # Override with AI-driven assessments
        response.human_review_required = priority_risk_assessment.requiresHumanReview
        response.legal_counsel_required = priority_risk_assessment.requiresLegalCounsel
        response.data_source = data_source
        response.data_completeness = completeness_assessment.overallCompletenessScore
        response.missing_data_items = completeness_assessment.criticalMissingItems + completeness_assessment.minorMissingItems
        response.supabase_data = {
            "ai_assessment": {
                "analysis_readiness": completeness_assessment.analysisReadiness,
                "data_quality_score": completeness_assessment.dataQualityScore,
                "assessment_confidence": completeness_assessment.assessmentConfidence,
                "claim_type_needs": completeness_assessment.claimTypeSpecificNeeds,
                "improvement_recommendations": completeness_assessment.improvementRecommendations
            },
            "component_scores": {
                "level01_analysis": completeness_assessment.level01AnalysisCompleteness,
                "claim_details": completeness_assessment.claimDetailsCompleteness,
                "ocr_documents": completeness_assessment.ocrDocumentsCompleteness,
                "attachments": completeness_assessment.attachmentsCompleteness
            },
            "traditional_metrics": {
                "level01_analysis_available": supabase_data.get("level01_analysis") is not None,
                "claim_details_available": supabase_data.get("claim_details") is not None,
                "ocr_texts_count": len(supabase_data.get("ocr_texts", [])),
                "attachments_count": len(supabase_data.get("attachments", []))
            }
        }
        
        # Add AI-driven priority and risk assessment to response
        response.risk_assessment = {
            "ai_priority_assessment": {
                "priority_level": priority_risk_assessment.overallPriorityLevel.value,
                "risk_score": priority_risk_assessment.overallRiskScore,
                "priority_drivers": priority_risk_assessment.priorityDrivers,
                "timeline_sensitivity": priority_risk_assessment.timelineSensitivity,
                "stakeholder_impact": priority_risk_assessment.stakeholderImpact,
                "review_requirements": {
                    "human_review": priority_risk_assessment.requiresHumanReview,
                    "legal_counsel": priority_risk_assessment.requiresLegalCounsel,
                    "specialist_review": priority_risk_assessment.requiresSpecialistReview,
                    "review_reason": priority_risk_assessment.reviewReason
                },
                "timeline_recommendations": {
                    "processing_timeline": priority_risk_assessment.recommendedProcessingTimeline,
                    "escalation_triggers": priority_risk_assessment.escalationTriggers,
                    "urgency_justification": priority_risk_assessment.urgencyJustification
                },
                "financial_assessment": {
                    "estimated_exposure": priority_risk_assessment.estimatedFinancialExposure,
                    "cost_of_delay": priority_risk_assessment.costOfDelay
                },
                "risk_factors": [
                    {
                        "category": risk.category.value,
                        "severity": risk.severity,
                        "description": risk.description,
                        "likelihood": risk.likelihood,
                        "potential_impact": risk.potentialImpact,
                        "mitigation_strategy": risk.mitigationStrategy
                    } for risk in priority_risk_assessment.identifiedRisks
                ],
                "assessment_quality": {
                    "confidence": priority_risk_assessment.assessmentConfidence,
                    "uncertainty_factors": priority_risk_assessment.uncertaintyFactors
                },
                "recommendations": {
                    "immediate_actions": priority_risk_assessment.immediateActions,
                    "process_optimizations": priority_risk_assessment.processOptimizations,
                    "preventive_recommendations": priority_risk_assessment.preventiveRecommendations
                }
            }
        }
        
        return response
        
    except ValueError as ve:
        logger.error(f"Validation error for claim {request.claim_id}: {str(ve)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Input validation error: {str(ve)}"
        )
        
    except Exception as e:
        processing_time_ms = int((time.time() - start_time) * 1000)
        logger.error(f"Error analyzing coverage for claim {request.claim_id}: {str(e)}")
        
        return Level02CoverageResponse(
            success=False,
            claim_id=request.claim_id,
            processing_time_ms=processing_time_ms,
            timestamp=datetime.utcnow().isoformat(),
            data_source="error",
            coverage_decision="ERROR",
            confidence_score=0.0,
            error_message=f"Analysis failed: {str(e)}"
        )

@router.post("/simple", response_model=Level02SimpleResponse)
async def analyze_coverage_level02_simple(request: Level02CoverageRequest):
    """
    Simplified Level 02 Coverage Analysis for n8n Workflow Integration
    
    Returns essential coverage decision information for workflow routing:
    - Coverage decision (NOT_COVERED, COVERED, INFORMATION_REQUIRED)
    - Confidence score and priority level
    - Human review and legal counsel requirements
    - Basic routing information for workflow automation
    
    Optimized for n8n workflow integration with minimal response payload.
    """
    
    start_time = time.time()
    
    try:
        logger.info(f"Starting Level 02 simple analysis for claim: {request.claim_id}")
        
        # Fetch claim data from Supabase
        supabase_data = await supabase_service.fetch_claim_data(request.claim_id)
        data_source = "supabase" if supabase_data.get("fetched_from_supabase") else "fallback"
        
        logger.info(f"Fetched data from {data_source} for claim {request.claim_id}")
        
        # Use AI-driven data completeness assessment (same as detailed endpoint)
        completeness_assessment = await assess_data_completeness_ai(request.claim_id, supabase_data)
        
        logger.info(f"AI-driven data completeness for claim {request.claim_id}: {completeness_assessment.overallCompletenessScore:.2f}")
        
        # Prepare BAML input with Supabase data
        baml_input = prepare_baml_input(request, supabase_data)
        
        # Execute BAML analysis (synchronous, not async)  
        # Extract individual parameters from baml_input for the new function signature
        level01 = baml_input.level01Analysis
        policy_docs_text = "\n\n".join(baml_input.policyDocuments) if baml_input.policyDocuments else "No policy documents available"
        additional_evidence_text = "\n\n".join(baml_input.additionalEvidence) if baml_input.additionalEvidence else "No additional evidence available"
        canadian_jurisdiction = "Unknown"
        
        # Try to extract Canadian jurisdiction from the evidence
        for evidence in baml_input.additionalEvidence:
            if "Canadian Jurisdiction:" in evidence:
                canadian_jurisdiction = evidence.split("Canadian Jurisdiction:")[1].split("\n")[0].strip()
                break
        
        baml_result = b.AnalyzeCoverageLevel02(
            claimId=level01.claimId,
            claimType=level01.claimType,
            policyNumber=level01.policyNumber,
            incidentDate=level01.incidentDate,
            primaryCause=level01.primaryCause,
            level01Confidence=level01.level01Confidence,
            level01ExitPath=level01.level01ExitPath,
            keyFindings=level01.keyFindings,
            policyDocuments=policy_docs_text,
            additionalEvidence=additional_evidence_text,
            canadianJurisdiction=canadian_jurisdiction
        )
        
        # Apply fact-checking and validation to prevent hallucination (same as detailed endpoint)
        validation_results = validate_ai_response_for_hallucination(baml_result, supabase_data)
        consistency_check = check_response_consistency(baml_result)
        baml_result = apply_fact_checking_constraints(baml_result, validation_results)
        
        # Log validation results for simple endpoint
        if not validation_results["is_valid"] or validation_results["warnings"] or not consistency_check["is_consistent"]:
            logger.warning(f"AI validation issues in simple analysis for claim {request.claim_id}")
        
        # Use AI-driven priority and risk assessment (same as detailed endpoint)
        priority_risk_assessment = await assess_priority_and_risk_ai(
            request.claim_id,
            baml_result.coverageDecision.value,
            baml_result.confidenceScore,
            supabase_data,
            completeness_assessment.overallCompletenessScore
        )
        
        processing_time_ms = int((time.time() - start_time) * 1000)
        
        logger.info(f"Completed Level 02 simple analysis for claim {request.claim_id} in {processing_time_ms}ms")
        logger.info(f"Coverage decision: {baml_result.coverageDecision.value}, AI Priority: {priority_risk_assessment.overallPriorityLevel.value}")
        
        # Create simplified response with AI-driven assessments
        response = create_simple_response(baml_result, request.claim_id)
        response.data_source = data_source
        response.data_completeness = completeness_assessment.overallCompletenessScore
        
        # Override with AI-driven assessments
        response.requires_human_review = priority_risk_assessment.requiresHumanReview
        response.requires_legal_counsel = priority_risk_assessment.requiresLegalCounsel
        response.priority_level = priority_risk_assessment.overallPriorityLevel.value
        
        return response
        
    except ValueError as ve:
        logger.error(f"Validation error for claim {request.claim_id}: {str(ve)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Input validation error: {str(ve)}"
        )
        
    except Exception as e:
        logger.error(f"Error in simple analysis for claim {request.claim_id}: {str(e)}")
        
        return Level02SimpleResponse(
            success=False,
            claim_id=request.claim_id,
            coverage_decision="ERROR",
            confidence_score=0.0,
            data_source="error",
            requires_human_review=True,
            requires_legal_counsel=True,
            priority_level="CRITICAL",
            error_message=f"Analysis failed: {str(e)}"
        )

@router.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """
    Level 02 Coverage Analysis Service Health Check
    
    Verifies service health and dependency status.
    """
    
    try:
        # Test BAML client availability
        baml_status = "healthy"
        try:
            # Quick test with minimal input
            test_input = Level02AnalysisInput(
                claimId="health-check",
                level01Analysis=Level01Summary(
                    claimId="health-check",
                    claimType="test",
                    policyNumber="test",
                    incidentDate="2024-01-01",
                    primaryCause="test",
                    level01Confidence=1.0,
                    level01ExitPath="PROCEED_TO_LEVEL02",
                    keyFindings=["health check"]
                ),
                policyDocuments=["health check"],
                additionalEvidence=[],
                humanInputs=[],
                processingNotes=[],
                urgencyLevel="LOW",
                specialInstructions=[]
            )
            
            # This will test BAML client connectivity using new function signature
            result = b.AnalyzeCoverageLevel02(
                claimId="health-check",
                claimType="test",
                policyNumber="test",
                incidentDate="2024-01-01",
                primaryCause="test",
                level01Confidence=1.0,
                level01ExitPath="PROCEED_TO_LEVEL02",
                keyFindings=["health check"],
                policyDocuments="health check policy documents",
                additionalEvidence="health check evidence",
                canadianJurisdiction="Ontario"
            )
            if result:
                baml_status = "healthy"
        except Exception as e:
            baml_status = f"error: {str(e)}"
        
        return HealthCheckResponse(
            status="healthy",
            timestamp=datetime.utcnow().isoformat(),
            dependencies={
                "baml_client": baml_status,
                "openai_gpt4o": "healthy" if baml_status == "healthy" else "unknown"
            }
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Service unhealthy: {str(e)}"
        )

@router.get("/info", response_model=ServiceInfoResponse) 
async def service_info():
    """
    Level 02 Coverage Analysis Service Information
    
    Returns comprehensive service capabilities and configuration.
    """
    
    return ServiceInfoResponse(
        service_name="Zurich Level 02 Coverage Analysis",
        version="1.0.0",
        description="Comprehensive insurance coverage determination with Canadian legal analysis",
        capabilities=[
            "Policy coverage analysis with detailed justification",
                        "AI-driven exclusion analysis",
            "Intelligent risk assessment and priority scoring",
            "Three-path intelligent routing (NOT_COVERED, COVERED, INFORMATION_REQUIRED)",
            "Information requirement analysis with source identification",
            "Risk assessment and human review recommendations",
            "Quality metrics and uncertainty identification",
            "n8n workflow integration support"
        ],
        exit_paths=[
            "NOT_COVERED - Policy exclusions apply or conditions not met",
            "COVERED - Policy terms clearly cover the loss",
            "INFORMATION_REQUIRED - Additional information needed for determination"
        ],
        dependencies=[
            "BAML Client (Level 02 Coverage Analysis)",
            "OpenAI GPT-4o (Primary Analysis Engine)",
            "Level 01 Analysis Results (Required Input)",
            "AI Risk Assessment and Priority Scoring"
        ]
    )

# =====================================================================================
# NOTE: Exception handlers are registered at the FastAPI app level in main.py
# APIRouter does not support exception_handler decorators
# ===================================================================================== 