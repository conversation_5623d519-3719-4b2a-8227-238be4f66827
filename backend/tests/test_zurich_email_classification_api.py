"""
ZURICH EMAIL CLASSIFICATION API - COMPREHENSIVE TEST SUITE
========================================================

Purpose: Test the email classification API with various scenarios
Features: Unit tests, integration tests, edge cases, and debugging utilities
Usage: Run with pytest or execute individual test scenarios

Test Categories:
1. Valid claim emails (auto, property, liability)
2. Non-claim emails (marketing, spam, general inquiries)
3. Attachment mismatch scenarios
4. High-risk and urgent claims
5. Canadian jurisdiction tests
6. Error handling and edge cases
"""

import asyncio
import pytest
import json
import requests
from datetime import datetime
from typing import Dict, Any, List

# Test configuration
API_BASE_URL = "http://localhost:8000"  # Adjust for your deployment
TEST_REQUEST_TIMEOUT = 30  # seconds

# ================================================================================================
# TEST DATA SCENARIOS
# ================================================================================================

class TestEmailScenarios:
    """Collection of email test scenarios for classification testing"""
    
    @staticmethod
    def valid_auto_claim_with_attachments():
        """Clear auto insurance claim with proper attachments"""
        return {
            "subject": "Car Accident - Need to File Claim - Policy #AC123456",
            "body": """Hi,
            
I was in a car accident yesterday on Highway 401 in Toronto, Ontario. The other driver ran a red light and hit my vehicle. I have photos of the damage and a police report number #TO-2024-001234.

My car has significant front-end damage and needs towing. The other driver admitted fault at the scene. There was one witness who provided a statement to police.

I'm attaching photos of the damage and the police report. Please let me know what other documents you need to process my claim.

Policy Number: AC123456789
Incident Date: March 15, 2024
Location: Highway 401, Toronto, ON

Thank you,
John Doe
(416) 555-0123""",
            "senderEmail": "<EMAIL>",
            "senderName": "John Doe",
            "attachments": [
                {
                    "filename": "accident_photos.zip",
                    "contentType": "application/zip",
                    "size": 2048000
                },
                {
                    "filename": "police_report_TO-2024-001234.pdf",
                    "contentType": "application/pdf",
                    "size": 512000
                }
            ]
        }
    
    @staticmethod
    def mentions_attachments_but_none_provided():
        """Email mentions attachments but none are provided - should trigger REQUEST_ATTACHMENTS"""
        return {
            "subject": "Property Damage Claim - Water Damage",
            "body": """Dear Zurich Claims Team,
            
My basement flooded last week due to a burst pipe in my home in Calgary, Alberta. The damage is extensive - carpeting, drywall, and some furniture were damaged.

I have taken photos of all the damage and obtained repair estimates from three contractors. Please see the attached documents for a complete damage assessment and cost estimates.

The incident occurred on March 10, 2024, and I immediately contacted a plumber to stop the leak. I also contacted a restoration company to begin the drying process.

Policy Number: PR987654321
Estimated Damage: $15,000 - $20,000

Please process this claim urgently as I need to begin repairs.

Best regards,
Jane Smith""",
            "senderEmail": "<EMAIL>",
            "senderName": "Jane Smith",
            "attachments": []  # No attachments despite mentioning them
        }
    
    @staticmethod
    def not_claim_related_marketing():
        """Marketing email that should be ignored"""
        return {
            "subject": "Special Offer: 20% Off Your Next Insurance Premium!",
            "body": """Dear Valued Customer,
            
We're excited to offer you an exclusive 20% discount on your next insurance premium renewal!

This limited-time offer includes:
- Auto insurance savings
- Home insurance discounts
- Bundle deals for multiple policies

To take advantage of this offer, simply:
1. Visit our website at www.insurance-deals.com
2. Enter promo code SAVE20
3. Complete your renewal online

This offer expires March 31, 2024. Don't miss out!

For questions, call our customer service at 1-800-INSURANCE.

Best regards,
Marketing Team
Special Offers Insurance Company""",
            "senderEmail": "<EMAIL>",
            "senderName": "Marketing Team",
            "attachments": []
        }
    
    @staticmethod
    def high_risk_liability_claim():
        """High-risk liability claim requiring human review"""
        return {
            "subject": "URGENT: Serious Construction Site Injury - Multiple Parties",
            "body": """URGENT NOTIFICATION - SERIOUS INCIDENT
            
This is to notify you of a serious workplace accident at our construction site in Vancouver, British Columbia.

INCIDENT DETAILS:
- Date: March 14, 2024
- Time: 2:30 PM PST
- Location: Downtown Vancouver Construction Site, 123 Main Street
- Project: 40-story residential tower

INJURIES:
A worker fell from scaffolding on the 15th floor and has been hospitalized with critical injuries including:
- Multiple fractures
- Head trauma
- Internal injuries
- Currently in ICU at Vancouver General Hospital

PARTIES INVOLVED:
- Injured Worker: Michael Johnson (employee)
- Construction Company: ABC Construction Ltd.
- General Contractor: XYZ Development Corp.
- Safety Inspector: Present on site
- Multiple witnesses

LEGAL IMPLICATIONS:
- WorkSafeBC has been notified and is investigating
- Legal counsel has been engaged
- Media may become involved due to high-profile project
- Potential regulatory violations under investigation
- Family has retained legal representation

This incident may result in significant liability exposure exceeding $5 million. Immediate expert review and risk assessment required.

Policy Number: LL555666777
Contact: Legal Department
Phone: (604) 555-LEGAL

CONFIDENTIAL - ATTORNEY-CLIENT PRIVILEGED""",
            "senderEmail": "<EMAIL>",
            "senderName": "Legal Department",
            "attachments": [
                {
                    "filename": "incident_report_confidential.pdf",
                    "contentType": "application/pdf",
                    "size": 1024000
                },
                {
                    "filename": "witness_statements.docx",
                    "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "size": 256000
                },
                {
                    "filename": "site_photos_evidence.zip",
                    "contentType": "application/zip",
                    "size": 5120000
                }
            ]
        }
    
    @staticmethod
    def quebec_civil_law_claim():
        """Quebec claim requiring civil law considerations"""
        return {
            "subject": "Réclamation - Accident de voiture à Montréal",
            "body": """Bonjour,
            
Je souhaite déclarer un sinistre automobile survenu à Montréal, Québec.

DÉTAILS DE L'ACCIDENT:
- Date: 12 mars 2024
- Heure: 16h30
- Lieu: Intersection Boulevard Saint-Laurent et Rue Sainte-Catherine, Montréal, QC
- Conditions: Temps clair, chaussée sèche

DESCRIPTION:
J'ai été impliqué dans une collision à l'intersection. L'autre conducteur a grillé un feu rouge et a percuté le côté droit de mon véhicule. Il y a eu des blessures mineures - mal au cou et au dos.

DOMMAGES:
- Véhicule: Dommages importants au côté passager
- Blessures: Cervicalgie, lombalgie
- Consultation médicale: Clinique sans rendez-vous le jour même

DOCUMENTS JOINTS:
- Photos des dommages
- Rapport de police SPVM #MTL-2024-5678
- Certificat médical

Numéro de police: QC445566789
Réclamant: Claude Martin
Téléphone: (*************

Merci de traiter ce dossier selon la loi québécoise applicable.

Cordialement,
Claude Martin""",
            "senderEmail": "<EMAIL>",
            "senderName": "Claude Martin",
            "attachments": [
                {
                    "filename": "photos_accident_mtl.jpg",
                    "contentType": "image/jpeg",
                    "size": 3072000
                },
                {
                    "filename": "rapport_police_SPVM.pdf",
                    "contentType": "application/pdf",
                    "size": 768000
                },
                {
                    "filename": "certificat_medical.pdf",
                    "contentType": "application/pdf",
                    "size": 256000
                }
            ]
        }
    
    @staticmethod
    def suspicious_fraud_indicators():
        """Email with potential fraud indicators requiring human review"""
        return {
            "subject": "Multiple Claims - Same Incident",
            "body": """Hi there,
            
I need to file multiple claims for the same incident that happened last week. My car, my friend's car, and my neighbor's property were all damaged in the same event.

The incident happened on different dates though:
- March 1st for my car
- March 3rd for friend's car  
- March 5th for neighbor's property

I have receipts showing I bought all the damaged items just last week. The total value is around $50,000 which seems fair for the damage.

My previous claims were:
- 2023: $45,000 claim 
- 2022: $38,000 claim
- 2021: $42,000 claim

I always have bad luck with accidents. Please process these claims quickly as I need the money urgently.

Policy numbers: 
- AUTO123 (my car)
- HOME456 (neighbor's house - I'm listed as additional insured)
- LIABILITY789 (friend's car - I was driving)

Contact me only by email, I don't have a phone right now.

Thanks,
Anonymous Claimant""",
            "senderEmail": "<EMAIL>",
            "senderName": "John Smith Jr",
            "attachments": [
                {
                    "filename": "totally_real_receipts.doc",
                    "contentType": "application/msword",
                    "size": 12000
                },
                {
                    "filename": "damage_photos.exe",
                    "contentType": "application/x-executable",
                    "size": 4096
                }
            ]
        }

# ================================================================================================
# API TEST FUNCTIONS
# ================================================================================================

def test_api_health_check():
    """Test that the API is running and healthy"""
    try:
        response = requests.get(f"{API_BASE_URL}/api/health", timeout=TEST_REQUEST_TIMEOUT)
        assert response.status_code == 200
        
        health_data = response.json()
        assert health_data["status"] == "healthy"
        assert "baml_client" in health_data
        assert "models" in health_data
        
        print("✅ Health check passed")
        print(f"📊 Health status: {json.dumps(health_data, indent=2)}")
        return True
        
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_email_classification(test_email_data: Dict[str, Any], expected_action: str = None, test_name: str = "Test"):
    """Test email classification with specific test data"""
    try:
        print(f"\n🧪 Running {test_name}")
        print(f"📧 Subject: {test_email_data['subject'][:50]}...")
        print(f"👤 Sender: {test_email_data['senderEmail']}")
        print(f"📎 Attachments: {len(test_email_data.get('attachments', []))}")
        
        # Test the detailed endpoint
        response = requests.post(
            f"{API_BASE_URL}/api/classify-email",
            json=test_email_data,
            timeout=TEST_REQUEST_TIMEOUT
        )
        
        if response.status_code != 200:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
        
        result = response.json()
        
        print(f"\n📋 CLASSIFICATION RESULTS:")
        print(f"   🎯 Workflow Action: {result['workflowAction']}")
        print(f"   📊 Is Claim Related: {result['isClaimRelated']}")
        print(f"   🏷️  Claim Type: {result['claimType']}")
        print(f"   ⚡ Urgency Level: {result['urgencyLevel']}")
        print(f"   🎲 Confidence Score: {result['confidenceScore']:.2f}")
        print(f"   🍁 Canadian Jurisdiction: {result['canadianJurisdiction']}")
        print(f"   ⏱️  Processing Time: {result['processingTime']:.2f}s")
        print(f"   🤖 Model Used: {result['modelUsed']}")
        
        print(f"\n📎 ATTACHMENT ANALYSIS:")
        att_analysis = result['attachmentAnalysis']
        print(f"   Has Attachments: {att_analysis['hasAttachments']}")
        print(f"   Mentions Attachments: {att_analysis['mentionsAttachments']}")
        print(f"   Attachment Mismatch: {att_analysis['attachmentMismatch']}")
        print(f"   Suspicious Filenames: {att_analysis['suspiciousFilenames']}")
        
        if result.get('riskAssessment'):
            print(f"\n⚠️  RISK ASSESSMENT:")
            risk = result['riskAssessment']
            print(f"   Liability Risk: {risk['liabilityRisk']}")
            print(f"   Financial Risk: {risk['financialRisk']}")
            print(f"   Overall Risk Score: {risk['overallRiskScore']:.2f}")
        
        print(f"\n💭 REASONING:")
        print(f"   {result['classificationReasoning'][:200]}...")
        
        if result.get('flagsForHumanReview'):
            print(f"\n🚩 FLAGS FOR HUMAN REVIEW:")
            for flag in result['flagsForHumanReview']:
                print(f"   - {flag}")
        
        # Validate expected action if provided
        if expected_action and result['workflowAction'] != expected_action:
            print(f"⚠️  Expected action: {expected_action}, Got: {result['workflowAction']}")
            return False
        
        print(f"✅ {test_name} completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ {test_name} failed: {e}")
        return False

def test_simple_classification(test_email_data: Dict[str, Any], test_name: str = "Simple Test"):
    """Test the simplified classification endpoint"""
    try:
        print(f"\n🎯 Running {test_name} (Simple Endpoint)")
        
        response = requests.post(
            f"{API_BASE_URL}/api/classify-email/simple",
            json=test_email_data,
            timeout=TEST_REQUEST_TIMEOUT
        )
        
        if response.status_code != 200:
            print(f"❌ Simple API Error: {response.status_code}")
            return False
        
        result = response.json()
        
        print(f"📋 SIMPLE RESULTS:")
        print(f"   Action: {result['workflowAction']}")
        print(f"   Claim Related: {result['isClaimRelated']}")
        print(f"   Confidence: {result['confidenceScore']:.2f}")
        print(f"   Attachment Issue: {result['attachmentIssue']}")
        print(f"   Human Review: {result['requiresHumanReview']}")
        
        print(f"✅ {test_name} (Simple) completed")
        return True
        
    except Exception as e:
        print(f"❌ {test_name} (Simple) failed: {e}")
        return False

def test_api_info():
    """Test the API info endpoint"""
    try:
        response = requests.get(f"{API_BASE_URL}/api/info", timeout=TEST_REQUEST_TIMEOUT)
        assert response.status_code == 200
        
        info_data = response.json()
        print(f"\n📚 API INFO:")
        print(f"   Name: {info_data['name']}")
        print(f"   Version: {info_data['version']}")
        print(f"   Endpoints: {len(info_data['endpoints'])}")
        print(f"   Workflow Actions: {info_data['workflow_actions']}")
        
        return True
        
    except Exception as e:
        print(f"❌ API Info test failed: {e}")
        return False

# ================================================================================================
# MAIN TEST RUNNER
# ================================================================================================

def run_comprehensive_tests():
    """Run all email classification tests"""
    print("🚀 ZURICH EMAIL CLASSIFICATION API - COMPREHENSIVE TEST SUITE")
    print("=" * 70)
    
    # Test API availability first
    if not test_api_health_check():
        print("❌ API is not available. Make sure the server is running on http://localhost:8000")
        return False
    
    test_api_info()
    
    test_scenarios = [
        # Valid claims tests
        (TestEmailScenarios.valid_auto_claim_with_attachments(), "PROCEED_TO_ZENDESK", "Valid Auto Claim"),
        (TestEmailScenarios.high_risk_liability_claim(), "HUMAN_REVIEW_REQUIRED", "High-Risk Liability"),
        (TestEmailScenarios.quebec_civil_law_claim(), "PROCEED_TO_ZENDESK", "Quebec Civil Law Claim"),
        
        # Attachment mismatch test
        (TestEmailScenarios.mentions_attachments_but_none_provided(), "REQUEST_ATTACHMENTS", "Missing Attachments"),
        
        # Non-claim tests
        (TestEmailScenarios.not_claim_related_marketing(), "IGNORE_EMAIL", "Marketing Email"),
        
        # Fraud indicators test
        (TestEmailScenarios.suspicious_fraud_indicators(), "HUMAN_REVIEW_REQUIRED", "Suspicious/Fraud"),
    ]
    
    passed_tests = 0
    total_tests = len(test_scenarios)
    
    for test_email, expected_action, test_name in test_scenarios:
        if test_email_classification(test_email, expected_action, test_name):
            passed_tests += 1
            
        # Also test simple endpoint for first few scenarios
        if passed_tests <= 3:
            test_simple_classification(test_email, test_name)
        
        print("-" * 50)
    
    print(f"\n📊 TEST SUMMARY:")
    print(f"   ✅ Passed: {passed_tests}/{total_tests}")
    print(f"   ❌ Failed: {total_tests - passed_tests}/{total_tests}")
    print(f"   📈 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 All tests passed! The API is working correctly.")
    else:
        print(f"\n⚠️  {total_tests - passed_tests} tests failed. Check the logs for details.")
    
    return passed_tests == total_tests

# ================================================================================================
# INDIVIDUAL TEST FUNCTIONS FOR DEBUGGING
# ================================================================================================

def debug_specific_email(subject: str, body: str, sender_email: str, attachments: List[Dict] = None):
    """Debug a specific email classification"""
    email_data = {
        "subject": subject,
        "body": body,
        "senderEmail": sender_email,
        "senderName": sender_email.split("@")[0],
        "attachments": attachments or []
    }
    
    return test_email_classification(email_data, test_name="Debug Email")

def test_attachment_detection():
    """Test attachment mention detection logic"""
    test_bodies = [
        "Please see attached documents",
        "I have photos attached to this email",
        "Find the report attached",
        "No attachments mentioned here",
        "Attached you will find the estimates",
        "Here are the PDF files",
        "Check the enclosed documents"
    ]
    
    print("\n🧪 TESTING ATTACHMENT DETECTION:")
    for body in test_bodies:
        # This would test the regex patterns used in the API
        mentions_attachments = any([
            "attach" in body.lower(),
            "document" in body.lower(),
            "pdf" in body.lower(),
            "enclosed" in body.lower()
        ])
        print(f"   '{body[:30]}...': {mentions_attachments}")

# ================================================================================================
# PYTEST INTEGRATION
# ================================================================================================

@pytest.mark.asyncio
async def test_auto_claim():
    """Pytest version of auto claim test"""
    result = test_email_classification(
        TestEmailScenarios.valid_auto_claim_with_attachments(),
        "PROCEED_TO_ZENDESK",
        "Pytest Auto Claim"
    )
    assert result == True

@pytest.mark.asyncio
async def test_missing_attachments():
    """Pytest version of missing attachments test"""
    result = test_email_classification(
        TestEmailScenarios.mentions_attachments_but_none_provided(),
        "REQUEST_ATTACHMENTS",
        "Pytest Missing Attachments"
    )
    assert result == True

@pytest.mark.asyncio
async def test_marketing_ignore():
    """Pytest version of marketing email test"""
    result = test_email_classification(
        TestEmailScenarios.not_claim_related_marketing(),
        "IGNORE_EMAIL",
        "Pytest Marketing"
    )
    assert result == True

# ================================================================================================
# COMMAND LINE EXECUTION
# ================================================================================================

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "health":
            test_api_health_check()
        elif command == "info":
            test_api_info()
        elif command == "auto":
            test_email_classification(TestEmailScenarios.valid_auto_claim_with_attachments(), "PROCEED_TO_ZENDESK", "Auto Claim Test")
        elif command == "attachments":
            test_email_classification(TestEmailScenarios.mentions_attachments_but_none_provided(), "REQUEST_ATTACHMENTS", "Missing Attachments Test")
        elif command == "marketing":
            test_email_classification(TestEmailScenarios.not_claim_related_marketing(), "IGNORE_EMAIL", "Marketing Test")
        elif command == "fraud":
            test_email_classification(TestEmailScenarios.suspicious_fraud_indicators(), "HUMAN_REVIEW_REQUIRED", "Fraud Test")
        elif command == "quebec":
            test_email_classification(TestEmailScenarios.quebec_civil_law_claim(), "PROCEED_TO_ZENDESK", "Quebec Test")
        elif command == "debug":
            test_attachment_detection()
        else:
            print("Available commands: health, info, auto, attachments, marketing, fraud, quebec, debug")
    else:
        # Run comprehensive tests
        run_comprehensive_tests() 