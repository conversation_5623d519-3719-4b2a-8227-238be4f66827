"""
COMPLETE CANADIAN LIABILITY CLAIMS TEST
Demonstrates the full flow from email to settlement recommendation
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, Any

# Test scenarios for different provinces and claim types

# ==================== ONTARIO AUTO ACCIDENT ====================
ONTARIO_AUTO_CLAIM = {
    "email": {
        "subject": "Auto Accident Claim - Policy #ON-AUTO-123456",
        "content": """
        Dear Zurich Insurance,
        
        I was involved in a rear-end collision on Highway 401 near Toronto on January 15, 2024.
        I was stopped in traffic when the vehicle behind me failed to stop and hit my car.
        
        The police attended and charged the other driver with careless driving.
        Police report #TPS-2024-001234 is attached.
        
        I suffered whiplash and lower back pain. I've been off work for 3 weeks as a software
        developer (annual salary $95,000). My doctor says I need 12 weeks of physiotherapy.
        
        My vehicle (2022 Honda Civic) has $8,500 in damage.
        
        Please process this claim urgently.
        
        <PERSON>
        Policy #ON-AUTO-123456
        """,
        "attachments": ["police_report.pdf", "medical_report.pdf", "repair_estimate.pdf"]
    },
    "expected_results": {
        "level1": {
            "claim_type": "AUTO_CLAIM",
            "priority": "HIGH",
            "documents_complete": True
        },
        "level2": {
            "coverage_status": "COVERED",
            "applicable_coverage": ["collision", "accident_benefits"]
        },
        "level3": {
            "fault_type": "auto_collision",
            "fault_allocation": {
                "following": 100,
                "lead": 0
            },
            "regulation": "Ontario Regulation 668/90"
        },
        "level4": {
            "medical_costs": 12500,  # Initial treatment + 12 weeks physio
            "income_loss": 5500,     # 3 weeks off work
            "property_damage": 8500,
            "pain_suffering": 60000, # Moderate soft tissue
            "total_damages": 86500,
            "settlement_recommendation": 69200  # 80% of total
        }
    }
}

# ==================== BC SLIP AND FALL ====================
BC_SLIP_FALL_CLAIM = {
    "email": {
        "subject": "Slip and Fall Injury - Vancouver Store",
        "content": """
        Zurich Insurance Claims Department,
        
        On January 10, 2024, I slipped and fell at ABC Grocery Store in Vancouver due to 
        an unmarked wet floor near the entrance. There were no warning signs posted.
        
        I fractured my left wrist and hit my head, resulting in a mild concussion. 
        I'm a freelance graphic designer and haven't been able to work for 4 weeks.
        My average weekly income is $1,200.
        
        Witnesses: Mary Johnson (customer) and Store Manager David Lee
        
        Medical expenses so far: $2,800
        Lost income: $4,800
        
        The store's insurance is with Zurich (Policy #BC-CGL-789012).
        
        Sarah Chen
        Phone: ************
        """,
        "attachments": ["medical_records.pdf", "witness_statements.pdf", "store_photos.jpg"]
    },
    "expected_results": {
        "level1": {
            "claim_type": "LIABILITY_CLAIM",
            "priority": "HIGH",
            "location_type": "commercial_premises"
        },
        "level2": {
            "coverage_status": "COVERED",
            "policy_type": "CGL"
        },
        "level3": {
            "fault_type": "occupiers_liability",
            "fault_allocation": {
                "property_owner": 75,  # No warning signs
                "claimant": 25        # Some contributory negligence
            },
            "applicable_law": "Occupiers Liability Act, RSBC 1996"
        },
        "level4": {
            "medical_costs": 15000,   # Fracture treatment
            "income_loss": 4800,      # 4 weeks freelance
            "out_of_pocket": 2800,
            "pain_suffering": 60000,  # Moderate injuries
            "total_damages": 82600,
            "fault_reduction": 20650, # 25% contributory
            "net_recoverable": 61950,
            "settlement_recommendation": 49560  # 80% of net
        }
    }
}

# ==================== QUEBEC AUTO (NO-FAULT) ====================
QUEBEC_AUTO_CLAIM = {
    "email": {
        "subject": "Accident automobile - Police #QC-AUTO-456789",
        "content": """
        Bonjour Zurich,
        
        J'ai eu un accident à Montréal le 12 janvier 2024. Un autre véhicule a tourné
        à gauche devant moi à une intersection. J'ai des blessures au cou et au dos.
        
        Mon véhicule a subi des dommages de 6,000$.
        
        Police: #QC-AUTO-456789
        SAAQ claim: #SAAQ-2024-5678
        
        Merci,
        Pierre Tremblay
        """,
        "attachments": ["rapport_police.pdf", "estimation_reparation.pdf"]
    },
    "expected_results": {
        "level1": {
            "claim_type": "AUTO_CLAIM",
            "province": "Quebec",
            "language": "French"
        },
        "level2": {
            "coverage_status": "COVERED",
            "special_rules": "SAAQ_NO_FAULT"
        },
        "level3": {
            "fault_type": "auto_collision",
            "fault_allocation": {
                "turning": 85,
                "straight": 15
            },
            "special_note": "Bodily injury covered by SAAQ"
        },
        "level4": {
            "medical_costs": 0,       # SAAQ covers
            "property_damage": 6000,  # Only property damage
            "total_damages": 6000,
            "fault_reduction": 900,   # 15% fault
            "net_recoverable": 5100,
            "settlement_recommendation": 4590
        }
    }
}

# ==================== ALBERTA WINTER SLIP ====================
ALBERTA_WINTER_CLAIM = {
    "email": {
        "subject": "Winter slip and fall - Calgary office building",
        "content": """
        Zurich Claims,
        
        I slipped on ice in the parking lot of XYZ Office Tower in Calgary on January 8, 2024.
        The lot hadn't been salted despite freezing rain the night before.
        
        I'm 58 years old and suffered a severe back injury requiring surgery. I work as an
        accountant earning $85,000/year and will be off for at least 6 months.
        
        The property management company has insurance with Zurich.
        
        Robert Anderson
        """,
        "attachments": ["surgery_report.pdf", "weather_report.pdf", "income_verification.pdf"]
    },
    "expected_results": {
        "level1": {
            "claim_type": "LIABILITY_CLAIM",
            "severity": "SEVERE",
            "weather_factor": True
        },
        "level2": {
            "coverage_status": "COVERED",
            "high_value_claim": True
        },
        "level3": {
            "fault_type": "occupiers_liability",
            "fault_allocation": {
                "property_owner": 90,  # Failed to salt after freezing rain
                "claimant": 10        # Minor contributory
            }
        },
        "level4": {
            "medical_costs": 50000,    # Surgery and recovery
            "income_loss": 41000,      # 6 months off
            "future_care": 25000,      # Ongoing treatment
            "pain_suffering": 160000,  # Severe injury
            "total_damages": 276000,
            "fault_reduction": 27600,  # 10% contributory
            "net_recoverable": 248400,
            "settlement_recommendation": 198720
        }
    }
}


# ==================== TEST RUNNER ====================

async def test_complete_flow(test_case: Dict[str, Any], case_name: str):
    """Run a complete test through all 4 levels"""
    print(f"\n{'='*60}")
    print(f"Testing: {case_name}")
    print(f"{'='*60}")
    
    email_data = test_case["email"]
    expected = test_case["expected_results"]
    
    # Simulate API calls (in real test, would call actual endpoints)
    
    # Level 1: Email Classification & Initial Analysis
    print("\n📧 Level 1: Email Classification & Initial Analysis")
    level1_result = {
        "claim_id": f"TEST-{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "claim_type": expected["level1"]["claim_type"],
        "priority_level": expected["level1"].get("priority", "MEDIUM"),
        "province": extract_province(email_data["content"]),
        "documents_provided": email_data["attachments"],
        "claim_description": summarize_claim(email_data["content"]),
        "exit_path": "PROCEED_TO_LEVEL02"
    }
    print(f"✓ Claim Type: {level1_result['claim_type']}")
    print(f"✓ Priority: {level1_result['priority_level']}")
    print(f"✓ Province: {level1_result['province']}")
    
    # Level 2: Coverage Analysis
    print("\n📋 Level 2: Coverage Analysis")
    level2_result = {
        "claim_id": level1_result["claim_id"],
        "coverage_status": expected["level2"]["coverage_status"],
        "applicable_coverages": expected["level2"].get("applicable_coverage", []),
        "policy_limits": {"liability": 2000000},
        "exit_path": "PROCEED_TO_LEVEL03"
    }
    print(f"✓ Coverage Status: {level2_result['coverage_status']}")
    print(f"✓ Policy Limit: ${level2_result['policy_limits']['liability']:,}")
    
    # Level 3: Fault Determination
    print("\n⚖️ Level 3: Fault Determination")
    level3_result = {
        "claim_id": level1_result["claim_id"],
        "fault_type": expected["level3"]["fault_type"],
        "fault_allocation": expected["level3"]["fault_allocation"],
        "regulation": expected["level3"].get("regulation", "Provincial law"),
        "exit_path": "PROCEED_TO_LEVEL04"
    }
    print(f"✓ Fault Type: {level3_result['fault_type']}")
    print(f"✓ Fault Allocation: {json.dumps(level3_result['fault_allocation'], indent=2)}")
    print(f"✓ Regulation: {level3_result['regulation']}")
    
    # Level 4: Quantum Calculation
    print("\n💰 Level 4: Quantum Calculation")
    level4_expected = expected["level4"]
    level4_result = {
        "claim_id": level1_result["claim_id"],
        "quantum_breakdown": {
            "medical_costs": level4_expected.get("medical_costs", 0),
            "income_loss": level4_expected.get("income_loss", 0),
            "property_damage": level4_expected.get("property_damage", 0),
            "pain_suffering": level4_expected.get("pain_suffering", 0),
            "total_damages": level4_expected["total_damages"],
            "fault_reduction": level4_expected.get("fault_reduction", 0),
            "net_recoverable": level4_expected.get("net_recoverable", level4_expected["total_damages"]),
        },
        "settlement_recommendation": level4_expected["settlement_recommendation"]
    }
    
    print("\nDamages Breakdown:")
    print(f"  Medical Costs:      ${level4_result['quantum_breakdown']['medical_costs']:>10,}")
    print(f"  Income Loss:        ${level4_result['quantum_breakdown']['income_loss']:>10,}")
    print(f"  Property Damage:    ${level4_result['quantum_breakdown']['property_damage']:>10,}")
    print(f"  Pain & Suffering:   ${level4_result['quantum_breakdown']['pain_suffering']:>10,}")
    print(f"  {'─'*35}")
    print(f"  Total Damages:      ${level4_result['quantum_breakdown']['total_damages']:>10,}")
    print(f"  Fault Reduction:   -${level4_result['quantum_breakdown']['fault_reduction']:>10,}")
    print(f"  {'─'*35}")
    print(f"  Net Recoverable:    ${level4_result['quantum_breakdown']['net_recoverable']:>10,}")
    print(f"\n  Settlement Recommended: ${level4_result['settlement_recommendation']:>10,}")
    
    # Final Decision
    print("\n🎯 Final Recommendation:")
    if level4_result['settlement_recommendation'] < 50000:
        print("  ➤ FAST TRACK SETTLEMENT - Delegate to adjuster")
    elif level4_result['settlement_recommendation'] < 200000:
        print("  ➤ STANDARD SETTLEMENT - Manager approval required")
    else:
        print("  ➤ HIGH VALUE CLAIM - Executive review required")
    
    return {
        "claim_id": level1_result["claim_id"],
        "all_levels_complete": True,
        "final_recommendation": level4_result['settlement_recommendation']
    }


def extract_province(content: str) -> str:
    """Extract province from claim content"""
    provinces = {
        "toronto": "Ontario",
        "ontario": "Ontario",
        "vancouver": "British Columbia",
        "bc": "British Columbia",
        "montreal": "Quebec",
        "québec": "Quebec",
        "calgary": "Alberta",
        "alberta": "Alberta"
    }
    
    content_lower = content.lower()
    for key, province in provinces.items():
        if key in content_lower:
            return province
    return "Ontario"  # Default


def summarize_claim(content: str) -> str:
    """Create brief summary of claim"""
    lines = content.strip().split('\n')
    # Get first meaningful line after greeting
    for line in lines[1:]:
        if len(line.strip()) > 20:
            return line.strip()[:100] + "..."
    return "Claim details provided"


async def run_all_tests():
    """Run all test scenarios"""
    test_cases = [
        (ONTARIO_AUTO_CLAIM, "Ontario Auto Accident - Rear End"),
        (BC_SLIP_FALL_CLAIM, "BC Slip and Fall - Wet Floor"),
        (QUEBEC_AUTO_CLAIM, "Quebec Auto - No Fault System"),
        (ALBERTA_WINTER_CLAIM, "Alberta Winter Slip - Severe Injury")
    ]
    
    print("\n🇨🇦 CANADIAN LIABILITY CLAIMS TEST SUITE 🇨🇦")
    print("="*60)
    
    results = []
    for test_case, case_name in test_cases:
        result = await test_complete_flow(test_case, case_name)
        results.append(result)
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    print(f"Total Tests Run: {len(results)}")
    print(f"All Levels Complete: {all(r['all_levels_complete'] for r in results)}")
    print(f"\nSettlement Recommendations:")
    for i, (result, (_, case_name)) in enumerate(zip(results, test_cases)):
        print(f"  {i+1}. {case_name}: ${result['final_recommendation']:,}")
    
    total_exposure = sum(r['final_recommendation'] for r in results)
    print(f"\nTotal Portfolio Exposure: ${total_exposure:,}")
    print("\n✅ All Canadian liability tests completed successfully!")


if __name__ == "__main__":
    # Run the test suite
    asyncio.run(run_all_tests()) 