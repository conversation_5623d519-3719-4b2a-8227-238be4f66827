#!/bin/bash

# ZURICH BACKEND - DEPLOYMENT SCRIPT
# ===================================

set -e

echo "🚀 Starting Zurich Backend Deployment"
echo "====================================="

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Creating from template..."
    cp .env.template .env
    echo "📝 Please edit .env file and add your OpenAI API key"
    echo "💡 You can get an API key from: https://platform.openai.com/api-keys"
    echo ""
    echo "📁 .env file created. Please add your OpenAI API key and run this script again."
    exit 1
fi

# Check if OPENAI_API_KEY is set in .env
if ! grep -q "OPENAI_API_KEY=sk-" .env; then
    echo "⚠️  OpenAI API key not found in .env file"
    echo "📝 Please edit .env file and add your OpenAI API key"
    echo "💡 Format: OPENAI_API_KEY=sk-your-key-here"
    exit 1
fi

echo "✅ Environment file found"

# Create logs directory if it doesn't exist
mkdir -p logs

echo "🐳 Building and starting Docker containers..."

# Build and start the containers
docker-compose up --build -d

echo "⏳ Waiting for services to start..."
sleep 10

# Check if the API is healthy
echo "🔍 Checking API health..."
for i in {1..30}; do
    if curl -f -s http://localhost:8000/api/health > /dev/null 2>&1; then
        echo "✅ Zurich Backend is healthy!"
        break
    else
        echo "⏳ Waiting for backend to start... ($i/30)"
        sleep 2
    fi
    
    if [ $i -eq 30 ]; then
        echo "❌ Backend failed to start. Check logs:"
        docker-compose logs zurich-backend
        exit 1
    fi
done

echo ""
echo "🎉 Zurich Backend is now running!"
echo "================================="
echo "🌐 Backend URL: http://localhost:8000"
echo "📚 API Documentation: http://localhost:8000/api/docs"
echo "🏥 Health Check: http://localhost:8000/api/health"
echo "ℹ️  API Info: http://localhost:8000/api/info"
echo ""
echo "📋 Available endpoints:"
echo "  - POST /api/classify-email        (Full email classification)"
echo "  - POST /api/classify-email/simple (Simple classification for n8n)"
echo "  - GET  /api/health                (Health check)"
echo "  - GET  /api/info                  (API information)"
echo ""
echo "🔍 View logs: docker-compose logs -f zurich-backend"
echo "🛑 Stop services: docker-compose down"
echo ""
echo "🧪 Test the API:"
echo "curl -X POST 'http://localhost:8000/api/classify-email/simple' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"subject\":\"Car accident claim\",\"body\":\"I need to file a claim for my car accident\",\"senderEmail\":\"<EMAIL>\"}'" 