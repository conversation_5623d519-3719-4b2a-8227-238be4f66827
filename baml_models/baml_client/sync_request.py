###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off
from typing import Dict, List, Optional, Union
from typing_extensions import Literal

import baml_py

from . import _baml


class HttpRequest:
    __runtime: baml_py.BamlRuntime
    __ctx_manager: baml_py.BamlCtxManager

    def __init__(self, runtime: baml_py.BamlRuntime, ctx_manager: baml_py.BamlCtxManager):
      self.__runtime = runtime
      self.__ctx_manager = ctx_manager

    
    def AnalyzeClaimLevel01(
        self,
        claimInput: _baml.types.ClaimDocumentInput,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "AnalyzeClaimLevel01",
        {
          "claimInput": claimInput,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    
    def AnalyzeCoverageLevel02(
        self,
        claimId: str,claimType: str,policyNumber: str,incidentDate: str,primaryCause: str,level01Confidence: float,level01ExitPath: str,keyFindings: List[str],policyDocuments: str,additionalEvidence: str,canadianJurisdiction: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "AnalyzeCoverageLevel02",
        {
          "claimId": claimId,"claimType": claimType,"policyNumber": policyNumber,"incidentDate": incidentDate,"primaryCause": primaryCause,"level01Confidence": level01Confidence,"level01ExitPath": level01ExitPath,"keyFindings": keyFindings,"policyDocuments": policyDocuments,"additionalEvidence": additionalEvidence,"canadianJurisdiction": canadianJurisdiction,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    
    def AssessDataCompleteness(
        self,
        claimId: str,claimType: str,level01Analysis: str,claimDetails: str,ocrTexts: List[str],attachmentsList: List[str],additionalContext: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "AssessDataCompleteness",
        {
          "claimId": claimId,"claimType": claimType,"level01Analysis": level01Analysis,"claimDetails": claimDetails,"ocrTexts": ocrTexts,"attachmentsList": attachmentsList,"additionalContext": additionalContext,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    
    def AssessPriorityAndRisk(
        self,
        claimId: str,claimType: str,coverageDecision: str,coverageConfidence: float,level01Analysis: str,claimDetails: str,policyInformation: str,dataCompleteness: float,additionalContext: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "AssessPriorityAndRisk",
        {
          "claimId": claimId,"claimType": claimType,"coverageDecision": coverageDecision,"coverageConfidence": coverageConfidence,"level01Analysis": level01Analysis,"claimDetails": claimDetails,"policyInformation": policyInformation,"dataCompleteness": dataCompleteness,"additionalContext": additionalContext,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    
    def ClassifyZurichEmail(
        self,
        emailData: _baml.types.EmailForClassification,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "ClassifyZurichEmail",
        {
          "emailData": emailData,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    
    def ClassifyZurichEmailFallback(
        self,
        emailData: _baml.types.EmailForClassification,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "ClassifyZurichEmailFallback",
        {
          "emailData": emailData,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    
    def EnhanceWithSparkNLP(
        self,
        emailContent: str,attachmentsText: List[str],
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "EnhanceWithSparkNLP",
        {
          "emailContent": emailContent,"attachmentsText": attachmentsText,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    
    def ExtractLevel03FaultFactors(
        self,
        input: _baml.types.Level03AnalysisInput,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "ExtractLevel03FaultFactors",
        {
          "input": input,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    
    def ExtractLevel04QuantumDetails(
        self,
        input: _baml.types.Level04AnalysisInput,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "ExtractLevel04QuantumDetails",
        {
          "input": input,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    
    def ExtractResume(
        self,
        resume: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "ExtractResume",
        {
          "resume": resume,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    
    def GenerateDocumentHighlights(
        self,
        document_text: str,document_filename: str,claim_reference: str,email_subject: str,email_body: str,level01_analysis: str,level02_analysis: str,level03_analysis: str,level04_analysis: str,ocr_texts: List[str],
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "GenerateDocumentHighlights",
        {
          "document_text": document_text,"document_filename": document_filename,"claim_reference": claim_reference,"email_subject": email_subject,"email_body": email_body,"level01_analysis": level01_analysis,"level02_analysis": level02_analysis,"level03_analysis": level03_analysis,"level04_analysis": level04_analysis,"ocr_texts": ocr_texts,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    
    def GenerateMultiDocumentHighlights(
        self,
        documents: List[str],filenames: List[str],claim_reference: str,email_subject: str,email_body: str,level01_analysis: str,level02_analysis: str,level03_analysis: str,level04_analysis: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "GenerateMultiDocumentHighlights",
        {
          "documents": documents,"filenames": filenames,"claim_reference": claim_reference,"email_subject": email_subject,"email_body": email_body,"level01_analysis": level01_analysis,"level02_analysis": level02_analysis,"level03_analysis": level03_analysis,"level04_analysis": level04_analysis,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    
    def ResearchCanadianLegalPrecedents(
        self,
        claimType: str,causeOfLoss: str,policyLanguage: str,jurisdiction: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "ResearchCanadianLegalPrecedents",
        {
          "claimType": claimType,"causeOfLoss": causeOfLoss,"policyLanguage": policyLanguage,"jurisdiction": jurisdiction,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    


class HttpStreamRequest:
    __runtime: baml_py.BamlRuntime
    __ctx_manager: baml_py.BamlCtxManager

    def __init__(self, runtime: baml_py.BamlRuntime, ctx_manager: baml_py.BamlCtxManager):
      self.__runtime = runtime
      self.__ctx_manager = ctx_manager

    
    def AnalyzeClaimLevel01(
        self,
        claimInput: _baml.types.ClaimDocumentInput,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "AnalyzeClaimLevel01",
        {
          "claimInput": claimInput,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    
    def AnalyzeCoverageLevel02(
        self,
        claimId: str,claimType: str,policyNumber: str,incidentDate: str,primaryCause: str,level01Confidence: float,level01ExitPath: str,keyFindings: List[str],policyDocuments: str,additionalEvidence: str,canadianJurisdiction: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "AnalyzeCoverageLevel02",
        {
          "claimId": claimId,"claimType": claimType,"policyNumber": policyNumber,"incidentDate": incidentDate,"primaryCause": primaryCause,"level01Confidence": level01Confidence,"level01ExitPath": level01ExitPath,"keyFindings": keyFindings,"policyDocuments": policyDocuments,"additionalEvidence": additionalEvidence,"canadianJurisdiction": canadianJurisdiction,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    
    def AssessDataCompleteness(
        self,
        claimId: str,claimType: str,level01Analysis: str,claimDetails: str,ocrTexts: List[str],attachmentsList: List[str],additionalContext: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "AssessDataCompleteness",
        {
          "claimId": claimId,"claimType": claimType,"level01Analysis": level01Analysis,"claimDetails": claimDetails,"ocrTexts": ocrTexts,"attachmentsList": attachmentsList,"additionalContext": additionalContext,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    
    def AssessPriorityAndRisk(
        self,
        claimId: str,claimType: str,coverageDecision: str,coverageConfidence: float,level01Analysis: str,claimDetails: str,policyInformation: str,dataCompleteness: float,additionalContext: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "AssessPriorityAndRisk",
        {
          "claimId": claimId,"claimType": claimType,"coverageDecision": coverageDecision,"coverageConfidence": coverageConfidence,"level01Analysis": level01Analysis,"claimDetails": claimDetails,"policyInformation": policyInformation,"dataCompleteness": dataCompleteness,"additionalContext": additionalContext,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    
    def ClassifyZurichEmail(
        self,
        emailData: _baml.types.EmailForClassification,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "ClassifyZurichEmail",
        {
          "emailData": emailData,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    
    def ClassifyZurichEmailFallback(
        self,
        emailData: _baml.types.EmailForClassification,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "ClassifyZurichEmailFallback",
        {
          "emailData": emailData,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    
    def EnhanceWithSparkNLP(
        self,
        emailContent: str,attachmentsText: List[str],
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "EnhanceWithSparkNLP",
        {
          "emailContent": emailContent,"attachmentsText": attachmentsText,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    
    def ExtractLevel03FaultFactors(
        self,
        input: _baml.types.Level03AnalysisInput,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "ExtractLevel03FaultFactors",
        {
          "input": input,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    
    def ExtractLevel04QuantumDetails(
        self,
        input: _baml.types.Level04AnalysisInput,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "ExtractLevel04QuantumDetails",
        {
          "input": input,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    
    def ExtractResume(
        self,
        resume: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "ExtractResume",
        {
          "resume": resume,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    
    def GenerateDocumentHighlights(
        self,
        document_text: str,document_filename: str,claim_reference: str,email_subject: str,email_body: str,level01_analysis: str,level02_analysis: str,level03_analysis: str,level04_analysis: str,ocr_texts: List[str],
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "GenerateDocumentHighlights",
        {
          "document_text": document_text,"document_filename": document_filename,"claim_reference": claim_reference,"email_subject": email_subject,"email_body": email_body,"level01_analysis": level01_analysis,"level02_analysis": level02_analysis,"level03_analysis": level03_analysis,"level04_analysis": level04_analysis,"ocr_texts": ocr_texts,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    
    def GenerateMultiDocumentHighlights(
        self,
        documents: List[str],filenames: List[str],claim_reference: str,email_subject: str,email_body: str,level01_analysis: str,level02_analysis: str,level03_analysis: str,level04_analysis: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "GenerateMultiDocumentHighlights",
        {
          "documents": documents,"filenames": filenames,"claim_reference": claim_reference,"email_subject": email_subject,"email_body": email_body,"level01_analysis": level01_analysis,"level02_analysis": level02_analysis,"level03_analysis": level03_analysis,"level04_analysis": level04_analysis,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    
    def ResearchCanadianLegalPrecedents(
        self,
        claimType: str,causeOfLoss: str,policyLanguage: str,jurisdiction: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "ResearchCanadianLegalPrecedents",
        {
          "claimType": claimType,"causeOfLoss": causeOfLoss,"policyLanguage": policyLanguage,"jurisdiction": jurisdiction,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    


__all__ = ["HttpRequest", "HttpStreamRequest"]