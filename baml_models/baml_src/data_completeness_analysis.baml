// ================================================================================================
// AI-DRIVEN DATA COMPLETENESS ANALYSIS - BAML IMPLEMENTATION
// ================================================================================================
// Purpose: Intelligent assessment of data completeness for insurance claims analysis
// Enhancement: Replaces hardcoded scoring with AI-driven contextual assessment
// ================================================================================================

// ================================================================================================
// DATA COMPLETENESS SCHEMAS
// ================================================================================================

class DataCompletenessAssessment {
  overallCompletenessScore float @description("Overall data completeness score (0.0-1.0)")
  
  // Component-specific assessments
  level01AnalysisCompleteness float @description("Completeness of Level 01 analysis (0.0-1.0)")
  claimDetailsCompleteness float @description("Completeness of basic claim information (0.0-1.0)")
  ocrDocumentsCompleteness float @description("Completeness of OCR extracted documents (0.0-1.0)")
  attachmentsCompleteness float @description("Completeness of file attachments (0.0-1.0)")
  
  // Dynamic importance weighting
  level01AnalysisImportance float @description("Importance weight for Level 01 analysis for this specific claim type (0.0-1.0)")
  claimDetailsImportance float @description("Importance weight for claim details for this specific claim type (0.0-1.0)")
  ocrDocumentsImportance float @description("Importance weight for OCR documents for this specific claim type (0.0-1.0)")
  attachmentsImportance float @description("Importance weight for attachments for this specific claim type (0.0-1.0)")
  
  // Critical gaps identification
  criticalMissingItems string[] @description("List of critically missing data items that significantly impact analysis")
  minorMissingItems string[] @description("List of minor missing data items with minimal impact")
  
  // Quality assessment
  dataQualityScore float @description("Overall quality of available data (0.0-1.0)")
  analysisReadiness string @description("Assessment of readiness for Level 02 analysis: READY, NEEDS_CLARIFICATION, INSUFFICIENT_DATA")
  
  // Recommendations
  improvementRecommendations string[] @description("Specific recommendations to improve data completeness")
  alternativeDataSources string[] @description("Alternative sources that could fill data gaps")
  
  // Context-specific insights
  claimTypeSpecificNeeds string @description("Specific data needs based on claim type (AUTO, PROPERTY, LIABILITY, etc.)")
  riskFactorsFromGaps string[] @description("Risk factors introduced by data gaps")
  
  // Confidence and uncertainty
  assessmentConfidence float @description("Confidence in this completeness assessment (0.0-1.0)")
  uncertaintyAreas string[] @description("Areas where completeness assessment is uncertain")
}

class DataItem {
  itemName string @description("Name of the data item")
  isPresent bool @description("Whether this data item is present")
  qualityScore float @description("Quality score of this data item if present (0.0-1.0)")
  importanceForClaimType float @description("Importance of this item for the specific claim type (0.0-1.0)")
  alternativeSources string[] @description("Alternative sources where this data could be obtained")
  impactOnAnalysis string @description("How absence/presence of this item impacts Level 02 analysis")
}

// ================================================================================================
// AI-DRIVEN DATA COMPLETENESS ANALYSIS FUNCTION
// ================================================================================================

function AssessDataCompleteness(
  claimId: string,
  claimType: string,
  level01Analysis: string,
  claimDetails: string,
  ocrTexts: string[],
  attachmentsList: string[],
  additionalContext: string
) -> DataCompletenessAssessment {
  client "openai/gpt-4o-mini"
  prompt #"
    You are an AI expert in insurance data analysis, specializing in assessing data completeness for claims processing.
    
    MISSION: Intelligently assess the completeness and quality of available data for Level 02 coverage analysis, providing dynamic importance weighting based on claim type and context.
    
    CLAIM INFORMATION:
    Claim ID: {{ claimId }}
    Claim Type: {{ claimType }}
    
    AVAILABLE DATA SOURCES:
    
    LEVEL 01 ANALYSIS:
    {{ level01Analysis }}
    
    BASIC CLAIM DETAILS:
    {{ claimDetails }}
    
    OCR EXTRACTED TEXTS:
    {% for text in ocrTexts %}
    - {{ text }}
    {% endfor %}
    
    ATTACHMENTS:
    {% for attachment in attachmentsList %}
    - {{ attachment }}
    {% endfor %}
    
    ADDITIONAL CONTEXT:
    {{ additionalContext }}
    
    INTELLIGENT DATA COMPLETENESS ASSESSMENT REQUIREMENTS:
    
    1. CONTEXTUAL IMPORTANCE WEIGHTING:
       - Analyze the specific claim type ({{ claimType }}) to determine data importance
       - Consider claim complexity, financial exposure, and regulatory requirements
       - Adjust importance weights based on specific circumstances
       - Account for industry best practices for this claim type
    
    2. QUALITY OVER QUANTITY ASSESSMENT:
       - Evaluate not just presence but quality of available data
       - Consider consistency across data sources
       - Assess reliability and completeness of each data component
       - Identify conflicting information that needs resolution
    
    3. CRITICAL GAP IDENTIFICATION:
       - Identify data gaps that would prevent accurate coverage determination
       - Distinguish between critical gaps and nice-to-have information
       - Consider alternative data sources that could fill gaps
       - Assess impact of each gap on analysis confidence
    
    4. CLAIM-TYPE SPECIFIC ANALYSIS:
       For AUTO claims, prioritize: vehicle details, police reports, damage assessments
       For PROPERTY claims, prioritize: property valuations, cause determinations, exclusion applicability
       For LIABILITY claims, prioritize: incident details, witness information, causation evidence
       For HEALTH/DISABILITY claims, prioritize: medical records, employment history, benefit calculations
    
    5. INTELLIGENT SCORING METHODOLOGY:
       - Use dynamic weighting based on claim characteristics
       - Consider data interdependencies (e.g., Level 01 analysis quality affects overall score more heavily)
       - Factor in data freshness and reliability
       - Account for regulatory and legal requirements
    
    6. ANALYSIS READINESS DETERMINATION:
       READY: Sufficient high-quality data for confident coverage determination
       NEEDS_CLARIFICATION: Some gaps exist but analysis can proceed with noted uncertainties
       INSUFFICIENT_DATA: Critical gaps prevent reliable coverage analysis
    
    7. ACTIONABLE RECOMMENDATIONS:
       - Specific steps to improve data completeness
       - Alternative data sources to consider
       - Prioritized list of information to obtain
       - Timeline considerations for data gathering
    
    ASSESSMENT GUIDELINES:
    
    - Be context-aware: Different claim types require different data priorities
    - Consider data quality, not just presence
    - Provide specific, actionable feedback
    - Use realistic scoring that reflects actual data utility
    - Account for diminishing returns (90% completeness may be as good as 100% for analysis purposes)
    - Consider legal and regulatory data requirements
    - Factor in cost-benefit analysis of obtaining additional data
    
    Provide a comprehensive assessment that enables intelligent decision-making about proceeding with Level 02 analysis or obtaining additional data first.
    
    {{ ctx.output_format }}
  "#
} 