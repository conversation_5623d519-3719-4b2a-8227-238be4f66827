// AI-Driven Document Highlights and Explainability
// Generates intelligent highlights with color-coding and explanations for claim documents

class DocumentHighlight {
  start int @description("Start character position in the document text")
  end int @description("End character position in the document text")
  text string @description("The actual highlighted text from the document")
  color string @description("Hex color code chosen by AI to reflect importance/type")
  field string @description("Which claim field or analysis this text supports (e.g., 'incident_date', 'coverage_decision')")
  explanation string @description("Why this text is important for the claim decision")
  contribution_score float @description("How much this text contributed to the AI's decision, 0.0-1.0")
  highlight_type string @description("Type of highlight: 'critical', 'supporting', 'evidence', 'context'")
}

class ColorLegend {
  color string @description("Hex color code")
  meaning string @description("What this color represents in the context of claim analysis")
  examples string[] @description("Examples of what gets highlighted in this color")
}

class DocumentExplainability {
  highlights DocumentHighlight[]
  color_legend ColorLegend[]
  summary string @description("Brief summary of what the AI found important in this document")
  confidence_score float @description("Overall confidence in the highlight analysis, 0.0-1.0")
  processing_notes string[] @description("Any notes about the analysis process or limitations")
}

function GenerateDocumentHighlights(
  document_text: string,
  document_filename: string,
  claim_reference: string,
  email_subject: string,
  email_body: string,
  level01_analysis: string,
  level02_analysis: string,
  level03_analysis: string,
  level04_analysis: string,
  ocr_texts: string[]
) -> DocumentExplainability {
  client "openai/gpt-4o"
  prompt #"
    You are an expert AI claims analyst tasked with generating explainable highlights for insurance claim documents.
    
    CONTEXT:
    - Claim Reference: {{ claim_reference }}
    - Document: {{ document_filename }}
    - Email Subject: {{ email_subject }}
    - Email Body: {{ email_body }}
    
    ANALYSIS LEVELS COMPLETED:
    Level 1 Analysis: {{ level01_analysis }}
    Level 2 Analysis: {{ level02_analysis }}
    Level 3 Analysis: {{ level03_analysis }}
    Level 4 Analysis: {{ level04_analysis }}
    
    OCR TEXTS FROM OTHER DOCUMENTS:
    {% for ocr in ocr_texts %}
    - {{ ocr }}
    {% endfor %}
    
    DOCUMENT TO ANALYZE:
    {{ document_text }}
    
    INSTRUCTIONS:
    1. Analyze the document text and identify key phrases, sentences, or sections that:
       - Support or contradict the AI analysis decisions made in Levels 1-4
       - Provide evidence for coverage decisions, fault determination, or quantum calculations
       - Contain critical dates, amounts, parties, or circumstances
       - Influence risk assessment or claim validity
    
    2. For each highlight, determine:
       - Exact character positions (start and end) in the document
       - Appropriate color based on importance and type (use a consistent color scheme)
       - Which specific claim field or analysis this text supports
       - Clear explanation of why this text is significant
       - Contribution score (how much this influenced the AI's decision)
       - Highlight type (critical/supporting/evidence/context)
    
    3. Create a color legend that explains:
       - What each color represents (e.g., red for critical issues, yellow for dates, green for positive evidence)
       - Examples of what gets highlighted in each color
       - Keep it simple but comprehensive
    
    4. Focus on explainability - every highlight should help a human understand:
       - Why the AI made certain decisions
       - What evidence supports or challenges the analysis
       - How different pieces of information connect
    
    5. Prioritize quality over quantity - highlight the most important elements that truly impact the claim
    
    COLOR SCHEME GUIDELINES:
    - Use hex colors that are visually distinct and accessible
    - Consider: #FF6B6B (red) for critical issues, #4ECDC4 (teal) for dates/facts, 
      #45B7D1 (blue) for coverage info, #FFA07A (orange) for amounts/values,
      #98D8C8 (mint) for positive evidence, #F7DC6F (yellow) for attention items
    
    {{ ctx.output_format }}
  "#
}

// Function for generating highlights for multiple documents at once
function GenerateMultiDocumentHighlights(
  documents: string[],
  filenames: string[],
  claim_reference: string,
  email_subject: string,
  email_body: string,
  level01_analysis: string,
  level02_analysis: string,
  level03_analysis: string,
  level04_analysis: string
) -> DocumentExplainability[] {
  client "openai/gpt-4o"
  prompt #"
    You are analyzing multiple documents for claim {{ claim_reference }}.
    
    CONTEXT:
    - Email Subject: {{ email_subject }}
    - Email Body: {{ email_body }}
    
    ANALYSIS LEVELS:
    Level 1: {{ level01_analysis }}
    Level 2: {{ level02_analysis }}
    Level 3: {{ level03_analysis }}
    Level 4: {{ level04_analysis }}
    
    DOCUMENTS TO ANALYZE:
    {% for doc, filename in zip(documents, filenames) %}
    
    DOCUMENT: {{ filename }}
    {{ doc }}
    
    ---
    {% endfor %}
    
    Generate highlights for each document that show how they collectively support the AI's analysis decisions.
    Maintain consistent color coding across all documents.
    
    {{ ctx.output_format }}
  "#
} 