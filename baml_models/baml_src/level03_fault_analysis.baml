// ================================================================================================
// ZURICH LEVEL 03 FAULT DETERMINATION - AI-ENHANCED RULE APPLICATION
// ================================================================================================
// Purpose: AI extracts liability factors from comprehensive data sources, then applies Canadian law
// Hybrid Approach: AI Analysis → Structured Extraction → Rule-Based Canadian Fault Determination
// Data Sources: Level 1, Level 2, Email Content, SparkNLP insights, OCR text
// ================================================================================================

// ================================================================================================
// LEVEL 03 FAULT ANALYSIS INPUT
// ================================================================================================

class Level03AnalysisInput {
  claimReference string @description("Unique claim identifier")
  province string @description("Canadian province where incident occurred")
  
  // Comprehensive data sources
  level01Analysis string @description("Complete Level 1 analysis results as JSON string")
  level02Coverage string @description("Level 2 coverage analysis as JSON string")
  emailContent string[] @description("Original email content and communications")
  sparkNlpInsights string @description("SparkNLP extracted entities and insights as JSON")
  ocrTexts string[] @description("OCR extracted text from documents")
  attachmentDetails string[] @description("Document names and descriptions")
}

// ================================================================================================
// FAULT ANALYSIS OUTPUT SCHEMAS
// ================================================================================================

class LiabilityFactorExtraction {
  accidentClassification AccidentClassification @description("Precise accident type and circumstances")
  negligenceFactors NegligenceAnalysis @description("Detailed negligence factor analysis")
  contributoryFactors ContributoryAnalysis @description("Contributory negligence assessment")
  evidenceQuality EvidenceQuality @description("Quality and reliability of evidence")
  circumstanceDetails CircumstanceDetails @description("Detailed incident circumstances")
  structuredForRules StructuredCircumstances @description("Data organized for Canadian rule application")
  injuryImpactAnalysis InjuryImpactAnalysis? @description("Analysis of injury severity impact on liability assessment")
  faultGuidance FaultGuidance @description("AI guidance for fault percentage distribution")
}

class AccidentClassification {
  primaryType "auto_collision" | "slip_fall" | "occupiers_liability" | "general_liability" | "product_liability" @description("Primary accident category")
  specificSubtype string @description("Specific subtype (e.g., wet_floor, rear_end_standard, etc.)")
  incidentComplexity "simple" | "moderate" | "complex" | "multi_party" @description("Complexity level for fault determination")
  applicableLaw string @description("Primary Canadian law/regulation that applies")
  certaintyLevel float @description("Confidence in accident classification (0.0-1.0)")
}

class NegligenceAnalysis {
  propertyOwnerFactors string[] @description("Property owner negligence factors identified")
  vehicleOperatorFactors string[] @description("Vehicle operator negligence factors") 
  thirdPartyFactors string[] @description("Third party negligence factors")
  institutionalFactors string[] @description("Institutional/corporate negligence")
  maintenanceFailures string[] @description("Specific maintenance failures identified")
  warningDeficiencies string[] @description("Missing or inadequate warnings")
  dutyOfCareBreaches string[] @description("Clear breaches of duty of care")
  statutoryViolations string[] @description("Violations of specific laws/regulations")
}

class ContributoryAnalysis {
  claimantFactors string[] @description("Claimant contributory factors")
  awarenessLevel "fully_aware" | "should_have_known" | "unaware" | "distracted" @description("Hazard awareness level")
  avoidabilityFactor "easily_avoidable" | "avoidable_with_care" | "difficult_to_avoid" | "unavoidable" @description("Whether incident was avoidable")
  behaviorFactors string[] @description("Claimant behavior contributing to incident")
  mitigatingCircumstances string[] @description("Circumstances reducing claimant fault")
  aggravatingCircumstances string[] @description("Circumstances increasing claimant fault")
  contributionPercentage float @description("Estimated contribution percentage (0.0-100.0)")
}

class EvidenceQuality {
  witnessStatements string[] @description("Quality of witness statements")
  documentaryEvidence string[] @description("Document and photo evidence quality")
  physicalEvidence string[] @description("Physical/forensic evidence available")
  expertOpinions string[] @description("Expert opinions and assessments")
  overallReliability float @description("Overall evidence reliability (0.0-1.0)")
  evidenceGaps string[] @description("Missing evidence that would be helpful")
}

class CircumstanceDetails {
  environmentalFactors EnvironmentalFactors @description("Environmental conditions")
  locationDetails string[] @description("Location-specific factors")
  timingFactors string[] @description("Time-related factors affecting incident")
  humanBehaviorFactors string[] @description("Human behavior factors")
  equipmentConditions string[] @description("Equipment/mechanical factors if applicable")
}

class EnvironmentalFactors {
  weatherConditions string @description("Weather at time of incident")
  lightingConditions string @description("Lighting conditions")
  surfaceConditions string @description("Surface/ground conditions")
  visibilityFactors string[] @description("Factors affecting visibility")
  crowdingLevel "empty" | "light" | "moderate" | "heavy" | "overcrowded" @description("Area crowding")
}

class StructuredCircumstances {
  // For Auto accidents
  trafficViolations string[] @description("Specific traffic violations")
  rightOfWayFactors string[] @description("Right-of-way considerations")
  vehicleConditions string[] @description("Vehicle condition factors")
  roadConditions string[] @description("Road and traffic conditions")
  
  // For Premises liability
  visitorStatus "invitee" | "licensee" | "trespasser" | "employee" @description("Legal visitor status")
  locationType "commercial" | "residential" | "industrial" | "public" | "recreational" @description("Property type")
  hazardType string @description("Specific hazard classification")
  warningsPosted bool @description("Whether adequate warnings were posted")
  mitigationEfforts string[] @description("Property owner mitigation efforts")
  
  // For General liability
  dutyOfCareLevel "low" | "standard" | "high" | "statutory" @description("Applicable duty of care standard")
  breachFactors string[] @description("Specific duty breaches")
  causationChain string[] @description("Causal sequence of events")
  
  // Common factors
  contributingFactors string[] @description("All contributing factors")
  mitigatingFactors string[] @description("All mitigating factors")
  atFaultParties string[] @description("Identified at-fault parties")
}

class FaultGuidance {
  suggestedPrimaryFault float @description("Suggested primary party fault percentage")
  suggestedSecondaryFault float @description("Suggested secondary party fault percentage")  
  faultRationale string @description("Reasoning for fault distribution")
  uncertaintyAreas string[] @description("Areas requiring human review")
  comparisonCases string[] @description("Similar cases for reference")
  recommendedRuleSet string @description("Which Canadian rule set to apply")
  confidenceInGuidance float @description("Confidence in fault guidance (0.0-1.0)")
}

class InjuryImpactAnalysis {
  injurySeverityFactor "minor" | "moderate" | "severe" | "catastrophic" @description("Overall injury severity affecting liability")
  medicalCausationCertainty float @description("Certainty that injuries caused by incident (0.0-1.0)")
  treatmentComplexity "simple" | "moderate" | "complex" | "ongoing_care" @description("Complexity of required treatment")
  functionalImpact "minimal" | "moderate" | "significant" | "life_altering" @description("Impact on claimant's daily function")
  workImpact "no_time_off" | "short_absence" | "extended_absence" | "permanent_disability" @description("Impact on ability to work")
  recoveryPrognosis "full_recovery" | "partial_recovery" | "ongoing_limitations" | "permanent_impairment" @description("Expected recovery outcome")
  preExistingFactors string[] @description("Pre-existing conditions affecting injury assessment")
  medicalDocumentationQuality float @description("Quality of medical evidence (0.0-1.0)")
  liabilityAggravationFactor float @description("How injury severity affects liability assessment (0.5-2.0)")
}

// ================================================================================================
// LEVEL 03 FAULT DETERMINATION FUNCTION
// ================================================================================================

function ExtractLevel03FaultFactors(input: Level03AnalysisInput) -> LiabilityFactorExtraction {
  client "openai/gpt-4o"
  prompt #"
    You are a Canadian liability insurance expert analyzing comprehensive claim data to extract precise fault determination factors.
    
    Your task: Analyze ALL available data sources and extract structured liability factors for Canadian rule-based fault determination.
    
    Data Sources Available:
    - Claim Reference: {{ input.claimReference }}
    - Province: {{ input.province }}
    - Level 1 Analysis: {{ input.level01Analysis }}
    - Level 2 Coverage: {{ input.level02Coverage }}
    - Email Content: {{ input.emailContent }}
    - SparkNLP Insights: {{ input.sparkNlpInsights }}
    - OCR Text: {{ input.ocrTexts }}
    - Attachments: {{ input.attachmentDetails }}
    
    CRITICAL REQUIREMENTS:
    1. COMPREHENSIVE ANALYSIS: Consider ALL data sources thoroughly
    2. CANADIAN LAW FOCUS: Apply knowledge of provincial regulations and common law
    3. PRECISE EXTRACTION: Extract specific, factual details that impact liability
    4. EVIDENCE-BASED: Only include factors supported by available evidence
    5. RULE-READY OUTPUT: Structure data for Canadian fault determination rules
    
    KEY ANALYSIS AREAS:
    
    A) ACCIDENT CLASSIFICATION:
    - Determine precise accident type from all evidence
    - Identify applicable Canadian law/regulation
    - Assess incident complexity for fault determination
    
    B) NEGLIGENCE ANALYSIS:
    - Extract specific negligence factors for each party
    - Identify duty of care breaches
    - Assess maintenance failures, warning deficiencies
    - Document statutory/regulatory violations
    
    C) CONTRIBUTORY FACTORS:
    - Assess claimant awareness and avoidability
    - Identify behavior and experience factors
    - Determine mitigating vs aggravating circumstances
    
    D) EVIDENCE QUALITY:
    - Evaluate witness, documentary, physical evidence
    - Assess overall reliability for fault determination
    - Identify evidence gaps
    
    E) ENVIRONMENTAL CIRCUMSTANCES:
    - Extract weather, lighting, surface conditions
    - Identify visibility and crowding factors
    - Assess timing and location factors
    
    F) STRUCTURED FOR CANADIAN RULES:
    - Organize data for immediate application to provincial fault rules
    - Determine visitor status for premises liability
    - Identify traffic violations for auto accidents
    - Structure duty of care factors for general liability
    
    G) INJURY IMPACT ANALYSIS (if injuries reported):
    - Assess injury severity and impact on liability assessment
    - Evaluate medical causation certainty
    - Consider how injury severity affects fault allocation
    - Assess impact of treatment complexity and recovery prognosis
    - Factor pre-existing conditions into liability analysis
    - Evaluate quality of medical documentation
    
    H) FAULT GUIDANCE:
    - Provide preliminary fault percentage recommendations
    - Explain reasoning based on Canadian legal principles
    - Identify areas requiring expert human review
    - Reference similar cases where applicable
    - Factor injury severity into fault percentage recommendations
    
    SPECIAL ATTENTION TO:
    - SparkNLP entity extraction for precise location/person details
    - Email communications revealing negligence or liability factors
    - OCR text from incident reports, photos, official documents
    - Level 2 risk assessment insights and coverage implications
    - Canadian jurisdictional requirements and provincial variations
    
    FAULT FACTOR PRIORITIES (highest to lowest weight):
    1. Statutory/regulatory violations 
    2. Clear duty of care breaches 
    3. Industry standard violations
    4. Maintenance and warning failures
    5. Contributory negligence factors
    6. Environmental and circumstantial factors
    
    CANADIAN PROVINCIAL CONSIDERATIONS:
    - Ontario: Regulation 668/90, Occupiers' Liability Act
    - BC: ICBC rules, Motor Vehicle Act, Occupiers Liability Act
    - Alberta: Traffic Safety Act, Occupiers' Liability Act
    - Quebec: Civil Code provisions, no-fault considerations
    - Other provinces: Apply relevant motor vehicle acts and common law
    
    Return comprehensive extraction ready for Canadian fault determination rule application.
    
    {{ ctx.output_format }}
  "#
} 