# ZURICH WORKFLOW APP - COMMON DEPENDENCIES
# =====================================================
# This file contains all dependencies for all APIs and services

# Core FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic[email]==2.5.0

# BAML client dependencies  
baml-py==0.90.2
httpx>=0.25.0

# HTTP client and requests
requests>=2.31.0
aiohttp>=3.8.0

# Database drivers
psycopg2-binary>=2.9.0  # PostgreSQL
asyncpg>=0.28.0  # Async PostgreSQL
supabase>=1.0.0  # Supabase client

# Email processing
imapclient>=2.3.0
email-validator>=2.0.0

# Data processing and validation
pandas>=2.0.0
numpy>=1.24.0
python-dateutil>=2.8.0

# Authentication and security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6

# Environment and configuration
python-dotenv>=1.0.0
pydantic-settings>=2.0.0

# Logging and monitoring
structlog>=23.0.0
sentry-sdk[fastapi]>=1.30.0

# Testing framework
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.11.0
httpx>=0.25.0  # For testing async clients

# Development tools
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.5.0

# Production deployment
gunicorn>=21.2.0
redis>=4.6.0  # For caching/sessions

# File processing and utilities
python-magic>=0.4.27
Pillow>=10.0.0  # Image processing
PyPDF2>=3.0.0  # PDF processing

# API documentation
swagger-ui-bundle>=0.1.0

# CanLII legal research integration
# call-canlii>=0.1.0  # Placeholder for future CanLII integration

# Optional: Machine Learning libraries
# scikit-learn>=1.3.0
# transformers>=4.30.0
# torch>=2.0.0 